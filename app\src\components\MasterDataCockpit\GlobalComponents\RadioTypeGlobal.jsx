import { updateModuleFieldData } from "@app/profitCenterTabsSlice";
import { colors } from "@constant/colors";
import { Checkbox, Grid, Typography } from "@mui/material";
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { updateModuleFieldDataCC } from "@app/costCenterTabsSlice";
import { updateModuleFieldDataGL } from "@app/generalLedgerTabSlice";
import { CHANGE_LOG_STATUSES, MODULE } from "@constant/enum";
import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";
import { updateModuleFieldDataIO } from "@InternalOrder/slice/internalOrderSlice";



const RadioTypeGlobal = (props) => {
  const { uniqueId, field, disabled, handleChange, module } = props;
  const dispatch = useDispatch();
  const { updateChangeLogGl } = useChangeLogUpdateGl();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);
  console.log(initialPayload,"initialPayload")
  const rowsBodyData = useSelector(
        (state) => state.generalLedger.payload?.rowsBodyData || {}
      );
      let requestStatus=rowsBodyData?.[uniqueId]?.["Torequestheaderdata"]?.["RequestStatus"]
      console.log(requestStatus,"requestStatusdaaya")
  const valueFromPayloadPC = useSelector((state) => state.profitCenter.payload);
  const valueFromPayloadCC = useSelector((state) => state.costCenter.payload);
  const valueFromPayloadGL = useSelector((state) => state.generalLedger.payload || {});
  const valueFromPayloadIO = useSelector((state) => state.internalOrder.IOpayloadData || {});

  const valueToCheck =
  module === "CostCenter" ?
  valueFromPayloadCC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ??
  field?.value : module === "GeneralLedger"?
  valueFromPayloadGL?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ??
  valueFromPayloadGL?.requestHeaderData?.[field?.jsonName] ??
  field?.value ?? "" : module === MODULE.IO ?
  valueFromPayloadIO?.rowsBodyData?.[uniqueId]?.payload?.[field?.jsonName] ?? valueFromPayloadIO?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? "" :
  valueFromPayloadPC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ??
  field?.value ??
  false;
  const initialFieldValue = valueToCheck === "X" || valueToCheck === true || valueToCheck === "TRUE" ? true : false;
  const [localValue, setLocalValue] = useState(initialFieldValue);


  console.log( requestId && !CHANGE_LOG_STATUSES.includes(requestStatus),CHANGE_LOG_STATUSES,"fefefe")
  useEffect(() => {
    setLocalValue(initialFieldValue);
    if(initialFieldValue) {
      dispatch(
        updateModuleFieldData({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: initialFieldValue,
          viewID: field?.viewName,
        })
      );
    }
  }, [initialFieldValue]);


const handleCheckBoxChange = (e) => {
    const updatedValue = e.target.checked;
    setLocalValue(updatedValue);
    if(module === "CostCenter"){
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
    }
    else if(module === "GeneralLedger"){
      //alert("coming")
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
       {
         updateChangeLogGl({
          uniqueId: uniqueId || "",
          viewName: props?.field?.viewName,
          plantData: '',
          fieldName: props?.field?.fieldName,
          jsonName: props?.field?.jsonName,
          currentValue: updatedValue,
          requestId: initialPayload?.RequestId,
          childRequestId:requestId
        });
      }
    }
    else if(module === MODULE.IO){
      dispatch(
        updateModuleFieldDataIO({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
    }
    else{
    dispatch(
        updateModuleFieldData({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
    }
  };

  return (
    <Grid item md={2}>
        <>
          <Typography
            variant="body2"
            color={colors.secondary.grey}
            sx={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              maxWidth: "100%",
              
            }}
            title={field.fieldName}
          >
            {field.fieldName}
            {field.visibility === "Required" ||
            field.visibility === "0" ? (
              <span style={{ color: colors.error.darkRed }}>*</span>
            ) : (
              ""
            )}
          </Typography>
          <Checkbox
            sx={{
              padding: 0,
              marginTop:"5px",
              "&.Mui-disabled": {
                color: colors.hover.light,
              },
              "&.Mui-disabled.Mui-checked": {
                color: colors.hover.light,
              },
            }}
            disabled={disabled}
            checked={localValue}
            onChange={handleCheckBoxChange}
          />
        </>
    </Grid>
  );
};

export default RadioTypeGlobal;
