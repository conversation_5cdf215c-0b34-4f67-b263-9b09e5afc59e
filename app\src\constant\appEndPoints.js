export const APP_END_POINTS = {
  DASHBOARD_FILES:{
    All_MATERIALS_BY_PLANT_AND_SALES:'AllMaterialsByPlantAndSales.xlsx',
    DRAFT_STATUS:'AllRequestsInDraft.xlsx',
    MATERIAL_LIST:'AllCreatedMaterials.xlsx',
    PRICING_GROUP:'MatPriceGrp.xlsx',
    MISSING_HTS:'MissingHTS_codeReport.xlsx',
    REQUEST_VS_OBJECT_TYPE: 'RequestCount_Vs_ObjectType.xlsx',
    AVG_REQUEST_ID_LIFE_CYCLE: 'AvgRequestLifecycle_Hours_Vs_ObjectType.xlsx',
    REJECTED_REQUESTS_PERCENTAGE: 'RejectedRequests_Percentage_Vs_ObjectType.xlsx',
    NOT_SUCCESSFULLY_COMPLETED_REQUESTS: 'UnsuccessfulRequests_Percentage_Vs_ObjectType.xlsx',
    SLA_BREACHED_REQUESTS: 'SLABreachedRequests_Count_Vs_ObjectType.xlsx',
    NO_OF_REQUESTS_REJECTED: 'RejectedCancelledRequests_Vs_Users.xlsx',
    NO_OF_REQUESTS_PENDING: 'PendingRequests_Vs_Approvers_MDM.xlsx',
    AVG_MDM_TASK_COMPLETION_TIME: 'AvgMDMTaskCompletionTime_Vs_ObjectType.xlsx',
    AVG_APPROVER_TASK_COMPLETION_TIME: 'AvgApproverTaskCompletionTime_Vs_ObjectType.xlsx',
    NO_OF_REQUESTS_REJECTED_VS_REQUESTORS: 'RejectedRequests_Vs_Requestors.xlsx',
    APPROVERS_WHO_HAVE_MISSED_SLA: 'ApproversMissedSLA_Count.xlsx',
    APPROVERS_AVG_APPROVAL_TIME: 'ApproversAvgApprovalTime_Hours.xlsx',
    REQUESTS_COMPLETED_BY_MDM: 'RequestsCompletedByMDM.xlsx',
    OPEN_REQUESTS_VS_TYPE: 'OpenRequests_Count_Vs_ObjectType.xlsx',
    REQUESTOR_APPROVER_COUNTS: 'Requestor_Approver_Counts_Vs_ObjectType.xlsx',

    OPEN_WORKFLOWS_REPORT: 'OpenWorkflowsReport.xlsx',
    TEMPORARY_BLOCK_REQUESTS_REPORT: 'TemporaryBlockRequestsReport.xlsx',
    SCHEDULED_REQUESTS_REPORT: 'ScheduledRequestsReport.xlsx',
    PDF_SCHEDULER_REPORT: 'PDFSchedulerReport.xlsx',
    REQUESTS_ON_HOLD_WITH_REQUESTORS: 'RequestsOnHoldWithRequestors.xlsx',
    ALL_ERROR_REQUESTS: 'AllErrorRequests.xlsx',
    OBJECT_NUMBERS_REPORT: 'ObjectNumbersReport.xlsx',
    SYNDICATION_ATTACHMENT_FAILED_REPORT: 'Syndication_AttachmentFailedReport.xlsx'
  },

  MY_TASK: "/workspace/MyTasks",
  REQUEST_BENCH: "/requestBench",
  REQUEST_HISTORY: "/requestBench/RequestHistory",
  MASTER_DATA: "/masterDataCockpit/materialMaster/material",
  DATA_CHECK: "/dataCheck/data",
  BOM: "/masterDataCockpit/billOfMaterial",
  CREATE_BOM: '/requestBench/bomCreateRequest',
  INTERNAL_ORDER: '/masterDataCockpit/internalOrder',
  MASTER_DATA_PC: "/masterDataCockpit/profitCenter",
  MASTER_DATA_GL: "/masterDataCockpit/generalLedger",
  MASTER_DATA_CC: "/masterDataCockpit/costCenter",
  PROFIT_CENTER: "/masterDataCockpit/profitCenter",
  CREATE_PCG:"requestBench/ProfitCenterGroupRequestTab",
  CREATE_CCG:"requestBench/CostCenterGroupRequestTab",
  BUSINESS_RULES:{
    AUTHORING:'/configCockpit/businessRules/authoring',
  },
   IWA_USER_MANAGEMENT: {
    // User Management Routes
    USERS_SUMMARY: '/configCockpit/userManagement/UsersSummary',
    CREATE_USERS: '/configCockpit/userManagement/CreateUsers',
    EDIT_USER: '/configCockpit/userManagement/EditUser',
    VIEW_USER: '/configCockpit/userManagement/ViewUser',
    QUICK_CREATE_USER: '/configCockpit/userManagement/QuickCreateUser',
    // Role Management Routes
    ROLES_SUMMARY: '/configCockpit/userManagement/RolesSummary',
    CREATE_ROLE: '/configCockpit/userManagement/CreateRole',
    VIEW_AND_EDIT_ROLE: '/configCockpit/userManagement/ViewAndEditRole',
    // Group Management Routes
    GROUPS_SUMMARY: '/configCockpit/userManagement/GroupsSummary',
    CREATE_GROUP: '/configCockpit/userManagement/CreateGroup',
    EDIT_GROUP: '/configCockpit/userManagement/EditGroup',
    VIEW_GROUP: '/configCockpit/userManagement/ViewGroup',
  },
};
