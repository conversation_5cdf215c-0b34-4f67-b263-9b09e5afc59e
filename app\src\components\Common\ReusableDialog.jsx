  import {
    CheckCircleOutline,
    Close,
    CloseFullscreen,
    DangerousOutlined,
    InfoOutlined,
    WarningAmberOutlined,
  } from "@mui/icons-material";
  import {
    Box,
    Button,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    FormControlLabel,
    FormHelperText,
    FormLabel,
    Grid,
    IconButton,
    InputLabel,
    MenuItem,
    Radio,
    RadioGroup,
    Select,
    TextField,
    Typography,
  } from "@mui/material";
  import { Stack } from "@mui/system";
  import React, { useState } from "react";
  import CloseIcon from "@mui/icons-material/Close";
  import ReusableSnackBar from "./ReusableSnackBar";
  import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
  import { doAjax } from "./fetchService";
  import { destination_ServiceRequest } from "../../destinationVariables";
  import ReusableDataTable from "./ReusableTable"
  import { useDispatch } from "react-redux";
  import { setDynamicKeyValue } from "@app/payloadSlice"
import { colors } from "@constant/colors";
import { WORK_FLOW_LEVELS } from "@constant/enum";
  // import AutoAwesomeOutlinedIcon from '@mui/icons-material/AutoAwesomeOutlined';

  function ReusableDialog({
    poId,
    roleName,
    SR_REDIRECT,
    handleOk = null,
    dialogPlaceholder,
    dialogState,
    setDialogState,
    openReusableDialog,
    closeReusableDialog,
    handleCancle,
    dialogTitle,
    dialogMessage,
    showInputText,
    inputText,
    setInputText=()=>{},
    handleDialogConfirm=()=>{},
    handleDialogReject=()=>{},
    showCancelButton,
    dialogCancelText,
    showOkButton,
    dialogOkText,
    dialogSeverity,
    requestId,
    commentArtifactType,
    user,
    loadingStatus,
    handleExtraButton,
    handleExtraText,
    showExtraButton,
    onClose,
    alertMsg,
    errorMessage,
    mandatoryTextInput,
    remarksError,
    isTable=false,
    tableColumns=[],
    tableRows=[],
    isShowWFLevel=false,
    selectedLevel='',
    handleLevelChange,
    workFlowLevels,
    setSyndicationType,
    syndicationType,
    isSyndicationBtn,
    isMassSyndication,
    blurLoading = false,
  }) {
    const [openSnackBar, setOpenSnackBar] = useState(false);
    const [isMaxLength, setIsMaxLength] = useState(false);
    const maxLength = 200;
    const dispatch = useDispatch();

    const handleSnackBarClick = () => {
      setOpenSnackBar(true);
    };

    const handleSnackBarClose = () => {
      setOpenSnackBar(false);
    };
    const handleCommentClick = () => {
      if (inputText) {
        const formData = new FormData();
        var comments = JSON.stringify({
          requestId: requestId,
          comment: inputText,
          commentArtifactType: commentArtifactType,
          createdBy: user,
          roleName: roleName,
          poNumber: poId
        });
        formData.append("comments", comments);
        let hSuccess = (data) => {
          // setInputText("");
        }
        let hError = (error) => {
          console.log(error);
        }
        doAjax(`/${destination_ServiceRequest}/comment/saveCommentWithoutFile`, 'postformdata', hSuccess, hError, formData)
      }
    };

    const handleRemarks = (e, value) => {
      const newValue = e.target.value;
      setIsMaxLength(newValue.length >= maxLength);
      
      if (newValue.length > 0 && newValue[0] === " ") {
        setInputText(newValue.trimStart());
        dispatch(
          setDynamicKeyValue({
            keyName: "Comments",
            data: newValue.trimStart()
          })
        );
      } else {
        let remarksUpperCase = newValue;
        setInputText(remarksUpperCase);
        dispatch(
          setDynamicKeyValue({
            keyName: "Comments",
            data: remarksUpperCase
          })
        );
      }
    };

    return (
      <>
        <Dialog
          hideBackdrop={false}
          elevation={2}
          PaperProps={{
            sx: { boxShadow: "none",marginBottom: "8px", borderRadius: "8px" },
          }}
          open={dialogState}
          onClose={onClose ? onClose : closeReusableDialog}
        >
          <Grid
            container
            sx={{ display: "flex", justifyContent: "space-between",backgroundColor: "#EAE9FF", }}
          >
            <Grid item>
              <DialogTitle
                id="alert-dialog-title"
                sx={{

                justifyContent: "space-between",
                // alignItems: "center",
                height: "max-content",
                // padding: ".5rem",
                paddingLeft: "1rem",
                display: "flex",
                // marginBottom: "8px"
                alignItems: "flex-start",
                }}
              >
                {dialogSeverity && (
                  <span style={{ display: "flex", alignItems: "center" }}>
                    {dialogSeverity === "success" && (
                      <CheckCircleOutline sx={{ color: "green" }} />
                    )}
                    {dialogSeverity === "warning" && (
                      <WarningAmberOutlined sx={{ color: "orange" }} />
                    )}
                    {dialogSeverity === "redirectionWarning" && (
                      <WarningAmberOutlined sx={{ color: "orange" }} />
                    )}
                    {dialogSeverity === "danger" && (
                      <CancelOutlinedIcon sx={{ color: "red" }} />
                    )}
                    {dialogSeverity === "info" && (
                      <InfoOutlined sx={{ color: "grey" }} />
                    )}
                    &nbsp;&nbsp;
                  </span>
                )}
                {/* <AutoAwesomeOutlinedIcon sx={{color:"#3B30C8", marginRight:"10px"}}/> */}
                {/* {dialogTitle} */}
                <Grid sx={{
                  display: "flex",
                  flexDirection: "column",
                }}>
                {dialogTitle?.split('\n')?.map((line, index) => (
                  <>
                  <Typography sx={{ fontWeight: 700 }}  key={index}>
                    {line}
                    
                  </Typography >
                  
                  </>
                ))}
                  
                </Grid>
                {mandatoryTextInput == true ? <span style={{ color: "red" }}>*</span>:''}
                
              </DialogTitle>
            </Grid>
            {/* <Grid item sx={{ padding: "12px" }}>
              {(dialogSeverity !== "danger" || showInputText) && (
                <IconButton
                  onClick={(e) => {
                    e.stopPropagation();
                    closeReusableDialog();
                  }}
                >
                  <CloseIcon />
                </IconButton>
              )}
            </Grid> */}
          </Grid>

          <DialogContent>
            <Stack>
              <Grid container>
                <Grid
                  item
                  md={12}
                  sx={{
                  
                    textAlign: "left",
                  }}
                >
                  <Typography className={loadingStatus ? "loading" : ""}>
                    {dialogMessage}
                  </Typography>
                  {errorMessage &&
                  <Typography className={loadingStatus ? "loading" : ""} sx={{fontSize: "12px", paddingTop:"0.5rem"}}>
                    Error Message: {errorMessage}
                  </Typography>}
                </Grid>
              </Grid>
              <Box sx={{ minWidth: 400 }}>
                {showInputText && (
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{
                        backgroundColor: "#F5F5F5",
                        '& .MuiOutlinedInput-root': {
                          '& fieldset': {
                            borderColor: isMaxLength ? colors?.error?.dark : 'rgba(0, 0, 0, 0.23)',
                          },
                          '&:hover fieldset': {
                            borderColor: isMaxLength ? colors?.error?.dark : 'rgba(0, 0, 0, 0.23)',
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: isMaxLength ? colors?.error?.dark : colors?.primary?.dark,
                          },
                        },
                      }}
                      value={inputText}
                      onChange={handleRemarks}
                      inputProps={{ maxLength: maxLength }}
                      multiline
                      placeholder={`${dialogTitle}...`}
                    />
                    <FormHelperText
                      sx={{ 
                        textAlign: 'right',
                        color: isMaxLength ? colors?.error?.dark : 'rgba(0, 0, 0, 0.6)',
                        marginTop: '4px'
                      }}
                    >
                      {`${inputText?.length || 0}/${maxLength}`}
                    </FormHelperText>
                  </FormControl>
                )}
                {remarksError === true && inputText?.length <= 0 ? 
                  <Grid>
                    <Typography><span style={{ color: "red" }}>*Please Enter Remarks</span></Typography>
                  </Grid> : ''
                }
              </Box>
              {isShowWFLevel && (
                <FormControl fullWidth margin="normal">
                  <InputLabel>Workflow Level</InputLabel>
                  <Select value={selectedLevel} onChange={handleLevelChange} label="Workflow Level">
                    {workFlowLevels?.map(level => (
                      <MenuItem key={level} value={level}>{WORK_FLOW_LEVELS[level]}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
              {(isSyndicationBtn && isMassSyndication) && <FormControl component="fieldset" sx={{ mb: 2 }}>
                <FormLabel component="legend">Syndication Type</FormLabel>
                <RadioGroup
                  row
                  value={syndicationType}
                  onChange={e => setSyndicationType(e.target.value)}
                  name="syndication-type"
                >
                  <FormControlLabel
                    value="immediate"
                    control={<Radio color="primary" />}
                    label="Immediate Syndication"
                  />
                  <FormControlLabel
                    value="scheduleSyndication"
                    control={<Radio color="primary" />}
                    label="Schedule Syndication"
                  />
                </RadioGroup>
              </FormControl>}
              {isTable && (
                <Box>
                  <ReusableDataTable 
                    columns={tableColumns} 
                    rows={tableRows} 
                    getRowIdValue={"id"}
                    tempheight='40vh'
                    rowsPerPageOptions={[100]}
                  />
                </Box>
              )}
              
            </Stack>
          </DialogContent>

          <DialogActions
            sx={{
              padding: "1rem 1.5rem",
            }}
          >
            {(dialogSeverity === "warning" || showCancelButton) && (
              <Button
                variant="outlined"
                color="error"
                sx={{
                  height: 40,
                  minWidth: "4rem",
                  textTransform: "none",
                  borderColor: "#cc3300",
                  // backgroundColor:"#cc3300",
                  // color: "white",
                }}
                onClick={(e) => { e.stopPropagation();
                  handleDialogReject();
                  closeReusableDialog();

                }}
              >
                Cancel
              </Button>
            )}
            {(dialogSeverity === "redirectionWarning") && (
              <Button
                variant="outlined"
                color="error"
                sx={{
                  height: 40,
                  minWidth: "4rem",
                  textTransform: "none",
                  borderColor: "#cc3300",
                  // backgroundColor:"#cc3300",
                  // color: "white",
                }}
                onClick={(e) => { e.stopPropagation();
                  handleDialogReject();
                  closeReusableDialog();

                }}
              >
                No
              </Button>
            )}
            {showOkButton && (
              <Button
                variant="contained"
                disabled={blurLoading}
                style={{
                  height: 40,
                  minWidth: "4rem",
                  backgroundColor: "#3B30C8",
                  textTransform: "none",
                }}
                onClick={() => {
                  handleOk ? handleOk() : closeReusableDialog();
                  handleDialogConfirm();
                  // handleCommentClick();
                  handleDialogReject();
                  handleSnackBarClick();
                }}
              >
                {dialogOkText}
              </Button>
            )}
            {/* {(dialogSeverity === "warning" || dialogSeverity === "danger") && ( */}
              <Button
                variant="contained"
                disabled={blurLoading}
                style={{
                  height: 40,
                  minWidth: "4rem",
                  backgroundColor: "#3B30C8",
                  textTransform: "none",
                }}
                onClick={() => {
                // handleOk ? handleOk() : closeReusableDialog();
                  handleOk ? handleOk() : '';
                  handleDialogConfirm();
                  // handleCommentClick();
                  handleDialogReject();
                  // handleSnackBarClick();
                }}
              >
                {dialogOkText}
              </Button>
            {/* )} */}
            {showExtraButton && (
              <Button
                variant="contained"
                disabled={blurLoading}
                style={{
                  height: 40,
                  minWidth: "4rem",
                  backgroundColor: "#3B30C8",
                  textTransform: "none",
                }}
                onClick={handleExtraButton}
              >
                {handleExtraText}
              </Button>
            )}
          </DialogActions>
        </Dialog>
      </>
    );
  }

  export default ReusableDialog;
