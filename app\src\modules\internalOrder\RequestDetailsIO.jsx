import { Box, Button, Checkbox, TextField, Typography, IconButton, <PERSON>lt<PERSON>, Tabs, Tab, Paper } from "@mui/material";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useState, useEffect } from "react";
import useLang from "@hooks/useLang";
import { DataGrid } from "@mui/x-data-grid";
import { v4 as uuidv4 } from "uuid";
import { DeleteOutlineOutlined as DeleteOutlineOutlinedIcon, CloseFullscreen as CloseFullscreenIcon, CropFree as CropFreeIcon, FormatColorResetRounded } from "@mui/icons-material";
import { useSelector, useDispatch } from "react-redux";
import { updateModuleFieldDataIO, updateTableColumnDataIO, removeRowDataIO } from "./slice/InternalOrderSlice";
import useInternalOrderFieldConfig from "@hooks/useInternalOrderFieldConfig";
import GenericTabsGlobal from "@components/MasterDataCockpit/GenericTabsGlobal";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import BottomNavGlobal from "@components/RequestBench/RequestPages/BottomNavGlobal";
import { MODULE } from "@constant/enum";

const RequestDetailsIO = () => {
  const { t } = useLang();
  const dispatch = useDispatch();
  const { fetchInternalOrderFieldConfig, fieldConfigByOrderType } = useInternalOrderFieldConfig();
  const { getButtonsDisplayGlobal } = useButtonDTConfig();

  // State management
  const [rows, setRows] = useState([]);
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [page, setPage] = useState(0);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedOrderType, setSelectedOrderType] = useState(null); // No default order type
  const [activeRowId, setActiveRowId] = useState(null); // Track which row is being edited

  const IODropdownData = useSelector((state) => state.internalOrder.dropDownDataIO);
  const orderTypeOptions = [{ code: "TUK1", desc: "TUK1" }]; // Dummy dropdown option
  const taskData = useSelector((state) => state.userManagement.taskData);
  const filteredButtons = useSelector((state) => state.userManagement.filteredButtons);

  const internalOrderTabs = (fieldConfigByOrderType[selectedOrderType]?.allTabsData || []).filter((tab) => tab.tab.toLowerCase() !== "header" && tab.tab.toLowerCase() !== "request header");

  // Call button DT for Internal Order
  useEffect(() => {
    if (taskData?.ATTRIBUTE_1) {
      getButtonsDisplayGlobal("Internal Order", "MDG_DYN_BTN_DT", "v3");
    }
  }, [taskData, getButtonsDisplayGlobal]);

  // Button handlers for BottomNavGlobal
  const handleSaveAsDraft = () => {
    console.log("Save as Draft clicked for Internal Order");
    // TODO: Implement save as draft logic
  };

  const handleSubmitForReview = () => {
    console.log("Submit for Review clicked for Internal Order");
    // TODO: Implement submit for review logic
  };

  const handleSubmitForApprove = () => {
    console.log("Submit for Approve clicked for Internal Order");
    // TODO: Implement submit for approve logic
  };

  const handleSendBack = () => {
    console.log("Send Back clicked for Internal Order");
    // TODO: Implement send back logic
  };

  const handleCorrection = () => {
    console.log("Correction clicked for Internal Order");
    // TODO: Implement correction logic
  };

  const handleRejectAndCancel = () => {
    console.log("Reject and Cancel clicked for Internal Order");
    // TODO: Implement reject and cancel logic
  };

  const handleValidateAndSyndicate = () => {
    console.log("Validate and Syndicate clicked for Internal Order");
    // TODO: Implement validate and syndicate logic
  };

  const validateAllRows = () => {
    console.log("Validate all rows for Internal Order");
    // TODO: Implement validation logic
    return true;
  };

  const handleAddRow = () => {
    const id = uuidv4();
    const newRow = {
      id,
      included: true,
      internalOrder: "",
      controllingArea: "",
      orderType: "",
      description: "",
      compCode: "",
    };

    // Add to local state
    setRows([...rows, newRow]);

    // Initialize table column data in Redux (not in payload)
    Object.entries(newRow).forEach(([fieldName, fieldValue]) => {
      if (fieldName !== "id") {
        // Don't store the ID as a field
        dispatch(
          updateTableColumnDataIO({
            uniqueId: id,
            keyName: fieldName,
            data: fieldValue,
          })
        );
      }
    });
  };
  const handleCellEdit = async ({ id, field, value }) => {
    // 1. Update local state
    const updatedRows = rows.map((row) => (row.id === id ? { ...row, [field]: value } : row));
    setRows(updatedRows);

    // 2. Store table column data in Redux (not in payload)
    dispatch(
      updateTableColumnDataIO({
        uniqueId: id,
        keyName: field,
        data: value,
      })
    );

    // 3. Handle Order Type change - use hook to get field configuration
    if (field === "orderType") {
      const orderTypeValue = value?.code || value;

      setSelectedOrderType(orderTypeValue);
      setActiveRowId(id); // Set the row that triggered the order type change

      try {
        fetchInternalOrderFieldConfig(orderTypeValue);
      } catch (error) {
        console.error("Failed to fetch field configuration for OrderType:", error);
      }
    }
  };

  const handleDeleteRow = (rowId) => {
    const updatedRows = rows.filter((row) => row.id !== rowId);
    setRows(updatedRows);

    dispatch(removeRowDataIO({ uniqueId: rowId }));
  };

  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  // Simple columns definition
  const columns = [
    {
      field: "included",
      headerName: t("Included"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      editable: false,
      renderCell: (params) => (
        <Checkbox
          checked={params.row.included}
          onChange={(e) =>
            handleCellEdit({
              id: params.row.id,
              field: "included",
              value: e.target.checked,
            })
          }
        />
      ),
    },
    {
      field: "internalOrder",
      headerName: t("Internal Order"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      editable: false,
      renderCell: (params) => {
        return (
          <TextField
            value={params.row.internalOrder || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "internalOrder",
                value: newValue,
              })
            }
            size="small"
            fullWidth
            variant="outlined"
            placeholder={t("Enter Internal Order")}
          />
        );
      },
    },
    {
      field: "controllingArea",
      headerName: t("Controlling Area"),
      flex: 0.7,
      editable: FormatColorResetRounded,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={IODropdownData?.controllingArea || []}
            value={params.row.controllingArea || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "controllingArea",
                value: newValue,
              })
            }
            placeholder={t("Select Controlling Area")}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "orderType",
      headerName: t("Order Type"),
      flex: 0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            // options={IODropdownData?.orderType || []}
            options={orderTypeOptions}
            value={params.row.orderType || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "orderType",
                value: newValue,
              })
            }
            placeholder={t("Select Order Type")}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "description",
      headerName: t("Description"),
      flex: 0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <TextField
          value={params.row.description || ""}
          onChange={(e) =>
            handleCellEdit({
              id: params.row.id,
              field: "description",
              value: e.target.value,
            })
          }
          size="small"
          fullWidth
          variant="outlined"
          placeholder={t("Enter Description")}
        />
      ),
    },
    {
      field: "compCode",
      headerName: t("Comp Code"),
      flex: 0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={IODropdownData?.compCode || []}
            value={params.row.compCode || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "compCode",
                value: newValue,
              })
            }
            placeholder={t("Select Company Code")}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "actions",
      headerName: t("Actions"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <Tooltip title={t("Delete Row")}>
          <IconButton onClick={() => handleDeleteRow(params.row.id)} color="error" size="small">
            <DeleteOutlineOutlinedIcon />
          </IconButton>
        </Tooltip>
      ),
    },
  ];

  return (
    <div>
      <div style={{ padding: "0", width: "100%", margin: "0", marginTop: "20px" }}>
        <Box
          sx={{
            position: isGridZoomed ? "fixed" : "relative",
            top: isGridZoomed ? 0 : "auto",
            left: isGridZoomed ? 0 : "auto",
            right: isGridZoomed ? 0 : "auto",
            bottom: isGridZoomed ? 0 : "auto",
            width: isGridZoomed ? "100vw" : "100%",
            height: isGridZoomed ? "100vh" : "auto",
            zIndex: isGridZoomed ? 1004 : undefined,
            backgroundColor: isGridZoomed ? "white" : "transparent",
            padding: isGridZoomed ? "20px" : "0",
            display: "flex",
            flexDirection: "column",
            boxShadow: isGridZoomed ? "0px 0px 15px rgba(0, 0, 0, 0.2)" : "none",
            transition: "all 0.3s ease",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "8px 16px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px 8px 0 0",
            }}
          >
            <Typography variant="h6">{t("Internal Order List")}</Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Button variant="contained" color="primary" size="small" onClick={handleAddRow}>
                + {t("Add")}
              </Button>
              <Tooltip title={isGridZoomed ? t("Exit Zoom") : t("Zoom In")} sx={{ zIndex: "1009" }}>
                <IconButton
                  onClick={toggleGridZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
            <div style={{ height: "100%" }}>
              <DataGrid
                rows={rows}
                columns={columns}
                pageSize={50}
                autoHeight={false}
                page={page}
                rowsPerPageOptions={[50]}
                onPageChange={(newPage) => handlePageChange(newPage)}
                pagination
                disableSelectionOnClick
                style={{
                  border: "1px solid #ccc",
                  borderRadius: "8px",
                  width: "100%",
                  height: isGridZoomed ? "calc(100vh - 150px)" : `${Math.min(rows.length * 50 + 130, 300)}px`,
                  overflow: "auto",
                }}
                sx={{
                  "& .MuiDataGrid-cell": {
                    padding: "8px",
                  },
                  "& .MuiDataGrid-columnHeaders": {
                    backgroundColor: "#f5f5f5",
                    fontWeight: "bold",
                  },
                }}
              />
            </div>
          </div>
        </Box>
      </div>

      {/* Field Configuration Tabs Section */}
      {selectedOrderType && internalOrderTabs.length > 0 && (
        <div style={{ padding: "20px 0", width: "100%", margin: "0", marginTop: "20px" }}>
          <Typography variant="h6" sx={{ marginBottom: 2 }}>
            {t("Field Configuration for Order Type")}: {selectedOrderType}
          </Typography>

          {/* Row Selection for Field Configuration */}
          {rows.length > 1 && (
            <Box sx={{ marginBottom: 2 }}>
              <Typography variant="body2" sx={{ marginBottom: 1 }}>
                {t("Select row for field configuration")}:
              </Typography>
              <SingleSelectDropdown
                options={rows.map((row) => ({
                  code: row.id,
                  desc: `Row ${rows.indexOf(row) + 1} - ${row.orderType || "No Order Type"}`,
                }))}
                value={activeRowId || rows[0]?.id}
                onChange={(newValue) => setActiveRowId(newValue?.code || newValue)}
                placeholder={t("Select Row")}
                minWidth="300px"
                listWidth={300}
              />
            </Box>
          )}

          <Tabs
            value={selectedTab}
            onChange={(_, newValue) => setSelectedTab(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              borderBottom: 1,
              borderColor: "divider",
              "& .MuiTabs-indicator": {
                backgroundColor: "#3026B9",
                height: "3px",
              },
            }}
          >
            {internalOrderTabs.map((tab, index) => (
              <Tab key={index} label={tab.tab} />
            ))}
          </Tabs>

          <Paper elevation={2} sx={{ p: 3, borderRadius: 4, marginTop: 2 }}>
            {internalOrderTabs[selectedTab] && <GenericTabsGlobal disabled={false} basicDataTabDetails={internalOrderTabs[selectedTab].data} dropDownData={IODropdownData} activeViewTab={internalOrderTabs[selectedTab].tab} uniqueId={activeRowId || rows[0]?.id || "default"} selectedRow={rows.find((row) => row.id === (activeRowId || rows[0]?.id)) || {}} module={MODULE.IO} />}
          </Paper>
        </div>
      )}

      {/* Bottom Navigation for Actions */}
      <BottomNavGlobal
        handleSaveAsDraft={handleSaveAsDraft}
        handleSubmitForReview={handleSubmitForReview}
        handleSubmitForApprove={handleSubmitForApprove}
        handleSendBack={handleSendBack}
        handleCorrection={handleCorrection}
        handleRejectAndCancel={handleRejectAndCancel}
        handleValidateAndSyndicate={handleValidateAndSyndicate}
        validateAllRows={validateAllRows}
        isSaveAsDraftEnabled={true}
        validateEnabled={true}
        filteredButtons={filteredButtons}
        moduleName={MODULE.IO}
      />
    </div>
  );
};

export default RequestDetailsIO;
