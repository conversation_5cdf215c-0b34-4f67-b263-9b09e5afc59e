import {
  Box,
  Button,
  DialogActions,
  DialogContent,
  Grid,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Button,
  Stepper,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { ArrowCircleLeftOutlined } from "@mui/icons-material";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import useLang from "@hooks/useLang";
import ExcelOperationsCard from "@components/Common/ExcelOperationsCard"
import { APP_END_POINTS } from "@constant/appEndPoints";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { WarningOutlined } from "@mui/icons-material";
import {
  button_Outlined,
  button_Primary,
} from "@components/common/commonStyles";
import { colors } from "@constant/colors";
import {
  ARTIFACTNAMES,
  DECISION_TABLE_NAME,
  DIALOUGE_BOX_MESSAGES,
  LOCAL_STORAGE_KEYS,
  MODULE_MAP,
  REGION,
  REQUEST_STATUS,
  REQUEST_TYPE,
} from "@constant/enum";
import {
  setHeaderFieldsBOM,
  setTabValue,
  clearTabData,
  setSelectedRowID,
  setBomRows,
  setDropDownDataBOM,
  setBOMpayloadData,
  clearBOMpayloadData,
  setButtonDTData,
} from "./bomSlice";
import RequestHeaderBOM from "./RequestHeaderBOM";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { resetPayloadData } from "@app/payloadSlice";
import BOMListDetails from "./BOMListDetails";
import { doAjax } from "@components/Common/fetchService";
import { destination_BOM } from "../../destinationVariables";
import { getLocalStorage, setLocalStorage } from "@helper/helper";
import ReusableAttachementAndComments from "@components/Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import AttachmentsCommentsTab from "@components/RequestBench/RequestPages/AttachmentsCommentsTab";
import { idGenerator } from "../../functions";
import PreviewPage from "@components/RequestBench/PreviewPage";
import useDisplayBomData from "./hooks/useDisplayBomData";
import { transformBOMDisplayResponseToRedux } from "../../functions";

const BomCreateRequest = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { getDtCall, dtData } = useGenericDtCall();
  const { getDtCall: getAttachmentDt, dtData: dtAttachmentData } =
    useGenericDtCall();
  const { t } = useLang();
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const requestId = urlSearchParams.get("RequestId");
  const steps = [
    t("Request Header"),
    t("BOM List"),
    t("Attachments & Remarks"),
    t("Preview"),
  ];
  const requestIdHeader = useSelector((state) => state.bom.requestHeaderID);
  const payloadFields = useSelector((state) => state.bom.BOMpayloadData);
  const tabValue = useSelector((state) => state.bom.tabValue);
  const queryParams = new URLSearchParams(location.search);
  const reqBench = queryParams.get("reqBench");
  const RequestType = queryParams.get("RequestType");
  const taskData = useSelector((state) => state.userManagement?.taskData);
  const rowData = location.state;

  const [isDialogVisible, setisDialogVisible] = useState(false);
  const [completed, setCompleted] = useState([false]);
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [pcNumber, setPcNumber] = useState("");
  const [downloadClicked, setDownloadClicked] = useState(false);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const { getDisplayBomData } = useDisplayBomData();
  const payloadForDownloadExcel = {
    // materialDetails: materialPayload,
    // dtName: getDTNameAndVersion(
    //   materialPayload?.[0]?.Torequestheaderdata?.RequestType
    // ).dtName,
    // version: getDTNameAndVersion(
    //   materialPayload?.[0]?.Torequestheaderdata?.RequestType
    // ).version,
    // requestId: materialPayload?.[0]?.Torequestheaderdata?.RequestId || "",
    // scenario: getDTNameAndVersion(
    //   materialPayload?.[0]?.Torequestheaderdata?.RequestType
    // )?.scenario,
    // templateName:
    //   payloadData?.RequestType === REQUEST_TYPE?.CHANGE ||
    //   payloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
    //     ? materialPayload?.[0]?.Torequestheaderdata?.TemplateName
    //     : "",
    // matlType: "ALL",
    // region: materialPayload?.[0]?.Torequestheaderdata?.Region || "",
  };
  const createPayload = useSelector((state) => state.bom);

  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted([true]);
    }
  }, [isSecondTabEnabled]);

  useEffect(() => {
    const attachmentNames =
      dtAttachmentData?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE || [];
    setAttachmentsData(attachmentNames);
  }, [dtAttachmentData]);

  useEffect(() => {
    const loadData = async () => {
      if (requestId) {
        const savedTask = getLocalStorage(
          LOCAL_STORAGE_KEYS.CURRENT_TASK,
          true,
          {}
        );
        const effectiveRequestType =
          RequestType || taskData?.ATTRIBUTE_2 || savedTask?.ATTRIBUTE_2;
        await getDisplayBomData(
          requestId,
          effectiveRequestType,
          reqBench,
          taskData,
          rowData
        );
        if (
          ((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD &&
            !rowData?.material?.length) ||
            RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) &&
          (rowData?.reqStatus === REQUEST_STATUS.DRAFT ||
            rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)
        ) {
          dispatch(setTabValue(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(false);
        } else {
          dispatch(setTabValue(1));
          setIsSecondTabEnabled(true);
          setIsAttachmentTabEnabled(true);
        }
      } else {
        dispatch(setTabValue(0));
      }
    };
    loadData();
    fetchHeaderFieldsFromDt();
    fetchAttachmentFieldsFromDt();
    dispatch(setDropDownDataBOM({ keyName: "Region", data: REGION }));
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.BOM);
    dispatch(clearBOMpayloadData());
    dispatch(clearTabData());
    dispatch(setButtonDTData(null));
    dispatch(setSelectedRowID(null));
    dispatch(setBomRows([]));
    fetchAndSetDropdown("getBomUsage", "BOMUsage");
    fetchAndSetDropdown("getBomItemCategory", "Category");
    fetchAndSetDropdown("getPlant", "Plant");

    setPcNumber(idGenerator("BOM"));
  }, []);

  useEffect(() => {
    if (dtData) {
      let responseData = dtData?.result[0]?.MDG_MAT_REQUEST_HEADER_CONFIG;
      const formattedData = responseData
        .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
        .map((item) => ({
          fieldName: item.MDG_MAT_UI_FIELD_NAME,
          sequenceNo: item.MDG_MAT_SEQUENCE_NO,
          fieldType: item.MDG_MAT_FIELD_TYPE,
          maxLength: item.MDG_MAT_MAX_LENGTH,
          value: item.MDG_MAT_DEFAULT_VALUE,
          visibility: item.MDG_MAT_VISIBILITY,
          jsonName: item.MDG_MAT_JSON_FIELD_NAME,
        }));

      const requestHeaderObj = { "Header Data": formattedData };
      dispatch(setHeaderFieldsBOM(requestHeaderObj));
    }
  }, [dtData]);

  const fetchAndSetDropdown = (endpoint, keyName) => {
    const hSuccess = (data) => {
      dispatch(setDropDownDataBOM({ keyName, data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_BOM}/data/${endpoint}`, "get", hSuccess, hError);
  };

  const handleTabChange = (index) => {
    dispatch(setTabValue(index));
  };

  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    } else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    } else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.BOM);
    }
  };

  const handleCancel = () => {
    setisDialogVisible(false);
  };

  const fetchHeaderFieldsFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_REQUEST_HEADER_CONFIG,
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_MODULE": "BOM",
        },
      ],
    };
    getDtCall(payload);
  };
  const fetchAttachmentFieldsFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "Material",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    getAttachmentDt(payload);
  };

  return (
    <>
      <Box sx={{ padding: 2 }}>
        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {requestIdHeader || requestId ? (
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                textAlign: "left",
                display: "flex",
                alignItems: "center",
                gap: 1,
              }}
            >
              <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
              {t("Request Header ID")}:{" "}
              <span>{requestIdHeader ? requestIdHeader : requestId}</span>
            </Typography>
          ) : (
            <div style={{ flex: 1 }} />
          )}
        </Grid>

        {payloadFields?.TemplateName && (
          <Typography
            variant="h6"
            sx={{
              mb: 1,
              textAlign: "left",
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            <FeedOutlinedIcon sx={{ fontSize: "1.5rem" }} />
            {t("Template Name")}: <span>{payloadFields?.TemplateName}</span>
          </Typography>
        )}
        <IconButton
          onClick={() => {
            if (reqBench) {
              navigate(APP_END_POINTS?.REQUEST_BENCH);
              return;
            }
            setisDialogVisible(true);
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{ left: "-10px" }}
          title={t("Back")}
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>
        <Stepper
          nonLinear
          activeStep={tabValue}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "25px 14%",
            marginTop: "-35px",
          }}
        >
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton
                color="error"
                disabled={
                  (index === 1 && !isSecondTabEnabled) ||
                  (index === 2 && !isAttachmentTabEnabled) ||
                  (index === 3 && !isAttachmentTabEnabled)
                }
                onClick={() => handleTabChange(index)}
                sx={{ fontSize: "50px", fontWeight: "bold" }}
              >
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>
                  {label}
                </span>
              </StepButton>
            </Step>
          ))}
        </Stepper>
        {tabValue === 0 && (
          <>
            <RequestHeaderBOM
              setIsSecondTabEnabled={setIsSecondTabEnabled}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
            />
            {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) && ((rowData?.reqStatus == REQUEST_STATUS.DRAFT && !rowData?.material?.length) || rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
              <ExcelOperationsCard
                handleDownload={()=>{}}
                setEnableDocumentUpload={""}
                enableDocumentUpload={""}
                handleUploadMaterial={()=>{}}
              />
            )}
          </>
        )}
        {tabValue === 1 && (
          <BOMListDetails
            setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
            setCompleted={setCompleted}
          />
        )}
        {tabValue === 2 && (
          <AttachmentsCommentsTab
            requestStatus={REQUEST_STATUS.ENABLE_FOR_FIRST_TIME}
            attachmentsData={attachmentsData}
            requestIdHeader={requestIdHeader ? requestIdHeader : requestId}
            pcNumber={pcNumber}
            module={MODULE_MAP?.BOM}
            artifactName={ARTIFACTNAMES.BOM}
          />
        )}
        {tabValue === 3 && (
          <Box
            sx={{
              width: "100%",
              overflow: "auto",
            }}
          >
            <PreviewPage
              requestStatus={REQUEST_STATUS.ENABLE_FOR_FIRST_TIME}
              module={MODULE_MAP?.BOM}
              payloadData={createPayload}
              payloadForDownloadExcel={payloadForDownloadExcel}
            />
          </Box>
        )}
      </Box>
      {isDialogVisible && (
        <CustomDialog
          isOpen={isDialogVisible}
          titleIcon={
            <WarningOutlined
              size="small"
              sx={{ color: colors?.secondary?.amber, fontSize: "20px" }}
            />
          }
          Title={t("Warning")}
          handleClose={handleCancel}
        >
          <DialogContent sx={{ mt: 2 }}>
            {t(DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE)}
          </DialogContent>
          <DialogActions>
            <Button
              variant="outlined"
              size="small"
              sx={{ ...button_Outlined }}
              onClick={handleCancel}
            >
              {t("No")}
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              onClick={handleYes}
            >
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </>
  );
};

export default BomCreateRequest;
