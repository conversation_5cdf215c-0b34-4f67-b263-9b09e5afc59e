import domtoimage from "dom-to-image";
import axios from "axios";
import moment from "moment";
import ManageAccountsOutlinedIcon from "@mui/icons-material/ManageAccountsOutlined";
import { IconButton, Tooltip, Button, Stack, Typography } from "@mui/material";
import {
  iconButton_SpacingSmall,
  button_Primary,
} from "./components/common/commonStyles";
import {
  destination_CostCenter_Mass,
  destination_ITM,
  destination_Po,
} from "./destinationVariables";
import Store from "./app/store";
import Excel from "exceljs";
import { saveAs } from "file-saver";
import { doAjax } from "./components/Common/fetchService";
import jsPDF from "jspdf";
// import "jspdf-autotable";
import generatePDF, { Margin, Resolution, usePDF } from "react-to-pdf";
import ReactDOMServer from "react-dom/server";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  VALIDATION_STATUS,
  MATERIAL_VIEWS,
  REGION_CODE,
  REQUEST_TYPE,
} from "@constant/enum";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import WarningIcon from "@mui/icons-material/Warning";
import { CircularProgress } from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import { colors } from "@constant/colors";
import { convertSAPDateForCalendar } from "./helper/helper";
import { EAN_CATEGORIES } from "./constant/enum";
import { setDependentDropdown } from "@app/dropDownDataSlice";
import { destination_ProfitCenter_Mass } from "./destinationVariables";
import { updateModuleFieldDataCC } from "./app/costCenterTabsSlice";
import { setDropDown } from "./app/dropDownDataSlice";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import dayjs from "dayjs"; 
import { setDropDown as  setDropDownAction} from "@costCenter/slice/costCenterDropDownSlice";


let timeStampToDate = (timeStamp) => {
  let dateFormat = new Date(parseInt(timeStamp));
  const appSettings = Store.appSettings["Format"];
  let datestamp =
    dateFormat.getDate() +
    "-" +
    (dateFormat.getMonth() + 1) +
    "-" +
    dateFormat.getFullYear();

  return moment(datestamp, "DD-MM-YYYY").format(appSettings.date);
};
let getTimestamp = (aDate) => {
  if (aDate) {
    const presentDate = new Date();
    const backDate = new Date();
    backDate.setDate(backDate.getDate() - 1);
    if (new Date(aDate).toDateString() === presentDate.toDateString()) {
      return `Today ${moment(aDate).format("hh:mm A")}`;
    }
    if (new Date(aDate).toDateString() === backDate.toDateString()) {
      return `Yesterday ${moment(aDate).format("hh:mm A")}`;
    }
    return `${moment(aDate).format("DD MMM YYYY hh:mm A")}`;
  }
};

export const showToast = (message, type = "default", options = {}) => {
  if (!toast.isActive(message)) {
    const {
      position = "bottom-left",
      autoClose = 3000,
      hideProgressBar = true,
      toastLoader = false,
      isLoading = false,
      largeWidth = false,
    } = options;

    const iconMap = {
      success: (
        <CheckCircleIcon
          sx={{ color: colors?.success?.bright, fontSize: 20 }}
        />
      ),
      error: <ErrorIcon sx={{ color: colors?.error?.red, fontSize: 20 }} />,
      warning: (
        <WarningIcon sx={{ color: colors?.warning?.orange, fontSize: 20 }} />
      ),
      info: <InfoIcon sx={{ color: colors?.info?.pale, fontSize: 20 }} />,
      default: null,
    };
    const toastContent = (
      <Stack
        direction="row"
        spacing={1.5}
        alignItems="center"
        sx={{
          maxWidth: largeWidth ? 600 : 320, // Dynamically Set Width
          wordBreak: "break-word",
        }}
      >
        {isLoading ? (
          <CircularProgress size={20} color="inherit" />
        ) : (
          iconMap[type]
        )}
        <Typography sx={{ fontSize: "0.875rem" }}>{message}</Typography>
      </Stack>
    );

    if (toastLoader) {
      toast.loading(message, {
        position,
        theme: "dark",
        toastId: message,
      });
    } else {
      toast(toastContent, {
        type,
        position,
        autoClose,
        hideProgressBar,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        theme: "dark",
        toastId: message,
        icon: false,
      });
    }
  }
};

let idGenerator = (ref) => {
  let date = new Date();
  // let randomNumb = Math.floor((Math.random() * 9999) - 1)
  const month =
    date.getMonth() < 10
      ? "0" + (date.getMonth() + 1).toString()
      : date.getMonth() + 1;
  const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  const hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  const minutes =
    date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  const seconds =
    date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();

  // return `${ref}${(date.getUTCFullYear()).toString().slice(-2)}${date.getMonth()+1}${date.getDate()}${date.getHours()}${date.getMinutes()}${(date.getSeconds())}`
  return `${ref}${date
    .getUTCFullYear()
    .toString()
    .slice(-2)}${month}${day}${hour}${minutes}${seconds}`;
};
let ASNidGenerator = (ref) => {
  let date = new Date();
  // let randomNumb = Math.floor((Math.random() * 9999) - 1)
  const month =
    date.getMonth() < 10
      ? "0" + (date.getMonth() + 1).toString()
      : date.getMonth() + 1;
  const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  const hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  const minutes =
    date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  const seconds =
    date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  const miliSeconds =
    date.getMilliseconds() < 10
      ? "00" + date.getMilliseconds()
      : date.getMilliseconds();
  // return `${ref}${(date.getUTCFullYear()).toString().slice(-2)}${date.getMonth()+1}${date.getDate()}${date.getHours()}${date.getMinutes()}${(date.getSeconds())}`
  return `${ref}${date
    .getUTCFullYear()
    .toString()}${month}${day}${hour}${minutes}${seconds}${miliSeconds}`;
};
// let serviceRequestIcon =   <MiscellaneousServicesOutlinedIcon />
let RETidGenerator = (ref) => {
  let date = new Date();
  // let randomNumb = Math.floor((Math.random() * 9999) - 1)
  const month =
    date.getMonth() < 10
      ? "0" + (date.getMonth() + 1).toString()
      : date.getMonth() + 1;
  const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  const hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  const minutes =
    date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  const seconds =
    date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  const miliSeconds =
    date.getMilliseconds() < 10
      ? "00" + date.getMilliseconds()
      : date.getMilliseconds();
  // return `${ref}${(date.getUTCFullYear()).toString().slice(-2)}${date.getMonth()+1}${date.getDate()}${date.getHours()}${date.getMinutes()}${(date.getSeconds())}`
  return `${ref}${date
    .getUTCFullYear()
    .toString()}${month}${day}${hour}${minutes}${seconds}${miliSeconds}`;
};

let SESIDGenerator = (ref) => {
  let date = new Date();
  const month =
    date.getMonth() < 10
      ? "0" + (date.getMonth() + 1).toString()
      : date.getMonth() + 1;
  const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  const hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  const minutes =
    date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  const seconds =
    date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  const miliSeconds =
    date.getMilliseconds() < 10
      ? "00" + date.getMilliseconds()
      : date.getMilliseconds();
  return `${ref}${date
    .getUTCFullYear()
    .toString()}${month}${day}${hour}${minutes}${seconds}${miliSeconds}`;
};

let PlanningMgmtIDGenerator = (ref) => {
  let date = new Date();
  const month =
    date.getMonth() < 10
      ? "0" + (date.getMonth() + 1).toString()
      : date.getMonth() + 1;
  const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  const hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  const minutes =
    date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  const seconds =
    date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  const miliSeconds =
    date.getMilliseconds() < 10
      ? "00" + date.getMilliseconds()
      : date.getMilliseconds() < 100
      ? "0" + date.getMilliseconds()
      : date.getMilliseconds();
  return `${ref}${date
    .getUTCFullYear()
    .toString()}${month}${day}${hour}${minutes}${seconds}${miliSeconds}`;
};

let getColor_Status = (status, colorObj) => {
  let modified_Status = status.toLowerCase().split(" ").join("");
  if (modified_Status in colorObj) {
    return colorObj[modified_Status];
  } else {
    return "none";
  }
};
let getColor_priority = (priority, colorObj) => {
  let modified_Priority = priority
    .toLowerCase()
    .replace(/[" "]/gi, "")
    .replace(/[0-9]/gi, "")
    .replace(/[-|.|@|#|$|%|^|&|*|(|)|<|>|?|"|:|;|}|{|]/gi, "");

  if (modified_Priority in colorObj) {
    return colorObj[modified_Priority];
  } else {
    return "none";
  }
};

let controller_UrlRedirecting = (value = null, type = null) => {
  let lowercasedValue = value?.toLowerCase();
  let lowercasedType = type?.toLowerCase();
  if (lowercasedValue.includes("com")) {
    return ``;
  }
  if (value && type) {
    if (lowercasedType === "e-invoice") {
      return `/invoices/singleInvoice/${value}`;
    }
    if (lowercasedType === "service request") {
      return `/serviceRequest/details/${value}`;
    }
    if (lowercasedType === "purchase order") {
      return `/purchaseOrder/management/singlePurchaseOrder/${value}`;
    }
    if (lowercasedType === "daily production report") {
      return `/purchaseOrder/DPR/singleDPR/${value}`;
    }
    if (lowercasedType === "advanced shipment notification") {
      return `/purchaseOrder/ASN/details/${value}`;
    }
    if (lowercasedType === "po workbench") {
      return `/purchaseOrder/confirmationTracker/taskDetail/${value}`;
    }
    if (lowercasedType === "invoice tracker") {
      return `/invoices/singleInvoice/${value}`;
    }
    if (lowercasedType === "invoice workbench") {
      return `/invoices/workbench/singleInvoice/${value}`;
    }
    if (lowercasedType === "return") {
      return `/ReturnManagement/SingleReturnOrder/${value}`;
    }
  }
  if (type && value === null) {
    if (lowercasedType === "home") {
      return `/`;
    }
    if (lowercasedType === "dashboard") {
      return `/dashboard`;
    }
    if (lowercasedType === "purchase order") {
      return `/purchaseOrder/management`;
    }
    if (lowercasedType === "daily production report") {
      return `/DPR`;
    }
    if (lowercasedType === "advance shipment notification") {
      return `/purchaseOrder/advanceShipmentManagement`;
    }
    if (lowercasedType === "po workbench") {
      return ` /workbench`;
    }
    if (lowercasedType === "e-invoice") {
      return `/invoices`;
    }
    if (lowercasedType === "invoice tracker") {
      return `/invoices`;
    }
    if (lowercasedType === "invoice workbench") {
      return `/invoices/workbench`;
    }
    if (lowercasedType === "service request") {
      return `/serviceRequest`;
    }
  }
};

const handleMultipleDownload = (array, key, fileName) => {
  array.forEach((item, index) => {
    axios({
      url: item[key],
      method: "GET",
      responseType: "blob", // important
    }).then((response) => {
      const href = URL.createObjectURL(response.data);

      // create "a" HTML element with href to file & click
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", item[fileName]); //or any other extension
      document.body.appendChild(link);
      link.click();

      // clean up "a" element & remove ObjectURL
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
    });
  });
};

async function setStatusRecord(poNumber, status, date, module) {
  var formData = new FormData();

  formData.set("poNumber", poNumber);
  formData.set("status", status);
  formData.set("date", date);
  formData.set("module", module);
  await fetch(`/${destination_Po}/poFlow/save`, {
    method: "POST",
    body: formData,
  })
    .then((res) => res.json())
    .then((data) => console.log(data));
}

class createServiceRequestForm {
  constructor(state, setstate, callback) {
    this.state = state;
    this.setState = setstate;
    this.icon = <ManageAccountsOutlinedIcon />;
    this.reload = callback ? callback : null;
  }

  button = () => {
    let accessList = Store.getState().masterData.accessList;
    if (accessList?.["ServiceRequest"]) {
      return (
        <Tooltip title="Create Service Request">
          <IconButton sx={iconButton_SpacingSmall} onClick={this.open}>
            {this.icon}
          </IconButton>
        </Tooltip>
      );
    } else {
      return <></>;
    }
  };
  createSR_Button = () => {
    return (
      <Button
        size="small"
        variant="contained"
        sx={{ ...button_Primary }}
        onClick={this.open}
      >
        Create Service Request
      </Button>
    );
  };
  component = (Data, ids = []) => {
    return (
      <ServiceRequestForm
        open={this.state.open}
        controller={this.controller}
        data={Data}
        transactionIds={ids}
      />
    );
  };
  open = () => {
    this.setState({ open: true });
  };
  controller = (ref) => {
    switch (ref) {
      case "CLOSE_FORM":
        this.setState({ open: false });
        break;
      case "SUCCESS":
        this.setState({ open: false });
        this.reload && typeof this.reload === "function" && this.reload();
        break;
      case "FAILED":
        break;
      default:
        return null;
    }
  };
}
const getAddressList = async (
  street = "",
  locality = "",
  postalcode = "",
  country = "",
  region = "",
  dqsToken
) => {
  const apiUrl =
    "https://api.dqmmicro.cfapps.us10.hana.ondemand.com/dq/addressCleanse";

  const payload = {
    addressInput: {
      street: street,
      locality: locality,
      region: region,
      postcode: postalcode,
      country: country,
      po_box: "",
      po_box_locality: "",
      po_box_region: "",
      po_box_postcode: "",
      po_box_country: "",
    },
    generateSuggestionList: true,
    outputFields: [
      "std_addr_prim_name1_4",
      "std_addr_prim_number_full",
      "std_addr_secaddr_no_floor",
      "std_addr_floor_number",
      "std_addr_locality_full",
      "std_addr_region_code",
      "std_addr_postcode_full",
      "std_addr_country_2char",
      "std_addr_po_box_number",
      "std_addr_po_box_locality_full",
      "std_addr_po_box_region_code",
      "std_addr_po_box_postcode_full",
      "std_addr_po_box_country_2char",
      "addr_asmt_level",
      "addr_po_box_asmt_level",
      "addr_info_code",
      "addr_po_box_info_code",
    ],
  };
  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${dqsToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    const jsonData = await response.json();

    if (jsonData?.addr_sugg_list?.length > 0) {
      let addressList = jsonData.addr_sugg_list.filter(
        (address) => address?.sugg_addr_range_type === ""
      );
      return [addressList, jsonData];
    } else {
      return [[], jsonData];
    }
  } catch (error) {
    // setError(error.message);
  }
};

const fetchAddressData = async (
  street = "",
  locality = "",
  postalcode = "",
  country = "",
  region = "",
  suggRply,
  dqsToken
) => {

  const apiUrl =
    "https://api.dqmmicro.cfapps.us10.hana.ondemand.com/dq/addressCleanse";

  const payload = JSON.stringify({
    addressInput: {
      mixed: street,
      country: country,
      locality: locality,
      region: region,
      postcode: postalcode,
    },
    generateSuggestionList: false,
    suggestionReply: [suggRply],
    outputFields: [
      "std_addr_address_delivery",
      "std_addr_locality_full",
      "std_addr_postcode_full",
      "addr_latitude",
      "addr_asmt_info",
      "addr_longitude",
      "addr_asmt_level",
      "addr_info_code",
      "geo_asmt_level",
      "geo_info_code",
      "std_addr_sec_address",
      "std_addr_region_code",
      "std_addr_prim_name",
    ],
  });

  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${dqsToken}`,
        "Content-Type": "application/json",
      },
      body: payload,
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }

    const jsonData = await response.json();

    return jsonData;
  } catch (error) {
    console.error(error.message);
    throw error;
  }
};
let formValidator = (stateData, keyList_Received, setErrorItemList) => {
  let keyList = [...keyList_Received];
  let ErrorItemList = [];
  keyList?.map(
    (item) =>
      !(
        stateData?.[item] !== "" &&
        stateData?.[item] !== null &&
        stateData?.[item] !== undefined
      ) && ErrorItemList?.push(item)
  );

  ErrorItemList.length > 0
    ? setErrorItemList(ErrorItemList)
    : setErrorItemList([]);

  return keyList?.every(
    (item) =>
      stateData?.[item] !== "" &&
      stateData?.[item] !== null &&
      stateData?.[item] !== undefined
  );
};
// let formValidatorForArray = (stateData, keyList_Received, setErrorItemList) => {
//   let keyList = [...keyList_Received];
//   let ErrorItemList = [];
//   keyList.map(
//     (item) =>
//       !(
//         stateData[item] !== "" &&
//         stateData[item] !== null &&
//         stateData[item] !== undefined
//       ) && ErrorItemList= stateData
//   );

//   ErrorItemList.length > 0
//     ? setErrorItemList((prev)=>[...prev,ErrorItemList])
//     : setErrorItemList(null);

//   return keyList.every(
//     (item) =>
//       stateData[item] !== "" &&
//       stateData[item] !== null &&
//       stateData[item] !== undefined
//   );
// };
const capitalize = (str) => {
  if (str === null) {
    return "";
  }
  const arr = str.split(" ");
  for (var i = 0; i < arr.length; i++) {
    arr[i] = arr[i].charAt(0) + arr[i].slice(1).toLowerCase();
  }

  const str2 = arr.join(" ");
  return str2;
};

const capitalizeByWord = (str = "", exWords = []) => {
  const arr = str.split(" ");
  const capitalizedArr = arr.map((word) =>
    exWords.includes(word)
      ? word
      : `${word[0].toUpperCase()}${word.slice(1).toLowerCase()}`
  );
  return capitalizedArr.join(" ");
};

// <-- Function for taking screenshot (Export button) -->

let captureScreenShot = async (imageName) => {
  const html = document.getElementsByTagName("HTML")[0];
  const body = document.getElementsByTagName("body")[0];
  const container = document.getElementById("print_screen");
  const app_container = document.getElementById("app_Container");

  const printScreen = document.querySelector(".printScreen");

  html.style.cursor = "progress";

  let htmlWidth = html.clientWidth;
  let bodyWidth = body.clientWidth;

  const data = container; //CHANGE THIS ID WITH ID OF OUTERMOST DIV CONTAINER
  const newWidth = data?.scrollWidth - data?.clientWidth;

  if (newWidth > data?.clientWidth) {
    htmlWidth += newWidth;
    bodyWidth += newWidth;
  }

  // html.style.width = htmlWidth + "px";
  // body.style.width = bodyWidth + "px";
  // app_container.style.height = "100vh";
  // container.style.height = inherit'

  app_container.style.overflow = "hidden";

  domtoimage
    .toPng(data, { quality: 1, bgcolor: "#fff" })
    .then(function (dataUrl) {
      var link = document.createElement("a");
      link.download = `${imageName}-${moment(new Date()).format(
        "DD-MMM-YY"
      )}.png`;
      link.href = dataUrl;
      link.click();
      // body.style.height = null;
      // html.style.overflow = null;
      html.style.width = null;
      body.style.width = null;
      container.style.height = null;
      container.style.overflow = null;
      app_container.style.overflow = null;
      app_container.style.height = null;
      html.style.cursor = null;
    });
};

export const exportAsPDF = (element) => {
  // Default export is a4 paper, portrait, using millimeters for units
  const doc = new jsPDF();
  let elementHtml = ReactDOMServer.renderToString(element);
  doc.html(elementHtml, {
    callback: function (doc) {
      // Save the PDF
      doc.save(`summary-report.pdf`);
    },
    resolution: Resolution.HIGH,
    page: {
      // margin is in MM, default is Margin.NONE = 0
      margin: Margin.SMALL,
      // default is 'A4'
      format: "letter",
      // default is 'portrait'
      orientation: "landscape",
    },
    x: 15,
    y: 15,
    width: 170, //target width in the PDF document
    windowWidth: 1200, //window width in CSS pixels
  });
  // doc.save('test.pdf')
};

//GET USER EMAIL ID
const getUserEmailId = (input) => {
  try {
    if (input !== "" && input.includes("@")) {
      return input.split("- ")[1];
    } else {
      return "";
    }
  } catch (e) {
    console.log(e);
  }
};

//IWA Access check functions - return boolean
const checkIwaAccess = (data, module = null, feature = null) => {
  if (feature && data) {
    //if entity exists
    return data.includes(feature);
  }
};

const multiConfirmationCheck = (status, multiConfStatus) => {
  if (multiConfStatus) {
    if (
      [
        "REQUIRES CONFIRMATION",
        "CONFIRMED",
        "PARTIALLY CONFIRMED",
        "FULLY CONFIRMED",
        "ASN TO BE INITIATED",
        "PRODUCTION TO BE INITIATED",
      ].includes(status)
    ) {
      return true;
    }
  } else {
    if (status === "REQUIRES CONFIRMATION") {
      return true;
    }
  }
  return false;
};

const generateAccessList = (jsonData) => {
  var accessList = {};
  var urlList = {};

  const processJson = (processdata) => {
    const pushItem = (item) => {
      accessList[item?.name] = item;
      urlList[item?.routhPath] = item;
    };
    processdata?.map((item) => {
      if (!item?.isAccessible) return;
      if (item?.isAccessible && !item?.childItems?.length) pushItem(item);
      if (item?.isAccessible && item?.childItems?.length) {
        pushItem(item);
        processJson(item?.childItems);
      }
    });
  };
  processJson(jsonData);

  return { accessList, urlList };
};
function validatePath(path, masterData) {
  if (masterData?.urlList[path]) {
    return true;
  } else {
    return false;
  }
}
let ValidNavigateTo = (path) => {
  let masterData = Store.getState().masterData;
  let navigate = masterData.navigate;
  if (validatePath(path, masterData)) {
    navigate(path);
  }
  return null;
};

const updateTaskStatus = (action, id, comment, userEmail) => {
  var itmAction;
  if (action.toUpperCase() === "ACCEPT") {
    itmAction = "APPROVE";
  } else if (action.toUpperCase() === "REJECT") {
    itmAction = "REJECT";
  }
  doAjax(`/${destination_Po}/task/workflow-task-Id/${id}`, "get", (data) => {
    if (data?.data) {
      var payload = {
        action: itmAction,
        systemId: "SCP",
        userId: userEmail,
        taskId: data?.data,
        comment: comment,
      };
      doAjax(
        `/${destination_ITM}/v1/task/updateTaskStatus`,
        "post",
        (res) => {
          if (res.statusCode === 200) {
            return true;
          } else {
            return false;
          }
        },
        (error) => {
          console.log(error);
          return false;
        },
        payload
      );
    }
  });
};

const columns = [
  { header: "SYSTEM NAME", key: "systemName" },
  { header: "PROCESS NAME", key: "processName" },
  { header: "ID", key: "referenceId" },
  { header: "TASK DESC", key: "taskDesc" },
  { header: "BUSINESS STATUS", key: "businessStatus" },
  { header: "CREATED BY", key: "createdByName" },
  { header: "ASSIGNED TO", key: "ownerId" },
  // { header: "UPDATED BY", key: "updatedBy" },
  { header: "SLA STAUS", key: "taskSla" },
  { header: "ATTACHMENT COUNT", key: "attachmentCount" },
];

const savePDF = (data) => {
  const doc = new jsPDF();
  try {
    // Get table data
    const tableData = data.rows;

    // Set table columns (headers)
    const tableColumns = data.columns.map((column) => ({
      header: column.header,
      dataKey: column.key,
    }));

    // Add table to the PDF document
    doc.autoTable({
      head: [tableColumns.map((col) => col.header)],
      body: tableData.map((row) => tableColumns.map((col) => row[col.dataKey])),
    });

    // Save the PDF file
    doc.save(`${data.fileName}.pdf`);
  } catch (error) {
    console.log(error, "pdf error");
    // Handle error if needed
  }
};

const saveExcel = async (data) => {
  const workbook = new Excel.Workbook();
  try {
    // creating one worksheet in workbook
    const worksheet = workbook.addWorksheet(data.fileName);
    worksheet.columns = data.columns;

    // updated the font for first row.
    worksheet.getRow(1).font = { bold: true };
    let dataTypeList = [];

    // loop through all of the columns and set the alignment with width.
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.font = { bold: true };
      cell.alignment = { horizontal: "center", vertical: "middle" };

      cell.border = {
        top: { style: "thin", color: { argb: colors?.basic?.black } },
        bottom: { style: "thin", color: { argb: colors?.basic?.black } },
        left: { style: "thin", color: { argb: colors?.basic?.black } },
        right: { style: "thin", color: { argb: colors?.basic?.black } },
      };
    });
    data.rows?.forEach((singleData, index) => {
      let tData = { ...singleData };
      dataTypeList.forEach((item) => {
        if (item.type === "date") {
          tData[item.key] = moment(tData[item.key]).format("DD-MMM-YYYY");
        }
      });
      worksheet.addRow(tData);
    });

    // loop through all of the rows and set the outline style.
    worksheet.eachRow({ includeEmpty: false }, (row) => {
      // store each cell to currentCell
      const currentCell = row._cells;
      // loop through currentCell to apply border only for the non-empty cell of excel
      currentCell.forEach((singleCell) => {
        // store the cell address i.e. A1, A2, A3, B1, B2, B3, ...
        const cellAddress = singleCell._address;
        // apply border
        worksheet.getCell(cellAddress).border = {
          top: { style: "thin" },
          left: { style: "thin" },
          bottom: { style: "thin" },
          right: { style: "thin" },
        };
      });
    });

    // write the content using writeBuffer
    const buf = await workbook.xlsx.writeBuffer();

    // download the processed file
    saveAs(new Blob([buf]), `${data.fileName}.xlsx`);
    // if (searchResults?.length > 0) {
    //   let toastMessage = "task downloaded successfully";
    //   if (searchResults?.length > 1) {
    //     toastMessage = "tasks downloaded successfully";
    //   }
    //   showToastMessage(`${searchResults.length} ${toastMessage}`);
    // }
  } catch (error) {
    console.log(error, "excel error");
    // showToastMessage("Something went wrong");
  } finally {
    // removing worksheet's instance to create new one
    // workbook.removeWorksheet(workSheetName);
  }
};

export const getRoleForWorkflow = (region, rolesArray) => {
  // Define priority of roles for selection
  const priorityRoles = ["CA-MDG-MRKTNG-US"];

  // Filter roles based on the region
  const filteredRoles = rolesArray.filter((role) => role.includes(region));

  // Find the highest-priority role
  for (const priorityRole of priorityRoles) {
    if (filteredRoles.includes(priorityRole)) {
      return priorityRole;
    }
  }

  // Return null if no matching role is found
  return null;
};

const dynamicAddressValidation = async (
  street = "",
  postalcode = "",
  country = "",
  region = "",
  dqsToken
) => {
  // setalertMessage("Validating Address");
  // setalertType("address");
  // handleOpenDialogAdd();

  const addressURL =
    "https://api.dqmmicro.cfapps.us10.hana.ondemand.com/dq/addressCleanse";

  const payload = JSON.stringify({
    addressInput: {
      mixed: street,
      locality: region,
      postcode: postalcode,
      country: country,
    },
    generateTypeAheadSuggestionList: false,
    generateSuggestionList: false,
    suggestionReply: [],
    outputFields: [
      "std_addr_address_delivery",
      "std_addr_locality_full",
      "std_addr_postcode_full",
      "addr_latitude",
      "addr_asmt_info",
      "addr_longitude",
      "addr_asmt_level",
      "addr_info_code",
      "geo_asmt_level",
      "geo_info_code",
      "std_addr_sec_address",
      "std_addr_region_code",
    ],
  });

  try {
    const response = await fetch(addressURL, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${dqsToken}`,
        "Content-Type": "application/json",
      },
      body: payload,
    });
    // setIsLoadingAdd(false)
    if (!response.ok) {
      throw new Error("Network response was not ok");
    }

    const jsonData = await response.json();
    return jsonData;
  } catch (error) {
    console.log(error);
    return true;
  }
};

const exportFileAsPdf = async (rowData, appSettings) => {
  const pdfBlob = await generatePDF(rowData, appSettings);
  return pdfBlob;
};
const downloadFileAsPdf = async (rowData, appSettings) => {
  const pdfBlob = await generatePDF(rowData, appSettings);
  if (pdfBlob) {
    const pdfData = URL.createObjectURL(pdfBlob); // Convert the blob to a URL
    const downloadLink = document.createElement("a"); // Create a download link
    downloadLink.href = pdfData; // Set the URL as the download link's href
    downloadLink.download = `${rowData?.requestId}.pdf`; // Set the download filename
    document.body.appendChild(downloadLink); // Append the download link to the document body
    downloadLink.click(); // Trigger the download
    document.body.removeChild(downloadLink); // Remove the download link from the document body
    return true;
  } else {
    console.log("Error: PDF blob is null or undefined");
    return false;
  }
};

const saveExcelMultiSheets = async (data) => {
  const workbook = new Excel.Workbook();
  try {
    data.forEach((iData) => {
      // creating one worksheet in workbook
      const worksheet = workbook.addWorksheet(iData.sheetName);
      worksheet.columns = iData.columns;

      // updated the font for first row.
      worksheet.getRow(1).font = { bold: true };
      let dataTypeList = [];

      // loop through all of the columns and set the alignment with width.
      worksheet.columns.forEach((column) => {
        if (
          column._header?.toLowerCase().split(" ").join("").includes("date")
        ) {
          dataTypeList.push({
            key: column._key,
            type: "date",
          });
        }

        column.width = column.header.length + 5;
        column.alignment = { horizontal: "center" };
      });

      // loop through data and add each one to worksheet
      iData.rows?.forEach((singleData, index) => {
        // worksheet.getRow(1).getCell(index).value =
        let tData = { ...singleData };
        dataTypeList.forEach((item) => {
          if (item.type === "date") {
            tData[item.key] = moment(tData[item.key]).format("DD-MMM-YYYY");
          }
        });
        worksheet.addRow(tData);
      });

      // loop through all of the rows and set the outline style.
      worksheet.eachRow({ includeEmpty: false }, (row) => {
        // store each cell to currentCell
        const currentCell = row._cells;

        // loop through currentCell to apply border only for the non-empty cell of excel
        currentCell.forEach((singleCell) => {
          // store the cell address i.e. A1, A2, A3, B1, B2, B3, ...
          const cellAddress = singleCell._address;

          // apply border
          worksheet.getCell(cellAddress).border = {
            top: { style: "thin" },
            left: { style: "thin" },
            bottom: { style: "thin" },
            right: { style: "thin" },
          };
        });
      });
    });

    const buf = await workbook.xlsx.writeBuffer();

    saveAs(new Blob([buf]), `${data[0].fileName}.xlsx`);
  } catch (error) {
    console.log(error, "excel error");
  } finally {
    // removing worksheet's instance to create new one
    // workbook.removeWorksheet(workSheetName);
  }
};

const showToastMessage = (
  sMessage,
  sType,
  position,
  autoClose,
  hideProgressBar,
  toastLoader // to dispaly the toast loader value will be boolean(true/false)
) => {
  if (!toast.isActive(sMessage)) {
    if (sType === "success") {
      toast(
        <Stack
          direction="row"
          spacing={1.5}
          alignItems="center"
          maxWidth={320}
          zIndex={999}
        >
          <Typography sx={{ fontSize: "0.875rem" }}>{sMessage}</Typography>
        </Stack>,
        {
          position: position || "bottom-left", // top-left, top-right, top-center, bottom-left, bottom-right, bottom-center
          autoClose: autoClose || 5000, // in ms
          hideProgressBar: hideProgressBar || true, // boolean
          closeOnClick: true, // boolean
          pauseOnHover: true, // boolean
          theme: "dark",
          toastId: sMessage,
        }
      );
    } else if (toastLoader) {
      toast.loading(sMessage, {
        type: sType, // default, error, info, success, warning
        position: position || "bottom-left", // top-left, top-right, top-center, bottom-left, bottom-right, bottom-center
        theme: "dark", // light, dark, colored
        toastId: sMessage,
      });
    } else {
      toast(sMessage, {
        type: sType, // default, error, info, success, warning
        position: position || "bottom-left", // top-left, top-right, top-center, bottom-left, bottom-right, bottom-center
        autoClose: autoClose || 5000, // in ms
        hideProgressBar: hideProgressBar || true, // boolean
        closeOnClick: true, // boolean
        pauseOnHover: true, // boolean
        theme: "dark", // light, dark, colored
        toastId: sMessage,
      });
    }
  }
};

//method to convert display response into redux structure
export const transformApiResponseToReduxPayload = (apiResponse, storedRows) => {
  let counter = storedRows?.length * 10 || 0;
  const nextLineNumber = () => {
    counter += 10;
    return counter;
  };
  const payload = {
    payloadData: {
      ReqCreatedBy: apiResponse[0]?.Torequestheaderdata?.ReqCreatedBy,
      RequestStatus: apiResponse[0]?.Torequestheaderdata?.RequestStatus,
      ReqCreatedOn: new Date().toISOString(),
      ReqUpdatedOn: new Date().toISOString(),
      RequestType: apiResponse[0]?.Torequestheaderdata?.RequestType,
      RequestDesc: apiResponse[0]?.Torequestheaderdata?.RequestDesc,
      RequestPriority: apiResponse[0]?.Torequestheaderdata?.RequestPriority,
      LeadingCat: apiResponse[0]?.Torequestheaderdata?.LeadingCat,
      RequestId: apiResponse[0]?.Torequestheaderdata?.RequestId,
      TotalIntermediateTasks: apiResponse[0]?.TotalIntermediateTasks,
      IntermediateTaskCount: apiResponse[0]?.IntermediateTaskCount,
      Division: apiResponse[0]?.Torequestheaderdata?.Division,
      Region: apiResponse[0]?.Torequestheaderdata?.Region,
    },
    errorFields: [],
    requiredFields: [],
    additionalData: [],
    unitsOfMeasureData: [],
    taxData: [],
    requiredFieldsGI: ["BusinessJustification"],
    singleMatPayload: {
      BusinessJustification: "",
      ChoosePriorityLevel: "Medium",
    },
    generalInformation: [],
    mandatoryFields: [],
    errorData: {},
    fcRows: [],
    changeFieldRows: [],
    changeFieldRowsDisplay: {},
    requestorPayload: {},
    changeLogData: {},
    filteredButtons: [],
    dynamicKeyValues: {},
    dataLoading: false,
    isSubmitDisabled: true,
    newRowIds: [],
    selectedRows: [],
    unselectedRows: [],
    templateArray: [],
    whseList: [],
    matNoList: [],
  };

  function getAccountingId(accountingData) {
    return accountingData?.map((item) => item?.AccountingId);
  }

  // Process each item in the API response
  apiResponse.forEach((item) => {
    const dynamicKey = item?.MaterialId?.toString();
    payload[dynamicKey] = {
      headerData: {
        id: dynamicKey,
        clientId: item?.Toclientdata?.ClientId,
        AccountingId: getAccountingId(item?.Toaccountingdata),
        included: true,
        industrySector: {
          code: item?.IndSector || "",
        },
        materialType: {
          code: item?.MatlType || "",
        },
        lineNumber: item?.lineNumber ?? nextLineNumber() ?? null,
        materialNumber: item?.Material || "",
        globalMaterialDescription: item?.Description || "",
        Bom: item?.Bom || "",
        sourceList: item?.SourceList || "",
        PIR: item?.PIR || "",
        Uom: item?.Uom?.code || item?.Uom || "",
        Category: item?.Category?.code || item?.Category || "",
        Relation: item?.Relation?.code || item?.Relation || "",
        Usage: item?.Usage || "",
        views: [
          ...(item?.ViewNames || "")
            .split(",")
            .map((view) => view.trim())
            .filter(Boolean),
          ...(item?.Bom ? ["BOM"] : []),
          ...(item?.SourceList ? ["Source List"] : []),
          ...(item?.PIR ? ["PIR"] : []),
        ],
        validated: VALIDATION_STATUS.default,
        orgData: item?.OrgData || [],
      },
      payloadData: {
        "Basic Data": {
          basic: {
            BaseUom: item?.Toclientdata?.BaseUom || "",
            MatlGroup: item?.Toclientdata?.MatlGroup || "",
            Division:
              item?.Torequestheaderdata?.Division ||
              item?.Toclientdata?.Division ||
              "",
            ProdHier: item?.Toclientdata?.ProdHier || "",
            VolumeUnit: item?.Toclientdata?.VolumeUnit || "",
            UnitOfWt: item?.Toclientdata?.UnitOfWt || "",
            GItemCat: item?.Toclientdata?.GItemCat || "",
            BasicMatl: item?.Toclientdata?.BasicMatl || "",
            Hazmatprof: item?.Toclientdata?.Hazmatprof || "",
            OldMatNo: item?.Toclientdata?.OldMatNo || "",
            Extmatlgrp: item?.Toclientdata?.Extmatlgrp || "",
            DsnOffice: item?.Toclientdata?.DsnOffice || "",
            NetWeight: item?.Toclientdata?.NetWeight || "",
            HighVisc: item?.Toclientdata?.HighVisc || "",
            IndSector: item?.Toclientdata?.IndSector || "",
            HazMatNo: item?.Toclientdata?.HazMatNo || "",
            StdDescr: item?.Toclientdata?.StdDescr || "",
            ShelfLife: item?.Toclientdata?.ShelfLife || "",
            CSalStatus: item?.Toclientdata?.CSalStatus || "",
            IntlPoPrice: item?.Toclientdata?.IntlPoPrice || "",
            PryVendor: item?.Toclientdata?.PryVendor || "",
            PlanningArea: item?.Toclientdata?.PlanningArea || "",
            PlanningFactor: item?.Toclientdata?.PlanningFactor || "",
            ReturnMatNumber: item?.Toclientdata?.ReturnMatNumber || "",
            ParentMatNumber: item?.Toclientdata?.ParentMatNumber || "",
            DiversionControlFlag:
              item?.Toclientdata?.DiversionControlFlag || "",
            MatGroupPackagingMat:
              item?.Toclientdata?.MatGroupPackagingMat || "",
            ProdAlloc: item?.Toclientdata?.ProdAlloc || "",
            Pvalidfrom:
              convertSAPDateForCalendar(item?.Toclientdata?.Pvalidfrom) || "",
          },
        },
        Purchasing: (item.Toplantdata || []).reduce((acc, plant) => {
          acc[plant.Plant] = {
            PurGroup: plant.PurGroup || "",
            PurStatus: plant.PurStatus || "",
            PlantId: plant?.PlantId,
            CommCode: plant.CommCode || "",
            CommCoUn: plant.CommCoUn || "",
            Countryori: plant.Countryori || "",
            IndPostToInspStock: plant.IndPostToInspStock || "",
            HtsCode: plant.HtsCode || "",
          };
          return acc;
        }, {}),
        Accounting: (item.Toaccountingdata || []).reduce(
          (acc, accounting, index) => {
            acc[accounting?.ValArea] = {
              AccountingId: accounting.AccountingId || "",
              ValClass: accounting.ValClass || "",
              PriceCtrl: accounting.PriceCtrl || "",
              PriceUnit: accounting.PriceUnit || "",
              StdPrice: accounting.StdPrice || "",
            };
            return acc;
          },
          {}
        ),
        Costing: (item.Toaccountingdata || []).reduce(
          (acc, accounting, index) => {
            acc[accounting?.ValArea] = {
              AccountingId: accounting.AccountingId || "",
              OrigMat: accounting.OrigMat || "",
              LotSize: item.Toplantdata?.[index]?.LotSize || "",
              ProfitCtr: item.Toplantdata?.[index]?.ProfitCtr || "",
              VarianceKey: item.Toplantdata?.[index]?.VarianceKey || "",
              MovingPr: accounting.MovingPr || "",
              StdPrice: accounting.StdPrice || "",
            };
            return acc;
          },
          {}
        ),
        MRP: (item.Toplantdata || []).reduce((acc, plant) => {
          acc[`${plant?.Plant}`] = {
            ...plant,
            MrpType: plant.MrpType || "",
            MrpCtrler: plant.MrpCtrler || "",
            ProcType: plant.ProcType || "",
            Backflush: plant.Backflush || "",
            PeriodInd: plant.PeriodInd || "",
            PlanStrgp: plant.PlanStrgp || "",
            Consummode: plant.Consummode || "",
            Availcheck: plant.Availcheck || "",
            DepReqId: plant.DepReqId || "",
            Lotsizekey: plant.Lotsizekey || "",
            SaftyTId: plant.SaftyTId || "",
            SmKey: plant.SmKey || "",
            MixedMrp: plant.MixedMrp || "",
            GrpReqmts: plant.GrpReqmts || "",
            Minlotsize: plant.Minlotsize || "",
            Maxlotsize: plant.Maxlotsize || "",
            FixedLot: plant.FixedLot || "",
            ReorderPt: plant.ReorderPt || "",
            MaxStock: plant.MaxStock || "",
            RoundVal: plant.RoundVal || "",
            AssyScarp: plant.AssyScarp || "",
            Spproctype: plant.Spproctype || "",
            SlocExprc: plant.SlocExprc || "",
            Inhseprodt: plant.Inhseprodt || "",
            PlndDelry: plant.PlndDelry || "",
            GrPrTime: plant.GrPrTime || "",
            Safetytime: plant.Safetytime || "",
            SafetyStk: plant.SafetyStk || "",
            BwdCons: plant.BwdCons || "",
            FwdCons: plant.FwdCons || "",
            Replentime: plant.Replentime || "",
            PlTiFnce: plant.PlTiFnce || "",
            IssStLoc: plant.IssStLoc || "",
            AssyScrap: plant.AssyScrap || "",
          };
          return acc;
        }, {}),
        "Work Scheduling": (item.Toplantdata || []).reduce((acc, plant) => {
          acc[`${plant?.Plant}`] = {
            Unlimited: plant.Unlimited || "",
            ProdProf: plant.ProdProf || "",
          };
          return acc;
        }, {}),
        Sales: (item.Tosalesdata || []).reduce((acc, sale, index) => {
          const key = `${sale?.SalesOrg}-${sale?.DistrChan}`;
          acc[key] = {
            ...sale,
            SalStatus: sale.SalStatus || "",
            SalesId: sale.SalesId || null,
            ValidFrom: convertSAPDateForCalendar(sale.ValidFrom) || null,
            DelyUom: sale.DelyUom || "",
            DelygPlnt: sale.DelygPlnt || "",
            MatPrGrp: sale.MatPrGrp || "",
          };
          return acc;
        }, {}),
        "Sales-General": {
          "Sales-General": {
            TransGrp: item?.Toclientdata?.TransGrp || "",
            BatchMgmt: item?.Toclientdata?.BatchMgmt || false,
            XSalStatus: item?.Toclientdata?.XSalStatus || "",
            Svalidfrom:
              convertSAPDateForCalendar(item?.Toclientdata?.Svalidfrom) || "",
          },
        },
        "Purchasing-General": {
          "Purchasing-General": {
            VarOrdUn: item?.Toclientdata?.VarOrdUn || "",
            PurValkey: item?.Toclientdata?.PurValkey || "",
          },
        },
        TaxData: {
          TaxData: {
            TaxDataSet: (item.Tocontroldata || []).reduce(
              (acc, controlData) => {
                const country = controlData.Depcountry;
                const controlId = controlData.ControlId;
                Object.keys(controlData)
                  .filter((key) => key.startsWith("TaxType"))
                  .forEach((taxKey, index) => {
                    const taxType = controlData[taxKey];
                    const taxClass = controlData[`Taxclass${index + 1}`];
                    if (taxType && taxClass !== undefined) {
                      let existingEntry = acc.find(
                        (entry) =>
                          entry.Country === country && entry.TaxType === taxType
                      );

                      const selected = {
                        TaxClass: taxClass.toString(),
                      };

                      if (!existingEntry) {
                        acc.push({
                          Country: country,
                          TaxType: taxType,
                          ControlId: controlId,
                          SelectedTaxClass: selected,
                        });
                      } else {
                        existingEntry.SelectedTaxClass = selected;
                      }
                    }
                  });
                return acc;
              },
              []
            ),
          },
        },
        [MATERIAL_VIEWS.SALES_PLANT]: (item.Toplantdata || []).reduce(
          (acc, plant) => {
            acc[plant.Plant] = {
              ...plant,
              Matfrgtgrp: plant.Matfrgtgrp || "",
              ProfitCtr: plant.ProfitCtr || "",
              Loadinggrp: plant.Loadinggrp || "",
              Availcheck: plant.Availcheck || "",
            };
            return acc;
          },
          {}
        ),
        [MATERIAL_VIEWS.WAREHOUSE]: (item.Towarehousedata || []).reduce(
          (acc, warehouse, index) => {
            acc[warehouse?.WhseNo] = {
              ...warehouse,
              Placement: warehouse.Placement || "",
            };
            return acc;
          },
          {}
        ),
        Tostroragelocationdata: item.Tostroragelocationdata || [],
        [MATERIAL_VIEWS.BOM]: (item.Toplantdata || []).reduce((acc, plant) => {
          acc[plant.Plant] = {
            // ...plant,
            BomUsage: plant.BomUsage || "",
            AltBom: plant.AltBom || "",
            Category: plant.Category || "",
            Component: plant.Component || "",
            Quantity: plant.Quantity || "",
            CompUom: plant.CompUom || "",
            Bvalidfrom: convertSAPDateForCalendar(plant.Bvalidfrom) || "",
            Bvalidto: convertSAPDateForCalendar(plant.Bvalidto) || "",
          };
          return acc;
        }, {}),
        [MATERIAL_VIEWS.SOURCE_LIST]: (item.Toplantdata || []).reduce(
          (acc, plant) => {
            acc[plant.Plant] = {
              // ...plant,
              PurchaseOrg: plant.PurchaseOrg || "",
              ProcurementPlant: plant.ProcurementPlant || "",
              SOrderUnit: plant.SOrderUnit || "",
              Agreement: plant.Agreement || "",
              AgreementItem: plant.AgreementItem || "",
              FixedSupplySource: plant.FixedSupplySource || "",
              Blocked: plant?.Blocked || "",
              SMrp: plant?.SMrp || "",
              Slvalidfrom: convertSAPDateForCalendar(plant.Slvalidfrom) || "",
              Slvalidto: convertSAPDateForCalendar(plant.Slvalidto) || "",
            };
            return acc;
          },
          {}
        ),
        [MATERIAL_VIEWS.STORAGE]: (item.Tostroragelocationdata || []).reduce(
          (acc, storage) => {
            const key = `${storage?.Plant}-${storage?.StgeLoc}`;
            acc[key] = {
              ...storage,
            };
            return acc;
          },
          {}
        ),
        [MATERIAL_VIEWS.STORAGE_PLANT]: (item.Toplantdata || []).reduce(
          (acc, plant) => {
            acc[plant.Plant] = {
              CcPhInv: plant?.CcPhInv || "",
              CcFixed: plant?.CcFixed || "",
              MaxStock: plant?.MaxStock || "",
              StgePdUn: plant?.StgePdUn || "",
              DefaultStockSegment: plant?.DefaultStockSegment || "",
              NegStocks: plant?.NegStocks || "",
              SernoProf: plant?.SernoProf || "",
              // Loadinggrp : plant?.Loadinggrp || "", already there in salesPlantView
              DistrProf: plant?.DistrProf || "",
              DetermGrp: plant?.DetermGrp || "",
              IuidRelevant: plant?.IuidRelevant || "",
              UidIea: plant?.UidIea || "",
              IuidType: plant?.IuidType || "",
              SortStockBasedOnSegment: plant?.SortStockBasedOnSegment || "",
            };
            return acc;
          },
          {}
        ),
        [MATERIAL_VIEWS.STORAGE_GENERAL]: {
          [MATERIAL_VIEWS.STORAGE_GENERAL]: {
            HazMatNo: item?.Toclientdata?.HazMatNo || "",
            QtyGrGi: item?.Toclientdata?.QtyGrGi || "",
            TempConds: item?.Toclientdata?.TempConds || "",
            Container: item?.Toclientdata?.Container || "",
            LabelType: item?.Toclientdata?.LabelType || "",
            LabelForm: item?.Toclientdata?.LabelForm || "",
            AppdBRec: item?.Toclientdata?.AppdBRec || "",
            Minremlife: item?.Toclientdata?.Minremlife || "",
            ShelfLife: item?.Toclientdata?.ShelfLife || "",
            PeriodIndExpirationDate:
              item?.Toclientdata?.PeriodIndExpirationDate || "",
            RoundUpRuleExpirationDate:
              item?.Toclientdata?.RoundUpRuleExpirationDate || "",
            StorPct: item?.Toclientdata?.StorPct || "",
            SledBbd: item?.Toclientdata?.SledBbd || "",
            SerializationLevel: item?.Toclientdata?.SerializationLevel || "",
            MaturityDur: item?.Toclientdata?.MaturityDur || "",
            ShelfLifeReqMax: item?.Toclientdata?.ShelfLifeReqMax || "",
            ShelfLifeReqMin: item?.Toclientdata?.ShelfLifeReqMin || "",
            StorConds: item?.Toclientdata?.StorConds || "",
          },
        },
        [MATERIAL_VIEWS.CLASSIFICATION]: {
          basic: {
            ClassificationId: item.ToClassification?.[0]?.ClassificationId,
            Classnum: item.ToClassification?.[0]?.Classnum || "",
            Classtype: item.ToClassification?.[0]?.Classtype || "",
          },
          classification: (item.ToClassification?.[0]?.Tochars || []).map(
            (char, index) => ({
              id: index + 1,
              CharacteristicsId: char?.CharacteristicsId,
              characteristic: char.Charact || "",
              description: char.CharactDescr || "",
              className: item.ToClassification?.[0]?.Classnum || "",
              value:
                (char.Tocharvalues && char.Tocharvalues[0]?.ValueChar) || "",
            })
          ),
        },
      },
      ManufacturerID: item?.ManufacturerId || "",
      unitsOfMeasureData:
        (item?.Touomdata || []).length === 0
          ? []
          : item?.Touomdata.map((uom, index) => ({
              ...uom,
              id: uom?.UomId,
              UomId: uom?.UomId || null,
              xValue: uom?.Denominatr || "1",
              aUnit: uom?.AltUnit || "",
              measureUnitText: "",
              yValue: uom?.Numerator || "1",
              eanUpc: uom?.EanUpc || "",
              eanCategory: uom?.EanCat || "",
              length: uom?.Length,
              width: uom?.Width,
              height: uom?.Height,
              unitsOfDimension: uom?.UnitDim || "",
              volume: uom?.Volume || "",
              volumeUnit: uom?.Volumeunit || "",
              grossWeight: uom?.GrossWt || "",
              netWeight: uom?.NetWeight || "",
              weightUnit: uom?.UnitOfWt || "",
            })),
      eanData:
        (item?.Toeandata || []).length === 0
          ? []
          : item?.Toeandata.map((ean, index) => ({
              ...ean,
              id: ean?.EanId,
              EanId: ean?.EanId,
              altunit: ean?.Unit,
              eanUpc: ean?.EanUpc,
              eanCategory: ean?.EanCat,
              MainEan: ean?.MainEan,
              au: ean?.Au,
            })),
      additionalData:
        (item?.Tomaterialdescription || []).length === 0
          ? []
          : (item?.Tomaterialdescription || []).map((addData, index) => ({
              id: index + 1,
              MaterialDescriptionId: addData?.MaterialDescriptionId,
              Function: addData?.Function,
              Material: addData?.Material,
              language: addData?.Langu,
              materialDescription: addData?.MatlDesc,
              DelFlag: addData?.DelFlag,
            })),
      Tomaterialerrordata: item?.Tomaterialerrordata || {},
      changeLogData: item?.changeLogData || {},
      Tochildrequestheaderdata: item?.Tochildrequestheaderdata
        ? item?.Tochildrequestheaderdata
        : {},
    };
  });
  return { payload };
};

export const transformApiResponseToReduxPayloadExtend = (
  apiResponse,
  orgRow,
  initialPayload,
  selectedViews,
  selectedMaterialRow
) => {
  let counter = 0;
  const nextLineNumber = () => {
    counter += 10;
    return counter;
  };

  const payload = {
    payloadData: {
      ...initialPayload,
    },
    errorFields: [],
    requiredFields: [],
    additionalData: [],
    unitsOfMeasureData: [],
    taxData: [],
    requiredFieldsGI: ["BusinessJustification"],
    singleMatPayload: {
      BusinessJustification: "",
      ChoosePriorityLevel: "Medium",
    },
    generalInformation: [],
    mandatoryFields: [],
    fcRows: [],
    errorData: {},
    changeFieldRows: [],
    changeFieldRowsDisplay: {},
    requestorPayload: {},
    changeLogData: {},
    filteredButtons: [],
    dynamicKeyValues: {},
    dataLoading: false,
    isSubmitDisabled: true,
    newRowIds: [],
    selectedRows: [],
    unselectedRows: [],
    templateArray: [],
    whseList: [],
    matNoList: [],
  };

  function getAccountingId(accountingData) {
    return accountingData?.map((item) => item?.AccountingId);
  }

  apiResponse.forEach((item) => {
    const dynamicKey = item.Material.toString();
    const purchasingKey = [item.Toplantdata];
    const accountingKey = [item.Toaccountingdata] ?? [];
    const wareHouseKey = [item.ToWarehousedata] ?? [];
    const plantID = orgRow?.map((item) => item?.plant?.value?.code);
    const salesID = orgRow?.map(
      (item) => `${item.salesOrg?.code}-${item.dc?.value?.code}`
    );
    const wareHouseID = orgRow?.map((item) => item?.warehouse?.value?.code);
    payload[dynamicKey] = {
      headerData: {
        id: dynamicKey,
        clientId: item?.ToBasicdata?.ClientId || null,
        AccountingId: getAccountingId(item?.Toaccountingdata) || null,
        included: true,
        industrySector: {
          code: selectedMaterialRow?.IndSector || "",
        },
        materialType: {
          code: selectedMaterialRow?.MatlType || "",
        },
        lineNumber: selectedMaterialRow.lineNumber ?? nextLineNumber() ?? null,
        materialNumber: selectedMaterialRow.Material || "",
        globalMaterialDescription:
          selectedMaterialRow.globalMaterialDescription || "",
        views: selectedViews,
        validated: VALIDATION_STATUS.default,
        orgData: orgRow || [],
      },
      payloadData: {
        "Basic Data": {
          basic: {
            BaseUom: item?.ToBasicdata?.BaseUom || "",
            MatlGroup: item?.ToBasicdata?.MatlGroup || "",
            MatGroupPackagingMat: item?.ToBasicdata?.MatGroupPackagingMat || "",
            Division: item?.ToBasicdata?.Division || "",
            ProdHier: item?.ToBasicdata?.ProdHier || "",
            VolumeUnit: item?.ToBasicdata?.VolumeUnit || "",
            UnitOfWt: item?.ToBasicdata?.UnitOfWt || "",
            GItemCat: item?.ToBasicdata?.GItemCat || "",
            BasicMatl: item?.ToBasicdata?.BasicMatl || "",
            Hazmatprof: item?.ToBasicdata?.Hazmatprof || "",
            OldMatNo: item?.ToBasicdata?.OldMatNo || "",
            Extmatlgrp: item?.ToBasicdata?.Extmatlgrp || "",
            DsnOffice: item?.ToBasicdata?.DsnOffice || "",
            PurValkey: item?.ToBasicdata?.PurValkey || "",
            NetWeight: item?.ToBasicdata?.NetWeight || "",
            HighVisc: item?.ToBasicdata?.HighVisc || "",
            IndSector: item?.ToBasicdata?.IndSector || "",
            PurStatus: item?.ToBasicdata?.PurStatus || "",
            HazMatNo: item?.ToBasicdata?.HazMatNo || "",
            StdDescr: item?.ToBasicdata?.StdDescr || "",
            ShelfLife: item?.ToBasicdata?.ShelfLife || "",
            ProdAlloc: item?.ToBasicdata?.ProdAlloc || "",
            CSalStatus: item?.ToBasicdata?.CSalStatus || "",
            IntlPoPrice: item?.ToBasicdata?.IntlPoPrice || "",
            PryVendor: item?.ToBasicdata?.PryVendor || "",
            PlanningArea: item?.ToBasicdata?.PlanningArea || "",
            PlanningFactor: item?.ToBasicdata?.PlanningFactor || "",
            ReturnMatNumber: item?.ToBasicdata?.ReturnMatNumber || "",
            ParentMatNumber: item?.ToBasicdata?.ParentMatNumber || "",
            DiversionControlFlag: item?.ToBasicdata?.DiversionControlFlag || "",
            Pvalidfrom:
              convertSAPDateForCalendar(item?.ToBasicdata?.Pvalidfrom) || "",
          },
        },
        Purchasing: (purchasingKey || []).reduce((acc, plant) => {
          plantID.forEach((id) => {
            acc[id] = {
              Plant: id,
              PurValkey: item?.ToBasicdata?.PurValkey || "",
              PurGroup: plant.PurGroup || "",
              PurStatus: plant.PurStatus || "",
              PlantId: plant?.PlantId || "",
              CommCode: plant.CommCode || "",
              CommCoUn: plant.CommCoUn || "",
              Countryori: plant.Countryori || "",
              IndPostToInspStock: plant.IndPostToInspStock || "",
              VarOrdUn: item?.ToBasicdata?.VarOrdUn || "",
              HtsCode: plant?.HtsCode || "",
            };
          });
          return acc;
        }, {}),
        Accounting: (Array.isArray(accountingKey) && accountingKey.length > 0
          ? accountingKey
          : [{}]
        ).reduce((acc, accounting) => {
          plantID.forEach((id) => {
            const accountingData = accounting[0] || {};
            acc[id] = {
              ...accountingData,
              ValClass: accountingData?.ValClass || "",
              PriceCtrl: accountingData?.PriceCtrl || "",
              PriceUnit: accountingData?.PriceUnit || "",
              StdPrice: accountingData?.StdPrice || "",
            };
          });
          return acc;
        }, {}),

        Costing: (Array.isArray(accountingKey) && accountingKey.length > 0
          ? accountingKey
          : [{}]
        ).reduce((acc, accounting) => {
          plantID.forEach((id) => {
            const accountingData = accounting[0] || {};
            acc[id] = {
              ...accountingData,
              OrigMat: accountingData?.OrigMat || "",
              LotSize: item?.Toplantdata?.LotSize || "",
              ProfitCtr: item?.Toplantdata?.ProfitCtr || "",
              VarianceKey: item?.Toplantdata?.VarianceKey || "",
              MovingPr: accountingData?.MovingPr || "",
              StdPrice: accountingData?.StdPrice || "",
            };
          });
          return acc;
        }, {}),
        MRP: (purchasingKey || []).reduce((acc, plant) => {
          plantID.forEach((id) => {
            acc[id] = {
              ...plant,
              MrpType: plant.MrpType || "",
              MrpCtrler: plant.MrpCtrler || "",
              ProcType: plant.ProcType || "",
              Backflush: plant.Backflush || "",
              PeriodInd: plant.PeriodInd || "",
              PlanStrgp: plant.PlanStrgp || "",
              Consummode: plant.Consummode || "",
              Availcheck: plant.Availcheck || "",
              DepReqId: plant.DepReqId || "",
              Lotsizekey: plant.Lotsizekey || "",
              SaftyTId: plant.SaftyTId || "",
              SmKey: plant.SmKey || "",
              MixedMrp: plant.MixedMrp || "",
              GrpReqmts: plant.GrpReqmts || "",
              Minlotsize: plant.Minlotsize || "",
              Maxlotsize: plant.Maxlotsize || "",
              FixedLot: plant.FixedLot || "",
              ReorderPt: plant.ReorderPt || "",
              MaxStock: plant.MaxStock || "",
              RoundVal: plant.RoundVal || "",
              AssyScarp: plant.AssyScarp || "",
              Spproctype: plant.Spproctype || "",
              SlocExprc: plant.SlocExprc || "",
              Inhseprodt: plant.Inhseprodt || "",
              PlndDelry: plant.PlndDelry || "",
              GrPrTime: plant.GrPrTime || "",
              Safetytime: plant.Safetytime || "",
              SafetyStk: plant.SafetyStk || "",
              BwdCons: plant.BwdCons || "",
              FwdCons: plant.FwdCons || "",
              Replentime: plant.Replentime || "",
              PlTiFnce: plant.PlTiFnce || "",
              IssStLoc: plant.IssStLoc || "",
              AssyScrap: plant.AssyScrap || "",
            };
          });
          return acc;
        }, {}),
        [MATERIAL_VIEWS.SALES_PLANT]: (purchasingKey || []).reduce(
          (acc, plant) => {
            plantID.forEach((id) => {
              acc[id] = {
                Matfrgtgrp: plant.Matfrgtgrp || "",
                ProfitCtr: item?.Toplantdata?.ProfitCtr || "",
                Loadinggrp: item?.Toplantdata?.Loadinggrp || "",
                Availcheck: plant.Availcheck || "",
              };
            });
            return acc;
          },
          {}
        ),
        [MATERIAL_VIEWS.SALES_GENERAL]: {
          [MATERIAL_VIEWS.SALES_GENERAL]: {
            TransGrp: item?.ToBasicdata?.TransGrp || "",
            BatchMgmt: item?.ToBasicdata?.BatchMgmt || false,
            XSalStatus: item?.ToBasicdata?.XSalStatus || "",
            Svalidfrom:
              convertSAPDateForCalendar(item?.Toclientdata?.Svalidfrom) || "",
          },
        },
        [MATERIAL_VIEWS.PURCHASING_GENERAL]: {
          [MATERIAL_VIEWS.PURCHASING_GENERAL]: {
            VarOrdUn: item?.ToBasicdata?.VarOrdUn || "",
            PurValkey: item?.ToBasicdata?.PurValkey || "",
          },
        },
        TaxData: {
          TaxData: {
            UniqueTaxDataSet: (item.Tocontroldata || []).reduce(
              (acc, controlData) => {
                const country = controlData.Depcountry;
                const ControlId = controlData.ControlId;
                Object.keys(controlData)
                  .filter((key) => key.startsWith("TaxType"))
                  .forEach((taxKey, index) => {
                    const taxType = controlData[taxKey];
                    const taxClass = controlData[`Taxclass${index + 1}`];

                    if (taxType && taxClass !== undefined) {
                      let existingEntry = acc.find(
                        (entry) =>
                          entry.Country === country && entry.TaxType === taxType
                      );

                      if (!existingEntry) {
                        existingEntry = {
                          Country: country,
                          TaxType: taxType,
                          ControlId: ControlId,
                          TaxClasses: [],
                          SelectedTaxClass: {
                            TaxClass: taxClass,
                            TaxClassDesc: `Description for ${taxClass}`,
                          },
                        };
                        acc.push(existingEntry);
                      }

                      existingEntry.SelectedTaxClass = {
                        TaxClass: taxClass,
                        TaxClassDesc: `Description for ${taxClass}`,
                      };
                    }
                  });

                return acc;
              },
              []
            ),
          },
        },
        [MATERIAL_VIEWS.WORKSCHEDULING]: (purchasingKey || []).reduce(
          (acc, plant) => {
            plantID.forEach((id) => {
              acc[id] = {
                Unlimited: plant.Unlimited || "",
                ProdProf: plant.ProdProf || "",
              };
            });
            return acc;
          },
          {}
        ),
        [MATERIAL_VIEWS.SALES]: ([item.Tosalesdata] || []).reduce(
          (acc, sale) => {
            salesID.forEach((id) => {
              acc[id] = {
                ...sale,
                SalStatus: sale.SalStatus || "",
                SalesId: sale.SalesId,
                MatPrGrp: sale.MatPrGrp || "",
                DelygPlnt: sale.DelygPlnt || "",
                RebateGrp: sale.RebateGrp || "",
                DelyUom: sale.DelyUom || "",
                ValidFrom: convertSAPDateForCalendar(sale.ValidFrom) || null,
              };
            });
            return acc;
          },
          {}
        ),
        [MATERIAL_VIEWS.WAREHOUSE]: (wareHouseKey || []).reduce(
          (acc, warehouse) => {
            wareHouseID.forEach((id) => {
              acc[id] = {
                ...warehouse,
                Placement: warehouse.Placement || "",
              };
            });
            return acc;
          },
          {}
        ),
        [MATERIAL_VIEWS.SALES_PLANT]: (purchasingKey || []).reduce(
          (acc, plant) => {
            plantID.forEach((id) => {
              acc[id] = {
                Matfrgtgrp: plant.Matfrgtgrp || "",
                ProfitCtr: plant.ProfitCtr || "",
                Loadinggrp: plant.Loadinggrp || "",
                Availcheck: plant.Availcheck || "",
              };
            });
            return acc;
          },
          {}
        ),
        [MATERIAL_VIEWS.STORAGE]: ([item.Tostroragelocationdata] || []).reduce(
          (acc, storage) => {
            storageID.forEach((id) => {
              acc[id] = {
                ...storage,
              };
            });
            return acc;
          },
          {}
        ),
        [MATERIAL_VIEWS.STORAGE_PLANT]: (purchasingKey || []).reduce(
          (acc, plant) => {
            plantID.forEach((id) => {
              acc[id] = {
                CcPhInv: plant?.CcPhInv || "",
                CcFixed: plant?.CcFixed || "",
                MaxStock: plant?.MaxStock || "",
                StgePdUn: plant?.StgePdUn || "",
                DefaultStockSegment: plant?.DefaultStockSegment || "",
                NegStocks: plant?.NegStocks || "",
                SernoProf: plant?.SernoProf || "",
                Loadinggrp: plant?.Loadinggrp || "",
                DistrProf: plant?.DistrProf || "",
                DetermGrp: plant?.DetermGrp || "",
                IuidRelevant: plant?.IuidRelevant || "",
                UidIea: plant?.UidIea || "",
                IuidType: plant?.IuidType || "",
                SortStockBasedOnSegment: plant?.SortStockBasedOnSegment || "",
              };
            });
            return acc;
          },
          {}
        ),
        [MATERIAL_VIEWS.STORAGE_GENERAL]: {
          [MATERIAL_VIEWS.STORAGE_GENERAL]: {
            HazMatNo: item?.ToBasicdata?.HazMatNo || "",
            QtyGrGi: item?.ToBasicdata?.QtyGrGi || "",
            TempConds: item?.ToBasicdata?.TempConds || "",
            Container: item?.ToBasicdata?.Container || "",
            LabelType: item?.ToBasicdata?.LabelType || "",
            LabelForm: item?.ToBasicdata?.LabelForm || "",
            AppdBRec: item?.ToBasicdata?.AppdBRec || "",
            Minremlife: item?.ToBasicdata?.Minremlife || "",
            ShelfLife: item?.ToBasicdata?.ShelfLife || "",
            PeriodIndExpirationDate:
              item?.ToBasicdata?.PeriodIndExpirationDate || "",
            RoundUpRuleExpirationDate:
              item?.ToBasicdata?.RoundUpRuleExpirationDate || "",
            StorPct: item?.ToBasicdata?.StorPct || "",
            SledBbd: item?.ToBasicdata?.SledBbd || "",
            SerializationLevel: item?.ToBasicdata?.SerializationLevel || "",
            MaturityDur: item?.ToBasicdata?.MaturityDur || "",
            ShelfLifeReqMax: item?.ToBasicdata?.ShelfLifeReqMax || "",
            ShelfLifeReqMin: item?.ToBasicdata?.ShelfLifeReqMin || "",
            StorConds: item?.ToBasicdata?.StorConds || "",
          },
        },
      },

      unitsOfMeasureData:
        (item?.Touomdata || []).length === 0
          ? []
          : item?.Touomdata.map((uom, index) => ({
              ...uom,
              id: index,
              UomId: uom?.UomId,
              xValue: uom?.Denominatr || "",
              aUnit: uom?.AltUnit || "",
              measureUnitText: uom?.measureUnitText || "",
              yValue: uom?.Numerator || "",
              bUnit: uom?.bUnit || "",
              measurementUnitText: uom?.measurementUnitText || "",
              eanUpc: uom?.EanUpc || "",
              eanCategory: uom?.EanCat || "",
              autoCheckDigit: uom?.autoCheckDigit || "",
              addEans: uom?.addEans || "",
              length: uom?.Length || "",
              width: uom?.Width || "",
              height: uom?.Height || "",
              unitsOfDimension: uom?.UnitDim || "",
              volume: uom?.Volume || "",
              volumeUnit: uom?.Volumeunit || "",
              grossWeight: uom?.GrossWt || "",
              netWeight: uom?.NetWeight || item.ToBasicdata.NetWeight || "",
              weightUnit: uom?.UnitOfWt || "",
              noLowerLvlUnits: uom?.noLowerLvlUnits || "",
              lowerLvlUnits: uom?.lowerLvlUnits || "",
              remVolAfterNesting: uom?.remVolAfterNesting || "",
              maxStackFactor: uom?.maxStackFactor || "",
              maxTopLoadFullPkg: uom?.maxTopLoadFullPkg || "",
              UomToploadFullPkg: uom?.UomToploadFullPkg || "",
              capacityUsage: uom?.CapacityUsage || "",
              UomCategory: uom?.UomCategory || "",
            })),
      additionalData:
        (item?.Tomaterialdescription || []).length === 0
          ? []
          : (item?.Tomaterialdescription || []).map((addData, index) => ({
              id: index,
              MaterialDescriptionId: addData?.MaterialDescriptionId,
              Function: addData?.Function,
              Material: addData?.Material,
              language: addData?.Langu,
              materialDescription: addData?.MatlDesc,
              DelFlag: addData?.DelFlag,
            })),
      Tomaterialerrordata: item?.Tomaterialerrordata || {},
    };
  });
  let filteredPayload = {};
  Object.keys(payload).forEach((key) => {
    if (key.includes("-") || /\d/.test(key)) {
      filteredPayload[key] = payload[key];
    }
  });

  return filteredPayload;
};
export const transformResponseForCreateRef = (
  apiResponse,
  initialPayload = {}
) => {
  let region = initialPayload?.Region;
  let copyPayload = {};
  apiResponse.forEach((item) => {
    copyPayload = {
      payloadData: {
        "Basic Data": {
          basic: {
            BaseUom: item?.ToBasicdata?.BaseUom || "",
            MatlGroup: item?.ToBasicdata?.MatlGroup || "",
            Division:
              item?.Torequestheaderdata?.Division ||
              item?.ToBasicdata?.Division ||
              "",
            ProdHier: item?.ToBasicdata?.ProdHier || "",
            VolumeUnit: item?.ToBasicdata?.VolumeUnit || "",
            UnitOfWt: item?.ToBasicdata?.UnitOfWt || "",
            GItemCat: item?.ToBasicdata?.GItemCat || "",
            BasicMatl: item?.ToBasicdata?.BasicMatl || "",
            Hazmatprof: item?.ToBasicdata?.Hazmatprof || "",
            OldMatNo: item?.ToBasicdata?.OldMatNo || "",
            Extmatlgrp: item?.ToBasicdata?.Extmatlgrp || "",
            DsnOffice: item?.ToBasicdata?.DsnOffice || "",
            NetWeight: item?.ToBasicdata?.NetWeight || "",
            HighVisc: item?.ToBasicdata?.HighVisc || "",
            IndSector: item?.ToBasicdata?.IndSector || "",
            HazMatNo: item?.ToBasicdata?.HazMatNo || "",
            StdDescr: item?.ToBasicdata?.StdDescr || "",
            ShelfLife: item?.ToBasicdata?.ShelfLife || "",
            CSalStatus: item?.ToBasicdata?.CSalStatus || "",
            IntlPoPrice: item?.ToBasicdata?.IntlPoPrice || "",
            PryVendor: item?.ToBasicdata?.PryVendor || "",
            PlanningArea: item?.ToBasicdata?.PlanningArea || "",
            PlanningFactor: item?.ToBasicdata?.PlanningFactor || "",
            ReturnMatNumber: item?.ToBasicdata?.ReturnMatNumber || "",
            ParentMatNumber: item?.ToBasicdata?.ParentMatNumber || "",
            DiversionControlFlag: item?.ToBasicdata?.DiversionControlFlag || "",
            MatGroupPackagingMat: item?.ToBasicdata?.MatGroupPackagingMat || "",
            ProdAlloc: item?.ToBasicdata?.ProdAlloc || "",
            Pvalidfrom:
              convertSAPDateForCalendar(item?.ToBasicdata?.Pvalidfrom) || "",
          },
        },
        ManufacturerID: item?.ManufacturerId || "",
        Purchasing: item?.Toplantdata
          ? {
              PurGroup: item?.Toplantdata?.PurGroup || "",
              PurStatus: item?.Toplantdata?.PurStatus || "",
              PlantId: item?.Toplantdata?.PlantId,
              CommCode: item?.Toplantdata?.CommCode || "",
              CommCoUn: item?.Toplantdata?.CommCoUn || "",
              Countryori: item?.Toplantdata?.Countryori || "",
              IndPostToInspStock: item?.Toplantdata?.IndPostToInspStock || "",
              HtsCode: item?.Toplantdata?.HtsCode || "",
            }
          : {},
        Accounting: item?.Toaccountingdata?.length
          ? {
              AccountingId: item?.Toaccountingdata[0]?.AccountingId || "",
              ValClass: item?.Toaccountingdata[0]?.ValClass || "",
              PriceCtrl: item?.Toaccountingdata[0]?.PriceCtrl || "",
              PriceUnit: item?.Toaccountingdata[0]?.PriceUnit || "",
              StdPrice: item?.Toaccountingdata[0]?.StdPrice || "",
            }
          : {},
        Costing: item.Toaccountingdata?.length
          ? {
              AccountingId: item.Toaccountingdata[0]?.AccountingId || "",
              OrigMat: item.Toaccountingdata[0]?.OrigMat || "",
              LotSize: item.Toplantdata?.LotSize || "",
              VarianceKey: item.Toplantdata?.VarianceKey || "",
              MovingPr: item.Toaccountingdata[0]?.MovingPr || "",
              StdPrice: item.Toaccountingdata[0]?.StdPrice || "",
              ProfitCtr: item.Toplantdata?.ProfitCtr,
            }
          : {},
        MRP: item.Toplantdata
          ? {
              MrpType: item.Toplantdata?.MrpType || "",
              IssStLoc: item.Toplantdata?.IssStLoc || "",
              MrpCtrler: item.Toplantdata?.MrpCtrler || "",
              ProcType: item.Toplantdata?.ProcType || "",
              Backflush: item.Toplantdata?.Backflush || "",
              PeriodInd: item.Toplantdata?.PeriodInd || "",
              PlanStrgp: item.Toplantdata?.PlanStrgp || "",
              Consummode: item.Toplantdata?.Consummode || "",
              Availcheck: item.Toplantdata?.Availcheck || "",
              DepReqId: item.Toplantdata?.DepReqId || "",
              Lotsizekey: item.Toplantdata?.Lotsizekey || "",
              SaftyTId: item.Toplantdata?.SaftyTId || "",
              SmKey: item.Toplantdata?.SmKey || "",
              MixedMrp: item.Toplantdata?.MixedMrp || "",
              GrpReqmts: item.Toplantdata?.GrpReqmts || "",
              Minlotsize: item.Toplantdata?.Minlotsize || "",
              Maxlotsize: item.Toplantdata?.Maxlotsize || "",
              FixedLot: item.Toplantdata?.FixedLot || "",
              ReorderPt: item.Toplantdata?.ReorderPt || "",
              MaxStock: item.Toplantdata?.MaxStock || "",
              RoundVal: item.Toplantdata?.RoundVal || "",
              AssyScarp: item.Toplantdata?.AssyScarp || "",
              Spproctype: item.Toplantdata?.Spproctype || "",
              SlocExprc: item.Toplantdata?.SlocExprc || "",
              Inhseprodt: item.Toplantdata?.Inhseprodt || "",
              PlndDelry: item.Toplantdata?.PlndDelry || "",
              GrPrTime: item.Toplantdata?.GrPrTime || "",
              Safetytime: item.Toplantdata?.Safetytime || "",
              SafetyStk: item.Toplantdata?.SafetyStk || "",
              BwdCons: item.Toplantdata?.BwdCons || "",
              FwdCons: item.Toplantdata?.FwdCons || "",
              Replentime: item.Toplantdata?.Replentime || "",
              PlTiFnce: item.Toplantdata?.PlTiFnce || "",
              AssyScrap: item.Toplantdata?.AssyScrap || "",
            }
          : {},
        "Work Scheduling": item.Toplantdata
          ? {
              Unlimited: item.Toplantdata?.Unlimited || "",
              ProdProf: item.Toplantdata?.ProdProf || "",
            }
          : {},
        Sales: item.Tosalesdata
          ? {
              ...item.Tosalesdata,
              SalStatus: item.Tosalesdata.SalStatus || "",
              SalesId: item.Tosalesdata.SalesId,
              MatPrGrp: item.Tosalesdata.MatPrGrp || "",
              ValidFrom:
                convertSAPDateForCalendar(item.Tosalesdata.ValidFrom) || null,
            }
          : {},
        "Sales-General": {
          "Sales-General": {
            TransGrp: item?.ToBasicdata?.TransGrp || "",
            BatchMgmt: item?.ToBasicdata?.BatchMgmt || false,
            XSalStatus: item?.ToBasicdata?.XSalStatus || "",
            Svalidfrom:
              convertSAPDateForCalendar(item?.ToBasicdata?.Svalidfrom) || null,
          },
        },
        "Purchasing-General": {
          "Purchasing-General": {
            VarOrdUn: item?.ToBasicdata?.VarOrdUn || "",
            PurValkey: item?.ToBasicdata?.PurValkey || "",
          },
        },
        TaxData: {
          TaxData: {
            UniqueTaxDataSet: (item.Tocontroldata || []).reduce(
              (acc, controlData) => {
                const country = controlData.Depcountry;
                const ControlId = controlData.ControlId;
                Object.keys(controlData)
                  .filter((key) => key.startsWith("TaxType"))
                  .forEach((taxKey, index) => {
                    const taxType = controlData[taxKey];
                    const taxClass = controlData[`Taxclass${index + 1}`];

                    if (taxType && taxClass !== undefined) {
                      let existingEntry = acc.find(
                        (entry) =>
                          entry.Country === country && entry.TaxType === taxType
                      );

                      if (!existingEntry) {
                        existingEntry = {
                          Country: country,
                          TaxType: taxType,
                          ControlId: ControlId,
                          TaxClasses: [],
                          SelectedTaxClass: {
                            TaxClass: taxClass,
                            TaxClassDesc: `Description for ${taxClass}`,
                          },
                        };
                        acc.push(existingEntry);
                      }

                      existingEntry.SelectedTaxClass = {
                        TaxClass: taxClass,
                        TaxClassDesc: `Description for ${taxClass}`,
                      };
                    }
                  });

                return acc;
              },
              []
            ),
          },
        },
        [MATERIAL_VIEWS.SALES_PLANT]: item.Toplantdata
          ? {
              Matfrgtgrp: item.Toplantdata?.Matfrgtgrp || "",
              ProfitCtr: item.Toplantdata?.ProfitCtr || "",
              Loadinggrp: item.Toplantdata?.Loadinggrp || "",
              Availcheck: item.Toplantdata?.Availcheck || "",
            }
          : {},
        [MATERIAL_VIEWS.WAREHOUSE]: item.ToWarehousedata
          ? {
              ...item.ToWarehousedata,
            }
          : {},
        [MATERIAL_VIEWS.STORAGE]: item.Tostroragelocationdata
          ? {
              ...item.Tostroragelocationdata,
            }
          : {},
        [MATERIAL_VIEWS.STORAGE_PLANT]: item?.Toplantdata
          ? {
              CcPhInv: item?.Toplantdata?.CcPhInv || "",
              CcFixed: item?.Toplantdata?.CcFixed || "",
              MaxStock: item?.Toplantdata?.MaxStock || "",
              StgePdUn: item?.Toplantdata?.StgePdUn || "",
              DefaultStockSegment: item?.Toplantdata?.DefaultStockSegment || "",
              NegStocks: item?.Toplantdata?.NegStocks || false,
              SernoProf: item?.Toplantdata?.SernoProf || "",
              // Loadinggrp : item?.Toplantdata?.Loadinggrp || "", already there in salesPlantView
              DistrProf: item?.Toplantdata?.DistrProf || "",
              DetermGrp: item?.Toplantdata?.DetermGrp || "",
              IuidRelevant: item?.Toplantdata?.IuidRelevant || false,
              UidIea: item?.Toplantdata?.UidIea || "",
              IuidType: item?.Toplantdata?.IuidType || "",
            }
          : {},
        [MATERIAL_VIEWS.STORAGE_GENERAL]: {
          [MATERIAL_VIEWS.STORAGE_GENERAL]: {
            HazMatNo: item?.ToBasicdata?.HazMatNo || "",
            QtyGrGi: item?.ToBasicdata?.QtyGrGi || "",
            TempConds: item?.ToBasicdata?.TempConds || "",
            Container: item?.ToBasicdata?.Container || "",
            LabelType: item?.ToBasicdata?.LabelType || "",
            LabelForm: item?.ToBasicdata?.LabelForm || "",
            AppdBRec: item?.ToBasicdata?.AppdBRec || "",
            Minremlife: item?.ToBasicdata?.Minremlife || "",
            ShelfLife: item?.ToBasicdata?.ShelfLife || "",
            PeriodIndExpirationDate:
              item?.ToBasicdata?.PeriodIndExpirationDate || "",
            RoundUpRuleExpirationDate:
              item?.ToBasicdata?.RoundUpRuleExpirationDate || "",
            StorPct: item?.ToBasicdata?.StorPct || "",
            SledBbd: item?.ToBasicdata?.SledBbd || "",
            SerializationLevel: item?.ToBasicdata?.SerializationLevel || "",
            MaturityDur: item?.ToBasicdata?.MaturityDur || "",
            ShelfLifeReqMax: item?.ToBasicdata?.ShelfLifeReqMax || "",
            ShelfLifeReqMin: item?.ToBasicdata?.ShelfLifeReqMin || "",
            StorConds: item?.ToBasicdata?.StorConds || "",
          },
        },
      },
      //will remove after testing this mapping is not required
      // eanData:
      //   (item?.Toeandata || []).length === 0
      //     ? []
      //     : item?.Toeandata.map((ean, index) => ({
      //       ...ean,
      //         id: ean?.EanId,
      //         EanId: ean?.EanId,
      //         altunit: ean?.Unit,
      //         eanUpc: ean?.EanUpc,
      //         eanCategory: ean?.EanCat,
      //         MainEan: ean?.MainEan,
      //       })),
      unitsOfMeasureData:
        (item?.Touomdata || []).length === 0
          ? []
          : item?.Touomdata.map((uom, index) => {
              const baseData = {
                ...uom,
                id: uom?.UomId,
                UomId: uom?.UomId,
                xValue: uom?.Denominatr,
                aUnit: uom?.AltUnit,
                measureUnitText: uom?.measureUnitText,
                yValue: uom?.Numerator,
                bUnit: uom?.bUnit,
                measurementUnitText: uom?.measurementUnitText,
                eanCategory:
                  region === REGION_CODE?.US ? uom?.EanCat || "" : "",
                eanUpc:
                  region === REGION_CODE?.US &&
                  uom?.EanCat === EAN_CATEGORIES?.MB
                    ? "" || uom?.EanUpc
                    : "",
                autoCheckDigit: uom?.autoCheckDigit,
                addEans: uom?.addEans,
                unitsOfDimension: uom?.UnitDim,
                volumeUnit: uom?.Volumeunit || "",

                weightUnit: uom?.UnitOfWt,
                noLowerLvlUnits: uom?.noLowerLvlUnits,
                lowerLvlUnits: uom?.lowerLvlUnits,
                remVolAfterNesting: uom?.remVolAfterNesting,
                maxStackFactor: uom?.maxStackFactor,
                maxTopLoadFullPkg: uom?.maxTopLoadFullPkg,
                UomToploadFullPkg: uom?.UomToploadFullPkg,
                capacityUsage: uom?.CapacityUsage,
                UomCategory: uom?.UomCategory,
                length: uom?.Length,
                width: uom?.Width,
                height: uom?.Height,
                volume: uom?.Volume,
                grossWeight: uom?.GrossWt,
              };

              return baseData;
            }),
      additionalData:
        (item?.Tomaterialdescription || []).length === 0
          ? []
          : (item?.Tomaterialdescription || []).map((addData) => ({
              id: addData?.MaterialDescriptionId,
              MaterialDescriptionId: addData?.MaterialDescriptionId,
              Function: addData?.Function,
              Material: addData?.Material,
              language: addData?.Langu,
              materialDescription: addData?.MatlDesc,
              DelFlag: addData?.DelFlag,
            })),
    };
  });
  return { copyPayload };
};

export const transformApiResponseToReduxPayloadPc = (apiResponse) => {
  const requestHeaderData = apiResponse[0]?.Torequestheaderdata;
 
  let rowsBodyData = {};
  let rowsHeaderData = [];
 
  // Only proceed if response has valid ProfitCenterID
  if (Array.isArray(apiResponse) && apiResponse.length > 0) {
    apiResponse.forEach((data) => {
      const dynamicKey = data?.ProfitCenterID?.toString();
      if (dynamicKey) {
        rowsBodyData[dynamicKey] = {
          ...data,
          ...(data?.ToCompanycode?.[0] || {}),
        };
      }
    });
 
    // Build header rows only from valid ProfitCenterID data
    rowsHeaderData = apiResponse
      .filter((item) => item.ProfitCenterID)
      .map((item) => ({
        id: item.ProfitCenterID,
        controllingArea: item.COArea || "",
        profitCenterNumber: item.ProfitCenter || "",
        ProfitCenterID: item.ProfitCenterID || "",
        longDescription: item.Description || "",
        businessSegment: item.ToGeneralInfoData?.BusinessSegment || "",
        companyCode: item.ToCompanycode?.[0]?.CompanyCode || "",
        included: true,
      }));
  }
 
  return {
    payload: {
      requestHeaderData,
      rowsHeaderData,
      rowsBodyData,
    },
  };
};
 
export const transformApiResponseToReduxPayloadCc = (apiResponse) => {
  const requestHeaderData = apiResponse[0]?.Torequestheaderdata;
  let rowsBodyData = {};
  let rowsHeaderData = [];
    if (Array.isArray(apiResponse) && apiResponse.length > 0) {
    apiResponse.forEach((data) => {
      const dynamicKey = data?.ToCostCenterData?.[0]?.CostCenterID?.toString();
      if (dynamicKey) {
        rowsBodyData[dynamicKey] = {
          ...data,
          ...(data?.ToCostCenterData?.[0] || {}),
        };
      }
    });
 
    // Build header rows only from valid ProfitCenterID data
    rowsHeaderData = apiResponse
    .filter((item) => item.ToCostCenterData?.[0]?.CostCenterID)
    .map((item, index) => ({
        id: item?.ToCostCenterData?.[0]?.CostCenterID || "",
        controllingArea: item.ControllingArea || "",
        costCenterNumber: item?.ToCostCenterData?.[0]?.Costcenter || "",
        CostCenterID: item?.ToCostCenterData?.[0]?.CostCenterID || "",
        longDescription: item?.ToCostCenterData?.[0]?.Descript || "",
        CompCode: item?.ToCostCenterData?.[0]?.CompCode || "",
        included: true,
      }));
  }
  const payload = {
    requestHeaderData,
    rowsHeaderData,
    rowsBodyData,
  };
  return { payload };
};
 
export const transformApiResponseToReduxPayloadGl = (apiResponse) => {
  const requestHeaderData = apiResponse[0]?.Torequestheaderdata;

  let rowsBodyData = {}
  // let count =0
  apiResponse?.forEach((data) => {
    const dynamicKey = data?.GeneralLedgerID ?? "";
    rowsBodyData[dynamicKey] = {
      ...data,
    };
  });

  const rowsHeaderData = apiResponse.map((item, index) => ({
    id: item?.GeneralLedgerID || "",
    chartOfAccount: item?.COA || "",
    companyCode: item?.CompanyCode || "",
    accountType: item?.Accounttype || "",
    accountGroup: item?.AccountGroup || "",
    glAccountNumber: item?.GLAccount || "",
    businessSegment:
      item?.["ToGeneralInfoData"]?.["BusinessSegment"] ,
    longDescription: item?.Description || "",
    shortDescription: item?.GLname || "",
    lineNumber:index+1,
    included: true,
  }));

  const payload = {
    requestHeaderData,
    rowsHeaderData,
    rowsBodyData,
  };

  return { payload };
};

export const createPayloadForPC = (
  reduxPayload,
  requestHeaderSlice,
  isrequestId,
  task,
  dynamicData,
  createChangeLogData
) => {
  
  const { requestHeaderData, rowsHeaderData, rowsBodyData } = reduxPayload;

  const finalPayload = rowsHeaderData?.map((row) => {
    const rowId = row.id;
    const body = rowsBodyData[rowId] || {};
    const changelogDta = createChangeLogData?.[rowId] ?? {};

    return {
      requestInProcess: "",
      ProfitCenterID: body?.ProfitCenterID ?? "",
      TemplateName: requestHeaderData?.TemplateName || "",
      TemplateHeaders: "",
      IsScheduled: false,
      Action: "I",
      RequestID: "",
      TaskStatus: "",
      TaskId: task?.taskId || "",
      ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
      ReqCreatedOn: `/Date(${new Date(
        requestHeaderData?.ReqCreatedOn
      ).getTime()})/`,
      RequestStatus: requestHeaderData?.RequestStatus || "",
      CreationId: "",
      EditId: "",
      DeleteId: "",
      MassCreationId: body?.MassCreationId ?? "",
      MassEditId: "",
      MassDeleteId: "",
      ProfitCenterErrorID: body?.ProfitCenterErrorId ?? "",
      RequestType:
        requestHeaderData?.RequestType === "Create"
          ? "Mass Create"
          : body?.RequestType || "",
      MassRequestStatus: "",
      Remarks: dynamicData?.Comments ?? "",
      TempLockRemarks: null,
      Info: "",
      ChangedFields: "",
      ProfitCenterName: body?.ProfitCenterName || "",
      Description: body?.Description || "",
      UserResponsible: body?.UserResponsible || "",
      PersonResponsible: body?.PersonResponsible || "",
      Department: body?.Department || "",
      PrctrHierGrp: body?.PrctrHierGrp || "",
      Segment: body?.Segment || "",
      LockIndicator: body?.LockIndicator ? "X" : "",
      Template: body?.Template || "",
      Title: body?.Title || "",
      Name1: body?.Name1 || "",
      Name2: body?.Name2 || "",
      Name3: body?.Name3 || "",
      Name4: body?.Name4 || "",
      Street: body?.Street || "",
      City: body?.City || "",
      District: body?.District || "",
      Country: body?.Country || "",
      Taxjurcode: body?.TaxJurisdiction || "",
      PoBox: body?.PoBox || "",
      PostalCode: body?.PostalCode || "",
      PobxPcd: body?.PobxPcd || "",
      Regio: body?.Regio || "",
      Language: body?.Language || "",
      Telephone: body?.Telephone || "",
      Telephone2: body?.Telephone2 || "",
      Telebox: body?.Telebox || "",
      Telex: body?.Telex || "",
      FaxNumber: body?.FaxNumber || "",
      Teletex: body?.Teletex || "",
      Printer: body?.Printer || "",
      DataLine: body?.DataLine || "",
      ProfitCenter: row?.profitCenterNumber ?? body?.ProfitCenter ?? "",
      // COArea: row?.controllingArea?.code ?? body?.COArea ?? "",
      COArea: row?.controllingArea || body?.COArea || "",
      ValidfromDate: body?.ValidfromDate
        ? `/Date(${new Date(body.ValidfromDate).getTime()})/`
        : "/Date(-2208988800000)/",
      ValidtoDate: body?.ValidtoDate
        ? `/Date(${new Date(body.ValidtoDate).getTime()})/`
        : "/Date(32503680000000)/",
      Testrun: null,
      IsFirstSynCompleted: false,
      TempLockIsSelectedForSyn: false,
      SelectedByRequestorToDisplay: row.included,
      IsSunoco: false,
      Countryiso: "",
      LanguIso: "",
      Logsystem: "",
      GeneralInfoID: null,
      RequestPriority: requestHeaderData?.RequestPriority || "",
      BusinessJustification: null,
      SAPorJEErrorCheck: null,
      // BusinessSegment:body?.ToGeneralInfoData?.BusinessSegment ?? "",
      BusinessSegment: "",
      HierarchyRegion: null,
      PCAAMNumber: "",
      ValidationDoneBy: "",
      TotalIntermediateTasks: body?.TotalIntermediateTasks ?? "",

      ToChildHeaderdata: {
        RequestId: body?.MassCreationId ?? "",
        Status: requestHeaderData?.RequestStatus || "",
        IntermediateTaskCount: null,
        TotalTaskCount: null,
        BifurcatedValue: "",
        TaskId: task?.taskId||"",

        CurrentLevel: task?.ATTRIBUTE_3 || 0,
        CurrentLevelName: task?.ATTRIBUTE_4||"",
        IsScheduled: "",
        ParticularLevel: "",
        TaskCreatedOn: "",
        TaskName: "",

        CreatedOn: "2025-06-05T06:34:55.995+00:00",
        UpdatedOn: "2025-06-05T06:34:58.653+00:00",
        RequestType: "Mass Create",
      },
      ToCompanycode: [
        {
          CompCodeID: body?.CompCodeID ?? "",
          CompanyCode: body?.CompanyCode || row?.companyCode?.code || "",
          CompanyName: body?.CompanyName || row?.companyCode?.desc || "",
          AssignToPrctr: body?.AssignToPrctr ? "X" : "",
          Venture: "",
          RecInd: "",
          EquityTyp: "",
          JvOtype: "",
          JvJibcl: "",
          JvJibsa: "",
        },
      ],
      Torequestheaderdata: {
        RequestId:
          requestHeaderSlice?.requestId ?? requestHeaderData?.RequestId ?? "",
        ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
        ReqCreatedOn: `/Date(${new Date(
          requestHeaderData?.ReqCreatedOn
        ).getTime()})/`,
        ReqUpdatedOn: `/Date(${new Date(
          requestHeaderData?.ReqUpdatedOn
        ).getTime()})/`,
        RequestType: requestHeaderData?.RequestType || "",
        RequestPrefix: requestHeaderSlice?.requestPrefix
          ? requestHeaderSlice?.requestPrefix
          : "",
        RequestPriority: requestHeaderData?.RequestPriority || "",
        RequestDesc: requestHeaderData?.RequestDesc || "",
        RequestStatus: requestHeaderData?.RequestStatus || "",
        FirstProd: "",
        LaunchDate: "",
        LeadingCat: "Anesthesia/Pain Management",
        Division: "00",
        TemplateName: requestHeaderData?.TemplateName || "",
        FieldName: "",
        Region: "",
        FilterDetails: null,
        IsBifurcated: null,
      },
      TochangeLogData: {
        ChangeLogData: Array.isArray(changelogDta) ? changelogDta : [],
        ChangeLogId: body?.TochangeLogData?.ChangeLogId ?? "",
        RequestHeaderId: body?.TochangeLogData?.RequestHeaderId ?? "",
        RequestId: body?.TochangeLogData?.RequestId ?? "",
      },
      ToProfitCenterErrorData:
        isrequestId && !row?.isNew
          ? body?.ToProfitCenterErrorData
          : {
              ProfitCenterErrorId: null,
              RequestId: "",
              ProfitCenter: "",
              CompanyCode: "",
              Segment: "",
              PcAamNumber: "",
              ControllingArea: "",
              SapMessage: "",
              ObjectSapError: "",
              ObjectDbError: "",
              ObjectExcelError: "",
              ShortDescSapError: "",
              ShortDescDbError: "",
              ShortDescExcelError: "",
              LongDescSapError: "",
              LongDescDbError: "",
              LongDescExcelError: "",
              AddrValidationError: "",
              PersonResponsibleError: "",
              DmsAttachmentErrorStatus: "",
              SapAttachmentErrorStatus: "",
              RemovalNodeErrorStatus: "",
              AddNodeErrorStatus: "",
            },
      ToGeneralInfoData: {
        GeneralInfoId: body?.ToGeneralInfoData?.GeneralInfoId ?? "",
        RequestPriority: requestHeaderData?.RequestPriority || "",
        BusinessJustification: "",
        SAPorJEErrorCheck: "",
        // BusinessSegment:body?.ToGeneralInfoData?.BusinessSegment ?? "",
        BusinessSegment:
          row?.businessSegment?.code ??
          body?.ToGeneralInfoData?.BusinessSegment ??
          "",
        Region: "",
      },
    };
  });
  return finalPayload;
};

export const changePayloadForPC = (
  requestHeaderSlice,
  taskData,
  reqBench,
  fetchReqBenchData,
  fetchedProfitCenterData
) => {
  const allData = reqBench ? fetchReqBenchData : fetchedProfitCenterData;

  const Payload = allData.map((pcData) => {
    return {
      requestInProcess: "",
      ProfitCenterID: pcData.ProfitCenterID || "",
      TemplateName: requestHeaderSlice?.templateName || "",
      TemplateHeaders: "",
      IsScheduled: null,
      Action: "",
      RequestID: "",
      TaskStatus: null,
      TaskId: taskData?.taskId,
      ReqCreatedBy: pcData?.createdBy || "",
      ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
      RequestStatus: requestHeaderSlice?.requestStatus || "",
      CreationId: "",
      EditId: "",
      DeleteId: "",
      MassCreationId: "",
      MassEditId: pcData?.MassEditId || "",
      MassDeleteId: "",
      ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
      RequestType: requestHeaderSlice?.requestType || "",
      MassRequestStatus: "",
      Remarks: null,
      TempLockRemarks: null,
      Info: "",
      ChangedFields: "",
      ProfitCenterName: pcData?.profitCenterName || "",
      Description: pcData?.description || "",
      UserResponsible: pcData?.userResponsible || "",
      PersonResponsible: pcData?.personResponsible || "",
      Department: pcData?.Department || "",
      PrctrHierGrp: "ET_PCA",
      Segment: pcData?.segment || "",
      // LockIndicator: pcData?.lockIndicator ? "X" : "",
      LockIndicator: "",
      Template: "",
      Title: pcData?.addressTabDto?.Title || "",
      Name1: pcData?.name1 || "",
      Name2: pcData?.name2 || "",
      Name3: pcData?.name3 || "",
      Name4: pcData?.name4 || "",
      Street: pcData?.street || "",
      City: pcData?.city || "",
      District: pcData?.district || "",
      Country: pcData?.country || "",
      Taxjurcode: pcData?.TaxJurisdiction || "",
      PoBox: pcData?.PoBox || "",
      PostalCode: pcData?.pocode || pcData?.PostalCode || "",
      PobxPcd: pcData?.PobxPcd || "",
      Regio: pcData?.region || "",
      Language: pcData?.communicationTabDto?.Language || "",
      Telephone: pcData?.communicationTabDto?.Telephone || "",
      Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
      Telebox: pcData?.communicationTabDto?.Telebox || "",
      Telex: pcData?.communicationTabDto?.Telex || "",
      FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
      Teletex: pcData?.communicationTabDto?.Teletex || "",
      Printer: pcData?.communicationTabDto?.Printer || "",
      DataLine: pcData?.communicationTabDto?.DataLine || "",
      ProfitCenter: pcData?.profitCenter || "",
      COArea: pcData?.controllingArea || "ETCA",
      ValidfromDate:
        pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
      ValidtoDate:
        pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
      Testrun: null,
      IsFirstSynCompleted: false,
      TempLockIsSelectedForSyn: false,
      SelectedByRequestorToDisplay: true,
      IsSunoco: false,
      Countryiso: "",
      LanguIso: "",
      Logsystem: "",
      GeneralInfoID: null,
      RequestPriority: requestHeaderSlice?.requestPriority || "",
      BusinessJustification: null,
      SAPorJEErrorCheck: null,
      BusinessSegment: pcData.businessSegment || "CRUDE",
      HierarchyRegion: "ET_PCA",
      PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
      ValidationDoneBy: taskData?.taskId ? "MDM Approval" : "Requestor",
      TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

       ToChildHeaderdata: {
          RequestId: pcData.RequestID ?? "",
          Status: requestHeaderSlice?.requestStatus || "",
          IntermediateTaskCount: null,
          TotalTaskCount: null,
          BifurcatedValue: "",
          TaskId: taskData?.taskId,
          CreatedOn: "2025-06-05T06:34:55.995+00:00",
          UpdatedOn: "2025-06-05T06:34:58.653+00:00",
          RequestType: "Mass Change",

        CurrentLevel: taskData?.ATTRIBUTE_3||"",
        CurrentLevelName: taskData?.ATTRIBUTE_4||"",
        IsScheduled: "",
        ParticularLevel: "",
        TaskCreatedOn: "",
        TaskName: "",
        },


      ToCompanycode: [
        {
          CompCodeID: pcData.CompCodeID || "",
          CompanyCode: pcData.companyCode || "",
          CompanyName: "",
          AssignToPrctr: "",
          Venture: "",
          RecInd: "",
          EquityTyp: "",
          JvOtype: "",
          JvJibcl: "",
          JvJibsa: "",
        },
      ],

      Torequestheaderdata: {
        RequestId: requestHeaderSlice?.requestId  ||"",
        ReqCreatedBy: requestHeaderSlice?.reqCreatedBy || "",
        ReqCreatedOn:
          requestHeaderSlice?.reqCreatedOn || "/Date(1743998564967)/",
        ReqUpdatedOn:
          requestHeaderSlice?.reqUpdatedOn || "/Date(1743998564967)/",
        RequestType: requestHeaderSlice?.requestType || "",
        RequestPrefix: requestHeaderSlice?.requestPrefix || "",
        RequestPriority: requestHeaderSlice?.requestPriority || "",
        RequestDesc: requestHeaderSlice?.requestDesc || "",
        RequestStatus: "Draft",
        FirstProd: "",
        LaunchDate: "",
        LeadingCat: "Anesthesia/Pain Management",
        Division: "00",
        TemplateName: "",
        FieldName: "",
        Region: "",
        FilterDetails: null,
        IsBifurcated: null,
      },

      TochangeLogData: {
        ChangeLogId: pcData.ChangeLogId || "",
        RequestId: null,
        RequestHeaderId: "",
        ChangeLogData: null,
      },

      ToProfitCenterErrorData: {
        ProfitCenterErrorId: pcData?.ProfitCenterErrorID || "",
        RequestId: "",
        ProfitCenter: pcData?.profitCenter || "",
        CompanyCode: pcData?.companyCode || "",
        Segment: pcData?.basicDataTabDto?.Segment || "",
        PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
        ControllingArea: pcData?.controllingArea || "",
        SapMessage: "",
        ObjectSapError: "",
        ObjectDbError: "",
        ObjectExcelError: "",
        ShortDescSapError: "",
        ShortDescDbError: "",
        ShortDescExcelError: "",
        LongDescSapError: "",
        LongDescDbError: "",
        LongDescExcelError: "",
        AddrValidationError: "",
        PersonResponsibleError: "",
        DmsAttachmentErrorStatus: "",
        SapAttachmentErrorStatus: "",
        RemovalNodeErrorStatus: "",
        AddNodeErrorStatus: "",
      },

      ToGeneralInfoData: {
        GeneralInfoId: pcData.GeneralInfoId || "",
        RequestPriority: "",
        BusinessJustification: "",
        SAPorJEErrorCheck: "",
        BusinessSegment: pcData.businessSegment || "CRUDE",
        Region: "",
      },
    };
  });
  return Payload;
};
export const changePayloadForCC = (
  requestHeaderData,
  initialPayload,
  task,
  isreqBench,
  fetchReqBenchDataCC,
  fetchedCostCenterData
) => {
  const allData = isreqBench ? fetchReqBenchDataCC : fetchedCostCenterData;

  const payload = allData.map((ccData) => {
    return {
      CostCenterHeaderID: ccData?.costCenterHeaderID ?? "",
      ControllingArea: ccData?.controllingArea ?? "",
      Testrun: true, // Not from UI
      IsSunoco: false,
      IsSunocoCCPC: false,
      IsScheduled: false,
      Action: "I", // Not from UI
      ReqCreatedBy: ccData?.createdBy ?? "",
      ReqCreatedOn: "",
      RequestStatus:
        requestHeaderData?.RequestStatus ?? ccData?.requestStatus ?? "",
      CreationId: "", // Not from UI
      EditId: "", // Not from UI
      DeleteId: "", // Not from UI
      MassCreationId: "", // Not from UI
      MassEditId: ccData?.massEditId ?? "",
      MassDeleteId: "",
      RequestType:
        requestHeaderData?.RequestType === "Change"
          ? "Mass Change"
          : ccData?.requestStatus
          ? ccData?.requestStatus
          : requestHeaderData?.RequestType || "",
      MassRequestStatus: ccData?.massRequestStatus ?? "", // Not from UI
      TaskId: task?.taskId ? task?.taskId : "",
      Remarks: ccData?.Comments ?? "",
      TempLockRemarks: "", // Not from UI
      Info:
        requestHeaderData?.TemplateName === "Temporary Block/Unblock"
          ? "Temporary Block or Unblock"
          : ccData?.templateName === "Temporary Block/Unblock"
          ? "Temporary Block or Unblock"
          : requestHeaderData?.TemplateName === "Change PC on CC"
          ? "Profit Center Change"
          : ccData?.templateName === "Change PC on CC"
          ? "Profit Center Change"
          : requestHeaderData?.TemplateName ===
            "CC Category & FERC Indicator Change"
          ? "Change FERC Indicator"
          : ccData?.templateName === "CC Category & FERC Indicator Change"
          ? "Change FERC Indicator"
          : requestHeaderData?.TemplateName === "Block"
          ? "Block"
          : ccData?.templateName === "Block"
          ? "Block"
          : requestHeaderData?.TemplateName === "All Other Fields"
          ? "Change All Other Fields or Unblock"
          : ccData?.templateName === "All Other Fields"
          ? "Change All Other Fields or Unblock"
          : requestHeaderData?.TemplateName === "Address Change"
          ? "Change Address Fields"
          : ccData?.templateName === "Address Change"
          ? "Change Address Fields"
          : "Change All Other Fields or Unblock",
      TemplateName:
        requestHeaderData?.TemplateName ?? ccData?.templateName ?? "", // Not from UI
      TemplateHeaders: ccData?.templateHeaders ?? "", // Not from UI
      ChangedFields: ccData?.changedFields ?? "", // Not from UI
      GeneralInfoID: ccData?.generalInfoID ?? "",
      RequestPriority:
        requestHeaderData?.RequestPriority ?? ccData?.requestPriority ?? "",
      BusinessJustification: ccData?.businessJustification ?? "", // to be mapped from UI
      SAPorJEErrorCheck: ccData?.sAPorJEErrorCheck ?? "", // to be mapped from UI
      FERCIndicator: ccData?.fERCIndicator ?? "", // Not from UI
      BusinessSegment: ccData?.businessSegment ?? "", // Not from UI
      HierarchyRegion: ccData?.hierarchyRegion ?? "",
      AAMNumber: ccData?.aAMNumber ?? "",
      ValidationDoneBy: task?.taskId ? "MDM Approval" : "Requestor",
      TotalIntermediateTasks:
        task?.TotalIntermediateTasks ?? ccData?.totalIntermediateTasks ?? "",
      IsFirstSynCompleted: ccData?.isFirstSynCompleted ?? false, // Not from UI
      TempLockIsSelectedForSyn: ccData?.tempLockIsSelectedForSyn ?? false, // Not from UI
      SelectedByRequestorToDisplay: ccData?.included ?? true, // to be mapped from UI
      ToChildHeaderdata: {
        RequestId: ccData?.toChildHeaderDataId ?? "",
        Status:
          requestHeaderData?.ToChildHeaderdata?.Status ??
          ccData?.toChildHeaderDataStatus ??
          "",
        IntermediateTaskCount:
          requestHeaderData?.ToChildHeaderdata?.IntermediateTaskCount ??
          ccData?.toChildHeaderDataIntermediateTaskCount ??
          "",
        TotalTaskCount:
          requestHeaderData?.ToChildHeaderdata?.TotalTaskCount ??
          ccData?.toChildHeaderDataTotalTaskCount ??
          "",
        BifurcatedValue:
          requestHeaderData?.ToChildHeaderdata?.BifurcatedValue ??
          ccData?.toChildHeaderDataBifurcatedValue ??
          "",
        TaskId:
          requestHeaderData?.ToChildHeaderdata?.TaskId ??
          ccData?.toChildHeaderDataTaskId ??
          "",
        CreatedOn: "2025-06-05T06:34:55.995+00:00",
        UpdatedOn: "2025-06-05T06:34:58.653+00:00",
        RequestType: "Mass Change",
      },
      ToCostCenterData: [
        {
          CostCenterID: ccData?.costCenterId ?? "",
          CostCenterErrorID: ccData?.costCenterErrorId, // Not from UI
          Costcenter: ccData?.costCenter ?? "",
          ValidFrom:
            // body?.ValidFrom
            // ? `/Date(${new Date(body.ValidFrom).getTime()})/`
            // :
            "/Date(-2208988800000)/",
          ValidTo:
            // body?.ValidTo
            // ? `/Date(${new Date(body.ValidTo).getTime()})/`
            // :
            "/Date(32503680000000)/",
          PersonInCharge: ccData?.userResponsible ?? "",
          CostcenterType: ccData?.CostcenterType ?? "",
          CostctrHierGrp: ccData?.CostctrHierGrp ?? "ET_CCA",
          BusArea: "",
          CompCode: ccData?.compCode ?? "",
          Currency: ccData?.currency ?? "",
          ProfitCtr: ccData?.profitCtr ?? "",
          Name: ccData?.name ?? "",
          Descript: ccData?.description ?? "",
          PersonInChargeUser: ccData?.personResponsible ?? "",
          RecordQuantity: ccData?.RecordQuantity ?? "",
          LockIndActualPrimaryCosts: ccData?.lockIndActualPrimaryCosts ?? "",
          LockIndPlanPrimaryCosts: ccData?.lockIndPlanPrimaryCosts || "",
          LockIndActSecondaryCosts: ccData?.lockIndActSecondaryCosts || "",
          LockIndPlanSecondaryCosts: ccData?.lockIndPlanSecondaryCosts || "",
          LockIndActualRevenues: ccData?.lockIndActualRevenues || "",
          LockIndPlanRevenues: ccData?.lockIndPlanRevenues || "",
          LockIndCommitmentUpdate: ccData?.lockIndCommitmentUpdate || "",
          ConditionTableUsage: "",
          Application: "",
          CstgSheet: "",
          ActyIndepTemplate: ccData?.ActyIndepTemplate || "",
          ActyDepTemplate: ccData?.ActyDepTemplate || "",
          AddrTitle: ccData?.AddrTitle || "",
          AddrName1: ccData?.name1 || "",
          AddrName2: ccData?.name2 || "",
          AddrName3: ccData?.name3 || "",
          AddrName4: ccData?.name4 || "",
          AddrStreet: ccData?.street || "",
          AddrCity: ccData?.city || "",
          AddrDistrict: ccData?.AddrDistrict || "",
          AddrCountry: ccData?.country || "",
          AddrCountryIso: ccData?.AddrCountryIso || "",
          AddrTaxjurcode: ccData?.AddrTaxjurcode || "",
          AddrPoBox: ccData?.pocode || "",
          AddrPostlCode: ccData?.AddrPostlCode || "",
          AddrPobxPcd: ccData?.AddrPobxPcd || "",
          AddrRegion: ccData?.region || "",
          TelcoLangu: ccData?.TelcoLangu || "",
          TelcoLanguIso: ccData?.TelcoLanguIso || "",
          TelcoTelephone: ccData?.TelcoTelephone || "",
          TelcoTelephone2: ccData?.TelcoTelephone2 || "",
          TelcoTelebox: ccData?.TelcoTelebox || "",
          TelcoTelex: ccData?.TelcoTelex || "",
          TelcoFaxNumber: ccData?.TelcoFaxNumber || "",
          TelcoTeletex: ccData?.TelcoTeletex || "",
          TelcoPrinter: ccData?.TelcoPrinter || "",
          TelcoDataLine: ccData?.TelcoDataLine || "",
          ActyDepTemplateAllocCc: ccData?.ActyDepTemplateAllocCc || "",
          ActyDepTemplateSk: ccData?.ActyDepTemplateSk || "",
          ActyIndepTemplateAllocCc: ccData?.ActyIndepTemplateAllocCc || "",
          ActyIndepTemplateSk: ccData?.ActyIndepTemplateSk || "",
          AvcActive:
            ccData?.AvcActive === true
              ? true
              : ccData?.AvcActive === false
              ? false
              : null,
          AvcProfile: ccData?.AvcProfile || "",
          BudgetCarryingCostCtr: ccData?.BudgetCarryingCostCtr || "",
          CurrencyIso: ccData?.CurrencyIso || "",
          Department: ccData?.Department || "",
          FuncArea: ccData?.FuncArea || "",
          FuncAreaFixAssigned: ccData?.FuncAreaFixAssigned || "",
          FuncAreaLong: ccData?.functionalArea || "",
          Fund: ccData?.Fund || "",
          FundFixAssigned: ccData?.FundFixAssigned || "",
          GrantFixAssigned: ccData?.GrantFixAssigned || "",
          GrantId: ccData?.GrantId || "",
          JvEquityTyp: ccData?.JvEquityTyp || "",
          JvJibcl: ccData?.JvJibcl || "",
          JvJibsa: ccData?.JvJibsa || "",
          JvOtype: ccData?.JvOtype || "",
          JvRecInd: ccData?.JvRecInd || "",
          JvVenture: ccData?.JvVenture || "",
          Logsystem: ccData?.Logsystem || "",
          FERCIndicator: "",
          TochangeLogData: {
            ChangeLogId: ccData?.toChangeLogDataId ?? "", // should be mapped from display
            RequestId: ccData?.toChangeLogRequestId ?? "", // should be mapped from display
            RequestHeaderId: ccData?.toChangeLogRequestHeaderId ?? "", // should be mapped from display
            ChangeLogData: null,
          },
          ToCostCenterErrorData: {
            CostCenterErrorId: ccData?.toCostCenterErrorDataId ?? "",
            RequestId: ccData?.toCostCenterErrorDataRequestId ?? "",
            RequestHeaderId: ccData?.toCostCenterErrorDataRequestHeaderId ?? "",
            CostCenter: ccData?.toCostCenterErrorDataCostCenter ?? "",
            CompCode: ccData?.toCostCenterErrorDataCompCode ?? "",
            ControllingArea: ccData?.toCostCenterErrorDataControllingArea ?? "",
            SAPMessage: ccData?.toCostCenterErrorDataSAPMessage ?? "",
            ObjectSAPError: ccData?.toCostCenterErrorDataObjectSAPError ?? "",
            ObjectDBError: ccData?.toCostCenterErrorDataObjectDBError ?? "",
            ObjectExcelError:
              ccData?.toCostCenterErrorDataObjectExcelError ?? "",
            ShortDescSAPError:
              ccData?.toCostCenterErrorDataShortDescSAPError ?? "",
            ShortDescDBError: ccData?.ShortDescDBError ?? "",
            ShortDescExcelError:
              ccData?.toCostCenterErrorDataShortDescExcelError ?? "",
            LongDescSAPError:
              ccData?.toCostCenterErrorDataLongDescSAPError ?? "",
            LongDescDBError: ccData?.toCostCenterErrorDataLongDescDBError ?? "",
            LongDescExcelError:
              ccData?.toCostCenterErrorDataLongDescExcelError ?? "",
            AddressValidation:
              ccData?.toCostCenterErrorDataAddressValidation ?? "",
            PersonResponsibleError: ccData?.PersonResponsibleError ?? "",
            UserResponsibleError:
              ccData?.toCostCenterErrorDataUserResponsibleError ?? "",
            CheckUserAndPersonResponsible:
              ccData?.toCostCenterErrorDataCheckUserAndPersonResponsible ?? "",
            DMSAttachmentErrorStatus:
              ccData?.toCostCenterErrorDataDMSAttachmentErrorStatus ?? "",
            SAPAttachmentErrorStatus:
              ccData?.toCostCenterErrorDataSAPAttachmentErrorStatus ?? "",
          },
        },
      ],
    Torequestheaderdata: {
        RequestId:
          requestHeaderData?.RequestId ??
          initialPayload?.requestId ??
          "",
        ReqCreatedBy:
          requestHeaderData?.ReqCreatedBy ??
          initialPayload?.reqCreatedBy ??
          "",
        ReqCreatedOn: `/Date(${new Date(
          requestHeaderData?.ReqCreatedOn ??
            initialPayload?.reqCreatedOn
        ).getTime()})/`,
        ReqUpdatedOn: `/Date(${new Date(
          requestHeaderData?.ReqUpdatedOn ??
            initialPayload?.reqUpdatedOn
        ).getTime()})/`,
        RequestType:
          requestHeaderData?.requestType ??
          initialPayload?.requestType ??
          "",
        RequestPrefix:
          requestHeaderData?.RequestPrefix ??
          initialPayload?.requestPrefix ??
          "",
        RequestPriority:
          requestHeaderData.RequestPriority ??
          initialPayload?.requestPriority ??
          "",
        RequestDesc:
          requestHeaderData?.RequestDesc ??
          initialPayload?.requestDesc ??
          "",
        RequestStatus:
          requestHeaderData?.RequestStatus ??
          initialPayload?.requestStatus ??
          "",
        FirstProd:
          requestHeaderData?.FirstProd ??
          initialPayload?.firstProd ??
          "",
        LaunchDate:
          requestHeaderData?.LaunchDate ??
          initialPayload?.launchDate ??
          "",
        LeadingCat:
          requestHeaderData?.LeadingCat ??
          initialPayload?.leadingCat ??
          "",
        Division:
          requestHeaderData?.Division ?? initialPayload?.division ?? "",
        TemplateName:
          requestHeaderData?.TemplateName ??
          initialPayload?.templateName ??
          "",
        FieldName:
          requestHeaderData?.FieldName ??
          initialPayload?.fieldName ??
          "",
        Region:
          requestHeaderData?.Region ?? initialPayload?.region ?? "",
        FilterDetails: requestHeaderData?.FilterDetails ?? "",
        IsBifurcated:
          requestHeaderData?.IsBifurcated ??
          initialPayload?.isBifurcated ??
          "",
      },
      ToGeneralInfoData: {
        GeneralInfoId: ccData?.toGeneralInfoDataGeneralInfoId ?? "",
        RequestPriority: ccData?.toGeneralInfoDataRequestPriority ?? "",
        BusinessJustification:
          ccData?.toGeneralInfoDataBusinessJustification ?? "",
        SAPorJEErrorCheck: ccData?.toGeneralInfoDataSAPorJEErrorCheck ?? "",
        BusinessSegment: ccData?.toGeneralInfoDataBusinessSegment ?? "",
        Region: ccData?.toGeneralInfoDataRegion ?? "",
        AAMNumber: ccData?.toGeneralInfoDataAAMNumber ?? "",
      },
    };
  });
  return payload;
};

export const createPayloadForGL = (
  reduxPayload,
  requestHeaderSlice,
  isrequestId,
  task,
  dynamicData,
  createChangeLogData,
  selectedCompanyCode
) => {
  const { requestHeaderData, rowsHeaderData, rowsBodyData } = reduxPayload;

  const finalPayload = rowsHeaderData?.map((row) => {
    const rowId = row.id;
    const body = rowsBodyData[rowId] || {};
    const changelogDta = createChangeLogData[rowId];
    return {
      requestInProcess: true,
      GeneralLedgerID: body?.GeneralLedgerID ?? "",
      TemplateName: requestHeaderData?.TemplateName || "",
      TemplateHeaders: "",
      IsScheduled: false,
      Action: "I",
      RequestID: "",
      TaskStatus: null,
      TaskId: task?.taskId || null,
      Remarks: dynamicData?.Comments ?? "",
      TempLockRemarks: "",
      Info: "ET Create",
      ChangedFields: "",
      CreationId: "",
      EditId: "",
      DeleteId: "",
      FercId: "",
      MassCreationId: body?.MassCreationId ?? "",
      MassEditId: "",
      MassDeleteId: "",
      ExtendId: "",
      MassExtendId: "",
      GeneralLedgerErrorID: body?.GeneralLedgerErrorID ?? "",
      RequestType:
        requestHeaderData?.RequestType === "Create"
          ? "Mass Create"
          : requestHeaderData?.RequestType || "",
      ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
      ReqCreatedOn: `/Date(${new Date(
        requestHeaderData?.ReqCreatedOn
      ).getTime()})/`,
      ReqUpdatedOn: `/Date(${new Date(
        requestHeaderData?.ReqUpdatedOn
      ).getTime()})/`,
      RequestPrefix: requestHeaderSlice?.requestPrefix
        ? requestHeaderSlice?.requestPrefix
        : "",
      RequestPriority: requestHeaderData?.RequestPriority || "",
      RequestDesc: requestHeaderData?.RequestDesc || "",
      RequestStatus: requestHeaderData?.RequestStatus || "",
      Testrun: null,
      COA: row?.chartOfAccount?.code ?? body?.ControllingArea ?? "ETCN",
      CompanyCode: body?.CompanyCode ?? "",
      CoCodeToExtend: selectedCompanyCode[rowId]
        ?.map((item) => item.code)
        .join(","),
      GLAccount: row?.glAccountNumber ?? body?.GLAccount,
      Accounttype: body?.Accounttype ?? "",
      AccountGroup: body?.AccountGroup ?? "",
      SubAccountGroup: "CASH AND CASH EQUIVALENTS",
      GLname: body?.GLname ?? "",
      Description: body?.Description ?? "",
      TradingPartner: body?.TradingPartner ?? "",
      GroupAccNo: body?.GroupAccNo ?? "",
      AccountCurrency: body?.AccountCurrency ?? "",
      Exchangerate: "",
      Balanceinlocrcy: body?.Balanceinlocrcy ? "X" : "",
      Taxcategory: body?.Taxcategory ?? "",
      Pstnwotax: body?.Pstnwotax ? "X" : "",
      ReconAcc: body?.ReconAcc ?? "",
      Valuationgrp: body?.Valuationgrp ?? "",
      AlterAccno: body?.AlterAccno ?? "",
      Openitmmanage: body?.Openitmmanage ? "X" : "",
      Sortkey: body?.Sortkey,
      CostEleCategory: body?.CostEleCategory ?? "",
      FieldStsGrp: "Z001",
      PostAuto: body?.PostAuto ? "X" : "",
      Supplementautopost: body?.Supplementautopost ? "X" : "",
      Planninglevel: body?.Planninglevel ?? "",
      Relvnttocashflow: body?.Relvnttocashflow ? 'X' : "",
      HouseBank: body?.HouseBank ?? "",
      AccountId: body?.AccountId ?? "",
      Interestindicator: body?.Interestindicator ?? "",
      ICfrequency: body?.ICfrequency ?? "",
      KeydateofLIC: body?.KeydateofLIC ?? "",
      LastIntrstundate: body?.LastIntrstundate ?? "",
      AccmngExistsys: body?.AccmngExistsys ?? "",
      Infationkey: body?.Infationkey ?? "",
      Tolerancegrp: body?.Tolerancegrp ?? "",
      AuthGroup: body?.AuthGroup ?? "",
      AccountClerk: body?.AccountClerk ?? "",
      ReconAccReady: body?.ReconAccReady ? 'X' : "",
      PostingBlocked: body?.PostingBlocked ?? "",
      PlanningBlocked: body?.PlanningBlocked ?? "",
      FunctionalArea: body?.FunctionalArea ?? "",
      Accountsubtype: body?.Accountsubtype ?? "",
      OpenItemManagebyLedgerGrp: body?.OpenItemManagebyLedgerGrp ? "X" : "",
      InternalUOM: body?.InternalUOM ?? "",
      RecordQuantity: body?.RecordQuantity ?? "",
      GeneralInfoID: 0,
      RequestPriority: body?.RequestPriority ?? "",
      BusinessJustification: body?.BusinessJustification ?? "",
      BusinessSegment: row?.businessSegment?.code ?? "NA",
      SAPorJEErrorCheck: body?.SAPorJEErrorCheck ?? "",
      TaxableCheck: body?.TaxableCheck ?? "NA",
      SpecificPostingCheck: body?.SpecificPostingCheck ?? "",
      ManualEntriesCheck: body?.ManualEntriesCheck ?? "",
      TradingPartnerCheck: body?.TradingPartnerCheck ?? "",
      GLSubAccountType: body?.GLSubAccountType ?? "",
      ProductionDateEstm: body?.ProductionDateEstm ?? "",
      Message: body?.Message ?? "",
      OIMCSLIndicator: "NA",
      taxFieldCheck: "NA",
      PostingBlockedCoCd: body?.PostingBlockedCoCd ? "X" : "",
      PostingBlockedCOA: body?.PostingBlockedCOA ? "X" : "",
      FERCInformation: [
        {
          FERCInformationID: "",
          CompCode: "",
          GLAccount: "",
          Description: "",
          CCUsed: "",
          WBSUsed: "",
          FERCIndicator: "",
          RuleSet: "",
          FercAccount: "",
        },
      ],
      ValidationDoneBy: task?.taskId ? "MDM Approval" : "Requestor",
      TotalIntermediateTasks: body?.TotalIntermediateTasks ?? "",
      OIMExisting: "",
      CSLExisting: "",
      IsFirstSynCompleted: true,
      TempLockIsSelectedForSyn: true,
      SelectedByRequestorToDisplay: true,
      Torequestheaderdata: {
        RequestId:
          requestHeaderSlice?.requestId ?? requestHeaderData?.RequestId ?? "",
        ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
        ReqCreatedOn: `/Date(${new Date(
          requestHeaderData?.ReqCreatedOn
        ).getTime()})/`,
        ReqUpdatedOn: `/Date(${new Date(
          requestHeaderData?.ReqUpdatedOn
        ).getTime()})/`,
        RequestType: requestHeaderData?.RequestType || "",
        RequestPrefix: requestHeaderSlice?.requestPrefix
          ? requestHeaderSlice?.requestPrefix
          : "",
        RequestPriority: requestHeaderData?.RequestPriority || "",
        RequestDesc: requestHeaderData?.RequestDesc || "",
        RequestStatus: requestHeaderData?.RequestStatus || "",
        FirstProd: "",
        LaunchDate: "",
        LeadingCat: "Anesthesia/Pain Management",
        Division: "00",
        TemplateName: requestHeaderData?.TemplateName || "",
        FieldName: "",
        Region: "",
        FilterDetails: null,
        IsBifurcated: null,
      },
      TochangeLogData: {
        ChangeLogId: body?.TochangeLogData?.ChangeLogId,
        RequestId: body?.TochangeLogData?.RequestId,
        RequestHeaderId: body?.TochangeLogData?.RequestHeaderId,
        ChangeLogData: changelogDta,
      },
      ToChildHeaderdata: {
        RequestId: body?.MassCreationId ?? "",
        Status: requestHeaderData?.RequestStatus || "",
        IntermediateTaskCount: "",
        TotalTaskCount: "",
        BifurcatedValue: "",
        TaskId: "",
        CreatedOn: "2025-06-16T12:27:12.535Z",
        UpdatedOn: "2025-06-16T12:27:12.535Z",
        RequestType: requestHeaderData?.RequestType || "",
      },
      ToGeneralLedgerErrorData: {
        GeneralLedgerErrorId:
          body?.ToGeneralLedgerErrorData?.GeneralLedgerErrorId ?? "",
        RequestId: "",
        RequestHeaderId: "",
        GLAccount: "",
        CompanyCode: "",
        CoCodeToExtend: "",
        SapResponse: "",
        DbDuplicateCheck: "",
        ChartOfAccount: "",
        ObjectSapError: "",
        ObjectDbError: "",
        ObjectExcelError: "",
        ObjectNoRangeError: "",
        ShortDescSapError: "",
        ShortDescDbError: "",
        ShortDescExcelError: "",
        LongDescSapError: "",
        LongDescDbError: "",
        LongDescExcelError: "",
        BroSeriesShortDescError: "",
        BroSeriesLongDescError: "",
        DmsAttachmentErrorStatus: "",
        SapAttachmentErrorStatus: "",
      },
      ToGeneralInfoData: {
        GeneralInfoId: body?.GeneralInfoId ?? "",
        RequestPriority: requestHeaderData?.RequestPriority || "",
        BusinessJustification: "",
        SAPorJEErrorCheck: "",
        BusinessSegment:
          row?.businessSegment?.code ?? body?.BusinessSegment ?? "NA",
        Region: "",
        SAPorJEErrorCheck: "",
        TaxableCheck: "",
        SpecificPostingCheck: "",
        ManualEntriesCheck: "",
        TradingPartnerCheck: "",
        GLSubAccountType: "",
        ProductionDateEstm: "",
        Message: "",
      },
    };
  });

  return finalPayload;
};


export const createPayloadForChangeGL = (reduxPayload, requestHeaderSlice, isrequestId, task, dynamicData, createChangeLogData = '', selectedCompanyCode) => {

  const finalPayload = reduxPayload?.map((gldata) => {
    const rowId = row.id;
    const body = rowsBodyData[rowId] || {};
    const changelogDta = createChangeLogData?.[rowId] ?? []
    return {
      requestInProcess: true,
      GeneralLedgerID: gldata?.GeneralLedgerID ?? "",
      TemplateName: requestHeaderData?.TemplateName || "",
      TemplateHeaders: "",
      IsScheduled: false,
      Action: "I",
      RequestID: "",
      TaskStatus: null,
      TaskId: task?.taskId || null,
      Remarks: ccData?.Comments ?? "",
      TempLockRemarks: "",
      Info: "ET Change",
      ChangedFields: gldata?.changedFields ?? "",
      CreationId: "",
      EditId: "",
      DeleteId: "",
      FercId: "",
      MassCreationId: '',
      MassEditId: gldata?.massEditId ?? "",
      MassDeleteId: "",
      ExtendId: "",
      MassExtendId: "",
      GeneralLedgerErrorID: ccData?.GeneralLedgerErrorID ?? "",
      RequestType: requestHeaderData?.RequestType === "Create" ? "Mass Create" : requestHeaderData?.RequestType || "",
      ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
      ReqCreatedOn: `/Date(${new Date(requestHeaderData?.ReqCreatedOn).getTime()})/`,
      ReqUpdatedOn: `/Date(${new Date(requestHeaderData?.ReqUpdatedOn).getTime()})/`,
      RequestPrefix: ccData?.toRequestHeaderDataRequestPrefix ?? initialPayload?.requestPrefix ?? "",
      RequestPriority: requestHeaderData?.RequestPriority || "",
      RequestDesc: requestHeaderData?.RequestDesc || "",
      RequestStatus: requestHeaderData?.RequestStatus || "",
      Testrun: null,
      COA: ccData?.chartOfAccount ?? ccData?.chartOfAccount ?? "ETCN",
      CompanyCode: ccData?.CompanyCode ?? "",
      CoCodeToExtend: '',
      GLAccount: ccData?.glAccountNumber ?? ccData?.GLAccount,
      Accounttype: ccData?.Accounttype ?? '',
      AccountGroup: ccData?.AccountGroup ?? '',
      SubAccountGroup: "CASH AND CASH EQUIVALENTS",
      GLname: ccData?.GLname ?? "",
      Description: ccData?.Description ?? "",
      TradingPartner: ccData?.TradingPartner ?? "",
      GroupAccNo: ccData?.GroupAccNo ?? "",
      AccountCurrency: ccData?.AccountCurrency ?? "",
      Exchangerate: "",
      Balanceinlocrcy: ccData?.Balanceinlocrcy ? "X" : "",
      Taxcategory: ccData?.Taxcategory ?? "",
      Pstnwotax: ccData?.pwotax ? "X": "",
      ReconAcc: ccData?.ReconAcc ?? "",
      Valuationgrp: ccData?.Valuationgrp ?? "",
      AlterAccno: ccData?.AlterAccno ?? "",
      Openitmmanage: ccData?.Openitmmanage ? "X" : '',
      Sortkey: ccData?.Sortkey,
      CostEleCategory: ccData?.CostEleCategory ?? "",
      FieldStsGrp: "Z001",
      PostAuto: ccData?.PostAuto ? "X" : '',
      Supplementautopost: ccData?.Supplementautopost ? "X" : "",
      Planninglevel: ccData?.Planninglevel ?? "",
      Relvnttocashflow: ccData?.Relvnttocashflow ? 'x' : "",
      HouseBank: ccData?.HouseBank ?? "",
      AccountId: ccData?.AccountId ?? "",
      Interestindicator: ccData?.Interestindicator ?? "",
      ICfrequency: ccData?.ICfrequency ?? "",
      KeydateofLIC: ccData?.KeydateofLIC ?? "",
      LastIntrstundate: ccData?.LastIntrstundate ?? "",
      AccmngExistsys: ccData?.AccmngExistsys ?? "",
      Infationkey: ccData?.Infationkey ?? "",
      Tolerancegrp: ccData?.Tolerancegrp ?? "",
      AuthGroup: ccData?.AuthGroup ?? "",
      AccountClerk: ccData?.AccountClerk ?? "",
      ReconAccReady: ccData?.ReconAccReady ? 'X' : "",
      PostingBlocked: ccData?.PostingBlocked ?? "",
      PlanningBlocked: ccData?.PlanningBlocked ?? "",
      FunctionalArea: ccData?.FunctionalArea ?? "",
      Accountsubtype: ccData?.Accountsubtype ?? "",
      OpenItemManagebyLedgerGrp: ccData?.OpenItemManagebyLedgerGrp ? "X" : '',
      InternalUOM: ccData?.InternalUOM ?? "",
      RecordQuantity: ccData?.RecordQuantity ?? "",
      GeneralInfoID: 0,
      RequestPriority: ccData?.RequestPriority ?? "",
      BusinessJustification: ccData?.BusinessJustification ?? "",
      BusinessSegment: ccData?.businessSegment?.code ?? "NA",
      SAPorJEErrorCheck: ccData?.SAPorJEErrorCheck ?? "",
      TaxableCheck: ccData?.TaxableCheck ?? "NA",
      SpecificPostingCheck: ccData?.SpecificPostingCheck ?? "",
      ManualEntriesCheck: ccData?.ManualEntriesCheck ?? "",
      TradingPartnerCheck: ccData?.TradingPartnerCheck ?? "",
      GLSubAccountType: ccData?.GLSubAccountType ?? "",
      ProductionDateEstm: ccData?.ProductionDateEstm ?? "",
      Message: ccData?.Message ?? "",
      OIMCSLIndicator: "NA",
      taxFieldCheck: "NA",
      PostingBlockedCoCd: ccData?.PostingBlockedCoCd ? "X" : '',
      PostingBlockedCOA: ccData?.PostingBlockedCOA ? "X" : '',
      FERCInformation: [
        {
          FERCInformationID: '',
          CompCode: "",
          GLAccount: "",
          Description: "",
          CCUsed: "",
          WBSUsed: "",
          FERCIndicator: "",
          RuleSet: "",
          FercAccount: ""
        }
      ],
      ValidationDoneBy: task?.taskId ? "MDM Approval" : "Requestor",
      TotalIntermediateTasks: ccData?.TotalIntermediateTasks ?? "",
      OIMExisting: "",
      CSLExisting: "",
      IsFirstSynCompleted: true,
      TempLockIsSelectedForSyn: true,
      SelectedByRequestorToDisplay: true,
      Torequestheaderdata: {
        RequestId: ccData?.toRequestHeaderDataRequestId ?? initialPayload?.requestId ?? "",
        ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
        ReqCreatedOn: `/Date(${new Date(requestHeaderData?.ReqCreatedOn).getTime()})/`,
        ReqUpdatedOn: `/Date(${new Date(requestHeaderData?.ReqUpdatedOn).getTime()})/`,
        RequestType: requestHeaderData?.RequestType === "Create" ? "Mass Create" : ccData?.requestStatus ? ccData?.requestStatus : requestHeaderData?.RequestType || "",
        RequestPrefix: ccData?.toRequestHeaderDataRequestPrefix ?? initialPayload?.requestPrefix ?? "",
        RequestPriority: requestHeaderData?.RequestPriority || "",
        RequestDesc: requestHeaderData?.RequestDesc || "",
        RequestStatus: requestHeaderData?.RequestStatus || "",
        FirstProd: "",
        LaunchDate: "",
        LeadingCat: "Anesthesia/Pain Management",
        Division: "00",
        TemplateName: requestHeaderData?.TemplateName || "",
        FieldName: "",
        Region: "",
        FilterDetails: null,
        IsBifurcated: null,
      },
      TochangeLogData: {
        ChangeLogId: ccData?.TochangeLogData?.ChangeLogId,
        RequestId: ccData?.TochangeLogData?.RequestId,
        RequestHeaderId: ccData?.TochangeLogData?.RequestHeaderId,
        ChangeLogData: ccData?.changelogDta ?? []
      },
      ToChildHeaderdata: {
        RequestId: ccData?.MassCreationId ?? "",
        Status: requestHeaderData?.RequestStatus || "",
        IntermediateTaskCount: '',
        TotalTaskCount: '',
        BifurcatedValue: "",
        TaskId: "",
        CreatedOn: "2025-06-16T12:27:12.535Z",
        UpdatedOn: "2025-06-16T12:27:12.535Z",
        RequestType: requestHeaderData?.RequestType || "",
      },
      ToGeneralLedgerErrorData: {
        GeneralLedgerErrorId: ccData?.ToGeneralLedgerErrorData?.GeneralLedgerErrorId ?? "",
        RequestId: "",
        RequestHeaderId: "",
        GLAccount: "",
        CompanyCode: "",
        CoCodeToExtend: "",
        SapResponse: "",
        DbDuplicateCheck: "",
        ChartOfAccount: "",
        ObjectSapError: "",
        ObjectDbError: "",
        ObjectExcelError: "",
        ObjectNoRangeError: "",
        ShortDescSapError: "",
        ShortDescDbError: "",
        ShortDescExcelError: "",
        LongDescSapError: "",
        LongDescDbError: "",
        LongDescExcelError: "",
        BroSeriesShortDescError: "",
        BroSeriesLongDescError: "",
        DmsAttachmentErrorStatus: "",
        SapAttachmentErrorStatus: ""
      },
      ToGeneralInfoData: {
        GeneralInfoId: ccData?.GeneralInfoId ?? "",
        RequestPriority: requestHeaderData?.RequestPriority || "",
        BusinessJustification: "",
        SAPorJEErrorCheck: "",
        BusinessSegment: ccData?.businessSegment?.code ?? ccData?.BusinessSegment ?? "NA",
        Region: "",
        SAPorJEErrorCheck: '',
        TaxableCheck: "",
        SpecificPostingCheck: "",
        ManualEntriesCheck: "",
        TradingPartnerCheck: "",
        GLSubAccountType: "",
        ProductionDateEstm: "",
        Message: ""
      }
    }
  });

  return finalPayload;
};


export const changePayloadForGL =(requestData,requestHeaderData,task,initialPayload)=>{
    const allData = requestData;
    const payload = allData?.map((ccData) => {
     return {
        requestInProcess: true,
        GeneralLedgerID: ccData?.GeneralLedgerID ?? "",
        TemplateName: requestHeaderData?.TemplateName || "",
        TemplateHeaders: "",
        IsScheduled: false,
        Action: "I",
        RequestID: "",
        TaskStatus: null,
        TaskId: task?.taskId || null,
        Remarks: ccData?.Comments ?? "",
        TempLockRemarks: "",
        Info: "ET Change",
        ChangedFields: ccData?.changedFields ?? "",
        CreationId: "",
        EditId: "",
        DeleteId: "",
        FercId: "",
        MassCreationId: '',
        MassEditId: ccData?.MassEditId || "",
        MassDeleteId: "",
        ExtendId: "",
        MassExtendId: "",
        GeneralLedgerErrorID: ccData?.GeneralLedgerErrorID ?? "",
        RequestType: requestHeaderData?.RequestType === "Create" ? "Mass Create" : requestHeaderData?.RequestType || "",
        ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
        ReqCreatedOn: `/Date(${new Date(requestHeaderData?.ReqCreatedOn).getTime()})/`,
        ReqUpdatedOn: `/Date(${new Date(requestHeaderData?.ReqUpdatedOn).getTime()})/`,
        RequestPrefix: ccData?.toRequestHeaderDataRequestPrefix ?? initialPayload?.requestPrefix ?? "",
        RequestPriority: requestHeaderData?.RequestPriority || "",
        RequestDesc: requestHeaderData?.RequestDesc || "",
        RequestStatus: requestHeaderData?.RequestStatus || "",
        Testrun: null,
        COA: ccData?.chartOfAccount ?? "ETCN",
        CompanyCode: ccData?.compCode ?? "",
        CoCodeToExtend:  '',
        GLAccount: ccData?.generalLedger ?? '',
        Accounttype: ccData?.accountType ?? '',
        AccountGroup: ccData?.accGroup ?? '',
        SubAccountGroup: "CASH AND CASH EQUIVALENTS",
        GLname: ccData?.glname ?? "",
        Description: ccData?.description ?? "",
        TradingPartner: ccData?.TradingPartner ?? "",
        GroupAccNo: ccData?.GroupAccNo ?? "",
        AccountCurrency: ccData?.AccountCurrency ?? "",
        Exchangerate: "",
        Balanceinlocrcy: ccData?.onlyBalanceInLocalCurrency ? "X" : "",
        Taxcategory: ccData?.taxcategory ?? "", 
        Pstnwotax: ccData?.pwotax ? "X": "",
        ReconAcc: ccData?.reconAc ??  "",
        Valuationgrp: ccData?.Valuationgrp ?? "",
        AlterAccno: ccData?.AlterAccno ?? "",
        Openitmmanage: ccData?.openItemManage ? "X" : '',
        Sortkey: ccData?.sortKey,
        CostEleCategory: ccData?.CostEleCategory ?? "",
        FieldStsGrp: ccData?.filedStatusGrp,
        PostAuto: ccData?.postAutoOnly ? "X" : '',
        Supplementautopost: ccData?.Supplementautopost ? "x" : "",
        Planninglevel: ccData?.Planninglevel ?? "",
        Relvnttocashflow: ccData?.Relvnttocashflow  ? "X":  "",
        HouseBank: ccData?.housebank ?? "",
        AccountId: ccData?.accountId ?? "",
        Interestindicator: ccData?.Interestindicator ?? "",
        ICfrequency: ccData?.ICfrequency ?? "",
        KeydateofLIC: ccData?.KeydateofLIC ?? "",
        LastIntrstundate: ccData?.LastIntrstundate ?? "",
        AccmngExistsys:ccData?.AccmngExistsys ?? "",
        Infationkey: ccData?.Infationkey ?? "",
        Tolerancegrp:ccData?.Tolerancegrp ?? "",
        AuthGroup: ccData?.AuthGroup ?? "",
        AccountClerk:ccData?.AccountClerk ?? "",
        ReconAccReady: ccData?.ReconAccReady ? 'X' : "",
        PostingBlocked:ccData?.PostingBlocked ?? "",
        PlanningBlocked:ccData?.PlanningBlocked ?? "",
        FunctionalArea: ccData?.FunctionalArea ?? "",
        Accountsubtype:ccData?.Accountsubtype ?? "",
        OpenItemManagebyLedgerGrp:  ccData?.openItemManageLedgerGrp ? "X" : '',
        InternalUOM: ccData?.InternalUOM ?? "",
        RecordQuantity: ccData?.RecordQuantity ?? "",
        GeneralInfoID: 0,
        RequestPriority: ccData?.RequestPriority ?? "",
        BusinessJustification: ccData?.BusinessJustification ?? "",
        BusinessSegment: ccData?.businessSegment?.code ?? "NA",
        SAPorJEErrorCheck:ccData?.SAPorJEErrorCheck ?? "",
        TaxableCheck: ccData?.TaxableCheck ?? "NA",
        SpecificPostingCheck: ccData?.SpecificPostingCheck ?? "",
        ManualEntriesCheck: ccData?.ManualEntriesCheck ?? "",
        TradingPartnerCheck: ccData?.TradingPartnerCheck ?? "",
        GLSubAccountType:ccData?.GLSubAccountType ?? "",
        ProductionDateEstm:ccData?.ProductionDateEstm ?? "",
        Message: ccData?.Message ?? "",
        OIMCSLIndicator: "NA",
        taxFieldCheck: "NA",
        PostingBlockedCoCd: ccData?.blockedforPostingCoCode ? "X" : '',
        PostingBlockedCOA: ccData?.PostingBlockedCOA ? "X" : '',
        FERCInformation: [
            {
                FERCInformationID: '',
                CompCode: "",
                GLAccount: "",
                Description: "",
                CCUsed: "",
                WBSUsed: "",
                FERCIndicator: "",
                RuleSet: "",
                FercAccount: ""
            }
        ],
        ValidationDoneBy: task?.taskId ? "MDM Approval" : "Requestor",
        TotalIntermediateTasks: ccData?.TotalIntermediateTasks ?? "",
        OIMExisting: "",
        CSLExisting: "",
        IsFirstSynCompleted: true,
        TempLockIsSelectedForSyn: true,
        SelectedByRequestorToDisplay: true,
        Torequestheaderdata: {
            RequestId: requestHeaderData?.RequestId ?? initialPayload?.requestId ?? "",
            ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
            ReqCreatedOn: `/Date(${new Date(requestHeaderData?.ReqCreatedOn).getTime()})/`,
            ReqUpdatedOn: `/Date(${new Date(requestHeaderData?.ReqUpdatedOn).getTime()})/`,
            RequestType: requestHeaderData?.RequestType === "Create" ? "Mass Create" : ccData?.requestStatus ? ccData?.requestStatus : requestHeaderData?.RequestType || "",
            RequestPrefix: requestHeaderData?.RequestPrefix ?? initialPayload?.requestPrefix ?? "",
            RequestPriority: requestHeaderData?.RequestPriority || "",
            RequestDesc: requestHeaderData?.RequestDesc || "",
            RequestStatus: requestHeaderData?.RequestStatus || "",
            FirstProd: "",
            LaunchDate: "",
            LeadingCat: "",
            Division: "",
            TemplateName: requestHeaderData?.TemplateName || "",
            FieldName:  '',
            Region: "",
            FilterDetails: null,
            IsBifurcated: null,
        },
        TochangeLogData: {
            ChangeLogId: ccData?.TochangeLogData?.ChangeLogId,
            RequestId: ccData?.TochangeLogData?.RequestId,
            RequestHeaderId:  ccData?.TochangeLogData?.RequestHeaderId,
            ChangeLogData: ccData?.changelogDta ?? []
        },
        ToChildHeaderdata: {
          RequestId:  ccData?.MassCreationId ?? "",
          Status: requestHeaderData?.RequestStatus || "",
          IntermediateTaskCount: '',
          TotalTaskCount: '',
          BifurcatedValue: "",
          TaskId: "",
          CreatedOn: "2025-06-16T12:27:12.535Z",
          UpdatedOn: "2025-06-16T12:27:12.535Z",
          RequestType: requestHeaderData?.RequestType || "",
        },
        ToGeneralLedgerErrorData: {
            GeneralLedgerErrorId: ccData?.ToGeneralLedgerErrorData?.GeneralLedgerErrorId ?? "",
            RequestId: "",
            RequestHeaderId: "",
            GLAccount: "",
            CompanyCode: "",
            CoCodeToExtend: "",
            SapResponse: "",
            DbDuplicateCheck: "",
            ChartOfAccount: "",
            ObjectSapError: "",
            ObjectDbError: "",
            ObjectExcelError: "",
            ObjectNoRangeError: "",
            ShortDescSapError: "",
            ShortDescDbError: "",
            ShortDescExcelError: "",
            LongDescSapError: "",
            LongDescDbError: "",
            LongDescExcelError: "",
            BroSeriesShortDescError: "",
            BroSeriesLongDescError: "",
            DmsAttachmentErrorStatus: "",
            SapAttachmentErrorStatus: ""
        },
        ToGeneralInfoData: {
          GeneralInfoId: ccData?.GeneralInfoId ?? "",
            RequestPriority: requestHeaderData?.RequestPriority || "",
            BusinessJustification: "",
            SAPorJEErrorCheck: "",
            BusinessSegment: ccData?.businessSegment?.code ?? ccData?.BusinessSegment ?? "NA",
            Region: "",
            SAPorJEErrorCheck: '',
            TaxableCheck: "",
            SpecificPostingCheck: "",
            ManualEntriesCheck: "",
            TradingPartnerCheck: "",
            GLSubAccountType: "",
            ProductionDateEstm: "",
            Message: ""
        }
      }
      });
    return payload;
}


export const transformGLResponseChange = (apiResponses) => {

  return apiResponses.map((item) => {
    return  {
        "id": item?.GeneralLedgerID,
        "genralLedgerID": item?.GeneralLedgerID,
        "generalLedger": item?.GeneralLedgerID,
        "compCode": item?.CompanyCode,
        "chartOfAccount": item?.COA,
        "glname": item?.GLname,
        "description":item?.Description,
        "onlyBalanceInLocalCurrency": item?.Balanceinlocrcy,
        "accGroup":item?.AccountGroup,
        "accountType":item?.Accounttype,
        "taxcategory": item?.Taxcategory,
        "pwotax": item?.Pstnwotax,
        "reconAc": item?.ReconAcc,
        "openItemManage": item?.Openitmmanage,
        "openItemManageLedgerGrp": item?.OpenItemManagebyLedgerGrp,
        "sortKey": item?.Sortkey,
        "filedStatusGrp": item?.FieldStsGrp,
        "postAutoOnly": item?.PostAuto,
        "housebank": item?.HouseBank,
        "accountId": item?.AccountId,
        "blockedforPostingCoCode": item?.PostingBlocked
    };
  });
};






export const createPayloadForCC = (
  reduxPayload,
  requestHeaderSlice,
  isrequestId,
  task,
  dynamicData,
  createChangeLogData
) => {
  const { requestHeaderData, rowsHeaderData, rowsBodyData } = reduxPayload;

  const finalPayload = rowsHeaderData?.map((row) => {
    const rowId = row.id;
    const body = rowsBodyData[rowId] || {};
    const changelogDta = createChangeLogData?.[rowId] ?? {};

    return {
      CostCenterHeaderID: body?.CostCenterHeaderID
        ? body?.CostCenterHeaderID
        : "",
      ControllingArea: row?.controllingArea?.code
        ? row?.controllingArea?.code
        : row?.controllingArea,
      Testrun: true, // Not from UI
      IsSunoco: false,
      IsSunocoCCPC: false,
      IsScheduled: false,
      Action: "I", // Not from UI
      ReqCreatedBy: "",
      ReqCreatedOn: "",
      RequestStatus: requestHeaderData?.RequestStatus || "",
      CreationId: "", // Not from UI
      EditId: "", // Not from UI
      DeleteId: "", // Not from UI
      MassCreationId: body?.MassCreationId ?? "", // Not from UI
      MassEditId: body?.MassEditId ?? "",
      MassDeleteId: body?.MassDeleteId ?? "",
      RequestType:
        requestHeaderData?.RequestType === "Create"
          ? "Mass Create"
          : requestHeaderData?.RequestType || "",
      MassRequestStatus: "", // Not from UI
      TaskId: task?.taskId ? task?.taskId : "",
      Remarks: dynamicData?.Comments ?? "",
      TempLockRemarks: "", // Not from UI
      Info: "Create",
      TemplateName: "", // Not from UI
      TemplateHeaders: "", // Not from UI
      ChangedFields: "", // Not from UI
      GeneralInfoID: null,
      RequestPriority: requestHeaderData?.RequestPriority || "",
      BusinessJustification: "", // to be mapped from UI
      SAPorJEErrorCheck: null, // to be mapped from UI
      FERCIndicator: "", // Not from UI
      BusinessSegment: "", // Not from UI
      HierarchyRegion: "",
      AAMNumber: "",
      ValidationDoneBy: task?.taskId ? "MDM Approval" : "Requestor",
      TotalIntermediateTasks: body?.TotalIntermediateTasks ?? "",
      IsFirstSynCompleted: false, // Not from UI
      TempLockIsSelectedForSyn: false, // Not from UI
      SelectedByRequestorToDisplay: row?.included, // to be mapped from UI
      ToChildHeaderdata: {
        RequestId: body?.ToChildHeaderdata?.RequestId ?? "",
        Status: requestHeaderData?.ToChildHeaderdata?.Status || "",
        IntermediateTaskCount:
          requestHeaderData?.ToChildHeaderdata?.IntermediateTaskCount ?? null,
        TotalTaskCount:
          requestHeaderData?.ToChildHeaderdata?.TotalTaskCount ?? null,
        BifurcatedValue:
          requestHeaderData?.ToChildHeaderdata?.BifurcatedValue ?? "",
        TaskId: requestHeaderData?.ToChildHeaderdata?.TaskId ?? null,
        CreatedOn: "2025-06-05T06:34:55.995+00:00",
        UpdatedOn: "2025-06-05T06:34:58.653+00:00",
        RequestType: "Mass Create",
      },
      ToCostCenterData: [
        {
          CostCenterID: body?.CostCenterID ?? "",
          CostCenterErrorID: null, // Not from UI
          Costcenter: row?.costCenterNumber.length===10
                    ? row?.costCenterNumber 
                    : (row?.CompCode?.code ?? row?.CompCode) + row?.costCenterNumber,
          ValidFrom:
            // body?.ValidFrom
            // ? `/Date(${new Date(body.ValidFrom).getTime()})/`
            // :
            "/Date(-2208988800000)/",
          ValidTo:
            // body?.ValidTo
            // ? `/Date(${new Date(body.ValidTo).getTime()})/`
            // :
            "/Date(32503680000000)/",
          PersonInCharge: body?.PersonInCharge || "",
          CostcenterType: body?.CostcenterType || "",
          CostctrHierGrp: row?.CostctrHierGrp?.code
            ? row?.CostctrHierGrp?.code
            : row?.CostctrHierGrp ?? "ET_CCA",
          BusArea: "",
          CompCode: row?.CompCode?.code ? row?.CompCode?.code : row?.CompCode,
          Currency: body?.Currency || "",
          ProfitCtr: body?.ProfitCtr || "",
          Name: body?.Name || "",
          Descript: body?.Descript || "",
          PersonInChargeUser: body?.PersonInChargeUser || "",
          RecordQuantity: body?.RecordQuantity || "",
          LockIndActualPrimaryCosts: body?.LockIndActualPrimaryCosts || "",
          LockIndPlanPrimaryCosts: body?.LockIndPlanPrimaryCosts || "",
          LockIndActSecondaryCosts: body?.LockIndActSecondaryCosts || "",
          LockIndPlanSecondaryCosts: body?.LockIndPlanSecondaryCosts || "",
          LockIndActualRevenues: body?.LockIndActualRevenues || "",
          LockIndPlanRevenues: body?.LockIndPlanRevenues || "",
          LockIndCommitmentUpdate: body?.LockIndCommitmentUpdate || "",
          ConditionTableUsage: "",
          Application: "",
          CstgSheet: "",
          ActyIndepTemplate: body?.ActyIndepTemplate || "",
          ActyDepTemplate: body?.ActyDepTemplate || "",
          AddrTitle: body?.AddrTitle || "",
          AddrName1: body?.AddrName1 || "",
          AddrName2: body?.AddrName2 || "",
          AddrName3: body?.AddrName3 || "",
          AddrName4: body?.AddrName4 || "",
          AddrStreet: body?.AddrStreet || "",
          AddrCity: body?.AddrCity || "",
          AddrDistrict: body?.AddrDistrict || "",
          AddrCountry: body?.AddrCountry || "",
          AddrCountryIso: body?.AddrCountryIso || "",
          AddrTaxjurcode: body?.AddrTaxjurcode || "",
          AddrPoBox: body?.AddrPoBox || "",
          AddrPostlCode: body?.AddrPostlCode || "",
          AddrPobxPcd: body?.AddrPobxPcd || "",
          AddrRegion: body?.AddrRegion || "",
          TelcoLangu: body?.TelcoLangu || "",
          TelcoLanguIso: body?.TelcoLanguIso || "",
          TelcoTelephone: body?.TelcoTelephone || "",
          TelcoTelephone2: body?.TelcoTelephone2 || "",
          TelcoTelebox: body?.TelcoTelebox || "",
          TelcoTelex: body?.TelcoTelex || "",
          TelcoFaxNumber: body?.TelcoFaxNumber || "",
          TelcoTeletex: body?.TelcoTeletex || "",
          TelcoPrinter: body?.TelcoPrinter || "",
          TelcoDataLine: body?.TelcoDataLine || "",
          ActyDepTemplateAllocCc: body?.ActyDepTemplateAllocCc || "",
          ActyDepTemplateSk: body?.ActyDepTemplateSk || "",
          ActyIndepTemplateAllocCc: body?.ActyIndepTemplateAllocCc || "",
          ActyIndepTemplateSk: body?.ActyIndepTemplateSk || "",
          AvcActive:
            body?.AvcActive === true
              ? true
              : body?.AvcActive === false
              ? false
              : null,
          AvcProfile: body?.AvcProfile || "",
          BudgetCarryingCostCtr: body?.BudgetCarryingCostCtr || "",
          CurrencyIso: body?.CurrencyIso || "",
          Department: body?.Department || "",
          FuncArea: body?.FuncArea || "",
          FuncAreaFixAssigned: body?.FuncAreaFixAssigned || "",
          FuncAreaLong: body?.FuncAreaLong || "",
          Fund: body?.Fund || "",
          FundFixAssigned: body?.FundFixAssigned || "",
          GrantFixAssigned: body?.GrantFixAssigned || "",
          GrantId: body?.GrantId || "",
          JvEquityTyp: body?.JvEquityTyp || "",
          JvJibcl: body?.JvJibcl || "",
          JvJibsa: body?.JvJibsa || "",
          JvOtype: body?.JvOtype || "",
          JvRecInd: body?.JvRecInd || "",
          JvVenture: body?.JvVenture || "",
          Logsystem: body?.Logsystem || "",
          FERCIndicator: "",
          TochangeLogData: {
            ChangeLogId: body?.TochangeLogData?.ChangeLogId
              ? body?.TochangeLogData?.ChangeLogId
              : "", // should be mapped from display
            RequestId: body?.TochangeLogData?.RequestId
              ? body?.TochangeLogData?.RequestId
              : "", // should be mapped from display
            RequestHeaderId: body?.TochangeLogData?.RequestHeaderId
              ? body?.TochangeLogData?.RequestHeaderId
              : "",  // should be mapped from display
            ChangeLogData: Array.isArray(changelogDta) ? changelogDta : [],
          },
          ToCostCenterErrorData: {
            CostCenterErrorId:
              body?.ToCostCenterErrorData?.CostCenterErrorId ?? "",
            RequestId: "",
            RequestHeaderId: "",
            CostCenter: "",
            CompCode: "",
            ControllingArea: "",
            SAPMessage: "",
            ObjectSAPError: "",
            ObjectDBError: "",
            ObjectExcelError: "",
            ShortDescSAPError: "",
            ShortDescDBError: "",
            ShortDescExcelError: "",
            LongDescSAPError: "",
            LongDescDBError: "",
            LongDescExcelError: "",
            AddressValidation: "",
            PersonResponsibleError: "",
            UserResponsibleError: "",
            CheckUserAndPersonResponsible: "",
            DMSAttachmentErrorStatus: "",
            SAPAttachmentErrorStatus: "",
          },
        },
      ],
      Torequestheaderdata: {
        RequestId:
          requestHeaderSlice?.requestId ?? requestHeaderData?.RequestId ?? "",
        ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
        ReqCreatedOn: `/Date(${new Date(
          requestHeaderData?.ReqCreatedOn
        ).getTime()})/`,
        ReqUpdatedOn: `/Date(${new Date(
          requestHeaderData?.ReqUpdatedOn
        ).getTime()})/`,
        RequestType: requestHeaderData?.RequestType || "",
        RequestPrefix: requestHeaderSlice?.requestPrefix
          ? requestHeaderSlice?.requestPrefix
          : "",
        RequestPriority: requestHeaderData?.RequestPriority || "",
        RequestDesc: requestHeaderData?.RequestDesc || "",
        RequestStatus: requestHeaderData?.RequestStatus || "",
        FirstProd: "",
        LaunchDate: "",
        LeadingCat: "",
        Division: "00",
        TemplateName: requestHeaderData?.TemplateName || "",
        FieldName: "",
        Region: "",
        FilterDetails: null,
        IsBifurcated: null,
      },
      ToGeneralInfoData: {
        GeneralInfoId: body?.GeneralInfoId ?? "",
        RequestPriority: requestHeaderData?.RequestPriority || "",
        BusinessJustification: "",
        SAPorJEErrorCheck: null,
        BusinessSegment:
          row?.businessSegment?.code ?? body?.BusinessSegment ?? "",
        Region: "",
        AAMNumber: "",
      },
    };
  });

  return finalPayload;
};

export const createPayloadForPCG = (
  reduxPayload,
  requestHeaderSlice,
  isrequestId,
  task,
  dynamicData,
  requestorPayload,
  type
) => {
  let requestHeaderData = reduxPayload?.requestHeaderData;
  let ToChildHeaderdata = reduxPayload?.ToChildHeaderdata;

  let finalPayload = {
    ProfitCenterGroupID: reduxPayload?.ProfitCenterGroupID || "",
    RequestID:
      requestHeaderSlice?.requestId || requestHeaderData?.RequestId || "",
    TotalIntermediateTasks: reduxPayload?.TotalIntermediateTasks || "",
    ReqCreatedBy:
      requestHeaderData?.ReqCreatedBy || requestHeaderSlice?.reqCreatedBy || "",
    ReqCreatedOn: `/Date(${new Date(
      requestHeaderData?.ReqCreatedOn
    ).getTime()})/`,
    RequestStatus:
      requestHeaderData?.RequestStatus ||
      requestHeaderSlice?.requestStatus ||
      "",
    TaskId: task?.TaskId || "",
    CreationId: reduxPayload?.CreationId || "",
    EditId: reduxPayload?.EditId || "",
    DeleteId: reduxPayload?.DeleteId || "",
    MassCreationId: reduxPayload?.MassCreationId || "",
    MassEditId: reduxPayload?.MassEditId || "",
    MassDeleteId: reduxPayload?.MassDeleteId || "",
    RequestType:
      requestHeaderData?.RequestType || requestHeaderSlice?.requestType || "",
    Remarks: dynamicData?.Comments || "",
    Info:
      requestHeaderData?.RequestType || requestHeaderSlice?.requestType || "",
    ControllingArea:
      requestorPayload?.["Controlling Area"]?.[0]?.code ||
      reduxPayload?.ControllingArea ||
      "",
    ParentNode:
      requestorPayload?.["Profit Center Group"]?.[0]?.code ||
      requestorPayload?.["Profit Center Group"] ||
      reduxPayload?.ParentNode ||
      "",
    ParentDesc:
      requestorPayload?.["Profit Center Group Description"]?.[0]?.code ||
      requestorPayload?.["Profit Center Group Desription"] ||
      reduxPayload?.ParentDesc ||
      "",
    isMass:
      requestHeaderData?.RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD ||
      requestHeaderData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD,
    isTagAdded: reduxPayload?.TagList?.length ? true : false,
    isNodeAdded: reduxPayload?.NodesList?.length ? true : false,
    isTagReplaced: reduxPayload?.ReplaceTagList?.length ? true : false,
    isNodeReplaced: reduxPayload?.ReplaceNodesList?.length ? true : false,
    isNodeDeleted: reduxPayload?.DeleteNodeList?.length ? true : false,
    ReplaceNodesList: reduxPayload?.ReplaceNodesList,
    ReplaceTagList: reduxPayload?.ReplaceTagList,
    TreeChanges:reduxPayload?.TreeChanges || {},
    NodesList: reduxPayload?.NodesList,
    TagList: reduxPayload?.TagList,
    DescList: reduxPayload?.DescList,
    EditDescList: reduxPayload?.EditDescList,
    DeleteNodeList: reduxPayload?.DeleteNodeList,
    PRList: [],
    EditPrList: [],
    NodeTree: reduxPayload?.treeData?.[0] || {},
    CreateRecords:
      requestHeaderData?.RequestType === REQUEST_TYPE?.CREATE ||
      requestHeaderData?.RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
        ? reduxPayload?.changeLog
        : [],
    ChangeRecords:
      requestHeaderData?.RequestType === REQUEST_TYPE?.CHANGE ||
      requestHeaderData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
        ? reduxPayload?.changeLog
        : [],
    DisplayRecords: {},
    GeneralInfoID: reduxPayload?.GeneralInfoID || "",
    RequestPriority:
      requestHeaderData?.RequestPriority ||
      requestHeaderSlice?.requestPriority ||
      "",
    BusinessJustification: "",
    SAPorJEErrorCheck: true,
    BusinessSegment: "",
    HierarchyRegion: "REG-1",
    AAMNumber: "",
    IntermediateTaskCount: reduxPayload?.IntermediateTaskCount || "",
    validationDoneBy:
      type === "VALIDATE"
        ? ToChildHeaderdata?.RequestId
          ? "MDM Approval"
          : "Requestor"
        : "",
    duplicateNodeList: [],
    duplicateDescList: [],
    duplicateTagList: [],
    duplicatePrList: [],
    nodesListForDBDuplicateCheck: reduxPayload?.nodesListForDBDuplicateCheck || [],
    descListForDBDuplicateCheck: reduxPayload?.descListForDBDuplicateCheck || [],
    nodesMassListForDBDuplicateCheck: [],
    descMassListForDBDuplicateCheck: [],
    Torequestheaderdata: {
      RequestId:
        requestHeaderSlice?.requestId ?? requestHeaderData?.RequestId ?? "",
      ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
      ReqCreatedOn: `/Date(${new Date(
        requestHeaderData?.ReqCreatedOn
      ).getTime()})/`,
      ReqUpdatedOn: `/Date(${new Date(
        requestHeaderData?.ReqUpdatedOn
      ).getTime()})/`,
      RequestType: requestHeaderData?.RequestType || "",
      RequestPrefix: requestHeaderSlice?.requestPrefix
        ? requestHeaderSlice?.requestPrefix
        : requestHeaderData?.RequestPrefix || "",
      RequestPriority: requestHeaderData?.RequestPriority || "",
      RequestDesc: requestHeaderData?.RequestDesc || "",
      RequestStatus: requestHeaderData?.RequestStatus || "",
      FirstProd: "",
      LaunchDate: "",
      LeadingCat: "",
      Division: "",
      TemplateName: requestHeaderData?.TemplateName || "",
      FieldName: "",
      Region: "",
      FilterDetails: null,
      IsBifurcated:
        requestHeaderSlice?.isBifurcated ??
        requestHeaderData?.IsBifurcated ??
        "",
      IsHierarchyGroup: true,
    },
    ToChildHeaderdata: isrequestId ? ToChildHeaderdata : {},
    ChangeLogId: reduxPayload?.ChangeLogId || "",
    ErrorLogId: reduxPayload?.ErrorLogId || "",
  };

  return finalPayload;
};

export const createPayloadForCCG = (
  reduxPayload,
  requestHeaderSlice,
  isrequestId,
  task,
  dynamicData,
  requestorPayload,
  type
) => {
  let requestHeaderData = reduxPayload?.requestHeaderData;
  let ToChildHeaderdata = reduxPayload?.ToChildHeaderdata;

  let finalPayload = {
    CostCenterGroupID: reduxPayload?.CostCenterGroupID || "",
    RequestID:
      requestHeaderSlice?.requestId || requestHeaderData?.RequestId || "",
    TotalIntermediateTasks: reduxPayload?.TotalIntermediateTasks || "",
    ReqCreatedBy:
      requestHeaderData?.ReqCreatedBy ||
      requestHeaderSlice?.reqCreatedBy ||
    "",
    ReqCreatedOn: `/Date(${new Date(
      requestHeaderData?.ReqCreatedOn
    ).getTime()})/`,
    RequestStatus:
      requestHeaderData?.RequestStatus ||
      requestHeaderSlice?.requestStatus ||
      "",
    TaskId: task?.TaskId || "",
    CreationId: reduxPayload?.CreationId || "",
    EditId: reduxPayload?.EditId || "",
    DeleteId: reduxPayload?.DeleteId || "",
    MassCreationId: reduxPayload?.MassCreationId || "",
    MassEditId: reduxPayload?.MassEditId || "",
    MassDeleteId: reduxPayload?.MassDeleteId || "",
    RequestType:
      requestHeaderData?.RequestType || requestHeaderSlice?.requestType || "",
    Remarks: dynamicData?.Comments || "",
    Info:
      requestHeaderData?.RequestType || requestHeaderSlice?.requestType || "",
    ControllingArea:
      requestorPayload?.["Controlling Area"]?.[0]?.code ||
      reduxPayload?.ControllingArea ||
      "",
    ParentNode:
      requestorPayload?.["Cost Center Group"]?.[0]?.code ||
      requestorPayload?.["Cost Center Group"] ||
      reduxPayload?.ParentNode ||
      "",
    ParentDesc:
      requestorPayload?.["Cost Center Group Description"]?.[0]?.code ||
      requestorPayload?.["Cost Center Group Desription"] ||
      reduxPayload?.ParentDesc ||
      "",
    isMass:
      requestHeaderData?.RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD ||
      requestHeaderData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD,
    isTagAdded: reduxPayload?.TagList?.length ? true : false,
    isNodeAdded: reduxPayload?.NodesList?.length ? true : false,
    isTagReplaced: reduxPayload?.ReplaceTagList?.length ? true : false,
    isNodeReplaced: reduxPayload?.ReplaceNodesList?.length ? true : false,
    isNodeDeleted: reduxPayload?.DeleteNodeList?.length ? true : false,
    ReplaceNodesList: reduxPayload?.ReplaceNodesList,
    ReplaceTagList: reduxPayload?.ReplaceTagList,
    TreeChanges:reduxPayload?.TreeChanges || {},
    NodesList: reduxPayload?.NodesList,
    TagList: reduxPayload?.TagList,
    DescList: reduxPayload?.DescList,
    EditDescList: reduxPayload?.EditDescList,
    DeleteNodeList: reduxPayload?.DeleteNodeList,
    PRList: [],
    EditPrList: [],
    NodeTree: reduxPayload?.treeData?.[0] || {},
    CreateRecords:
      requestHeaderData?.RequestType === REQUEST_TYPE?.CREATE ||
      requestHeaderData?.RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
        ? reduxPayload?.changeLog
        : [],
    ChangeRecords:
      requestHeaderData?.RequestType === REQUEST_TYPE?.CHANGE ||
      requestHeaderData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
        ? reduxPayload?.changeLog
        : [],
    DisplayRecords: {},
    GeneralInfoID: reduxPayload?.GeneralInfoID || "",
    RequestPriority:
      requestHeaderData?.RequestPriority ||
      requestHeaderSlice?.requestPriority ||
      "",
    BusinessJustification: "",
    SAPorJEErrorCheck: true,
    BusinessSegment: "",
    HierarchyRegion: "REG-1",
    AAMNumber: "",
    IntermediateTaskCount: reduxPayload?.IntermediateTaskCount || "",
    validationDoneBy:
      type === "VALIDATE"
        ? ToChildHeaderdata?.RequestId
          ? "MDM Approval"
          : "Requestor"
        : "",
    duplicateNodeList: [],
    duplicateDescList: [],
    duplicateTagList: [],
    nodesListForDBDuplicateCheck: reduxPayload?.nodesListForDBDuplicateCheck || [],
    descListForDBDuplicateCheck: reduxPayload?.descListForDBDuplicateCheck || [],
    nodesMassListForDBDuplicateCheck: [],
    descMassListForDBDuplicateCheck: [],
    Torequestheaderdata: {
      RequestId:
        requestHeaderSlice?.requestId ?? requestHeaderData?.RequestId ?? "",
      ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
      ReqCreatedOn: `/Date(${new Date(
        requestHeaderData?.ReqCreatedOn
      ).getTime()})/`,
      ReqUpdatedOn: `/Date(${new Date(
        requestHeaderData?.ReqUpdatedOn
      ).getTime()})/`,
      RequestType: requestHeaderData?.RequestType || "",
      RequestPrefix: requestHeaderSlice?.requestPrefix
        ? requestHeaderSlice?.requestPrefix
        : requestHeaderData?.RequestPrefix || "",
      RequestPriority: requestHeaderData?.RequestPriority || "",
      RequestDesc: requestHeaderData?.RequestDesc || "",
      RequestStatus: requestHeaderData?.RequestStatus || "",
      FirstProd: "",
      LaunchDate: "",
      LeadingCat: "",
      Division: "",
      TemplateName: requestHeaderData?.TemplateName || "",
      FieldName: "",
      Region: "",
      FilterDetails: null,
      IsBifurcated:
        requestHeaderSlice?.isBifurcated ??
        requestHeaderData?.IsBifurcated ??
        "",
      IsHierarchyGroup: true,
    },
    ToChildHeaderdata: isrequestId ? ToChildHeaderdata : {},
    ChangeLogId: reduxPayload?.ChangeLogId || "",
    ErrorLogId: reduxPayload?.ErrorLogId || "",
  };

  return finalPayload;
};

export const getCompanyCode = (CA, rolePrefix) => {
  const hSuccess = (data) => {
    setDropdownDataCompany(data.body);
    dispatch({
      type: "SET_DROPDOWN",
      payload: { keyName: "CompanyCode", data: data.body },
    });
  };

  const hError = (error) => {
    console.log(error);
  };

  doAjax(
    `/${destination_ProfitCenter_Mass}/data/getCompCodeBasedOnControllingArea?controllingArea=${CA}&rolePrefix=${rolePrefix}`,
    "get",
    hSuccess,
    hError
  );
};
  export const getCompanyCodeBasedOnControllingArea = (CA,rolePrefix) => {
      const hSuccess = (data) => {
              setDropdownDataCompany(data.body);
              dispatch(setDropDown({ keyName: "CompCode", data: data.body }));
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        `/${destination_CostCenter_Mass}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${CA}&rolePrefix=${rolePrefix}`,
        "get",
        hSuccess,
        hError
      );
    };
export const fetchRegionBasedOnCountry = (countryCode, dispatch, uniqueId) => {
  const hSuccess = (data) => {
    dispatch(
      setDependentDropdown({
        keyName: "Regio",
        data: data?.body || [],
        keyName2: uniqueId,
      })
    );
  };

  const hError = (error) => {
    console.log(error);
  };

  doAjax(
    `/${destination_ProfitCenter_Mass}/data/getRegionBasedOnCountry?country=${countryCode}`,
    "get",
    hSuccess,
    hError
  );
};
export const fetchCurrencyBasedOnCompCode = (compCode, dispatch, uniqueId) => {
  const hSuccess = (data) => {
    dispatch(setDropDownAction({
          keyName: "Currency",
          data: data?.body || [],
      }))
    // dispatch(
    //   setDependentDropdown({
    //     keyName: "Currency",
    //     data: data?.body || [],
    //     keyName2: uniqueId,
    //   })
    // );
    dispatch(
      updateModuleFieldDataCC({
        uniqueId: uniqueId,
        keyName: "Currency",
        data: data?.body?.[0]?.code || "",
      })
    );
  };

  const hError = (error) => {
  };

  doAjax(
    `/${destination_CostCenter_Mass}/data/getCurrency?companyCode=${compCode}`,
    "get",
    hSuccess,
    hError
  );
};

export const getProfitCenterGrp = (coa = "ETCA", dispatch, selectedRowId) => {
  const hSuccess = (data) => {
    dispatch(
      setDependentDropdown({
        keyName: "PrctrHierGrp",
        data: data?.body || [],
        keyName2: selectedRowId,
      })
    );
  };

  const hError = (error) => {
  };

  doAjax(
    `/${destination_ProfitCenter_Mass}/data/getProfitCtrGroup?controllingArea=${coa}`,
    "get",
    hSuccess,
    hError
  );
};
export const fetchProfitCenter = (coa, dispatch, uniqueId) => {
  const hSuccess = (data) => {
    dispatch(
      setDependentDropdown({
        keyName: "ProfitCtr",
        data: data?.body || [],
        keyName2: uniqueId,
      })
    );
  };

  const hError = (error) => {
  };

  doAjax(
    `/${destination_CostCenter_Mass}/data/getProfitCenterAsPerControllingArea?controllingArea=${coa}`,
    "get",
    hSuccess,
    hError
  );
};
export const getProfitCenter = (CA,compcode,dispatch) => {
    const payload = {
      controllingArea: CA,
      companyCode: compcode,
      top: "100",
      skip: "0",
    };
    const hSuccess = (data) => {
            dispatch(setDropDownAction({
            keyName:"ProfitCtr",
            data: data?.body?.list || [],
      }))
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCentersNo`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
export const fetchRegionBasedOnCountryCC = (
  countryCode,
  dispatch,
  uniqueId
) => {
  const hSuccess = (data) => {
                dispatch(setDropDownAction({
            keyName:"AddrRegion",
            data: data?.body || [],
      }))
  };

  const hError = (error) => {
  };

  doAjax(
    `/${destination_CostCenter_Mass}/data/getRegionBasedOnCountry?country=${countryCode}`,
    "get",
    hSuccess,
    hError
  );
};

const getLockIndicatorValue = (
  CCCategory,
  TemplateName,
  ExistingBlockingStatus,
  LockIndicator,
  lockIndicatorMap
) => {

  if (TemplateName === "Block") {
    return true;
  } else if (TemplateName === "All Other Fields") {
    if (ExistingBlockingStatus === "Unblock") {
      return;
    } else if (ExistingBlockingStatus === "Block") {
      const match = lockIndicatorMap.find((obj) =>
        obj.hasOwnProperty(CCCategory)
      );
      if (match) {
        const indicators = match[CCCategory];
        if (indicators.includes(LockIndicator)) {
          return true;
        }
      }
    }
  } else if (TemplateName === "Change Cost Center Category & FERC Indicator") {
    if (ExistingBlockingStatus === "Unblock") {
      const match = lockIndicatorMap.find((obj) =>
        obj.hasOwnProperty(CCCategory)
      );
      if (match) {
        const indicators = match[CCCategory];
        if (indicators.includes(LockIndicator)) {
          return true;
        }
      }
    } else if (ExistingBlockingStatus === "Block") {
      return;
    }
  }
};

export const transformProfitCenterResponseChange = (apiResponses) => {
  return apiResponses.map((item, index) => ({
    id: index, // required for DataGrid rows
    profitCenter: item.ProfitCenter,
    controllingArea: item?.COArea || "",
    profitCenterName: item?.ProfitCenterName || "",
    ProfitCenterID: item?.ProfitCenterID || "",
    RequestID: item?.RequestID || "",
    GeneralInfoId: item?.ToGeneralInfoData?.GeneralInfoId || "",
    ChangeLogId: item?.TochangeLogData?.ChangeLogId || "",
    ProfitCenterErrorID: item?.ProfitCenterErrorID || "",
    description: item.Description || "",
    segment: item.Segment || "",
    businessSegment: item.ToGeneralInfoData?.BusinessSegment || "",
    pcaamnum: item.PCAAMNumber || "",
    personResponsible: item.PersonResponsible || "",
    userResponsible: item.InChargeUser || "",
    street: item.Street || "",
    city: item.City || "",
    region: item.Regio || "",
    pocode: item.PostalCode || "",
    country: item.Country || "",
    name1: item.Name1 || "",
    name2: item.Name2 || "",
    name3: item.Name3 || "",
    name4: item.Name4 || "",
    companyCode: Array.isArray(item?.ToCompanycode) && item.ToCompanycode[0]?.CompanyCode 
  ? item.ToCompanycode[0].CompanyCode 
  : "",
CompCodeID: Array.isArray(item?.ToCompanycode) && item.ToCompanycode[0]?.CompCodeID 
  ? item.ToCompanycode[0].CompCodeID 
  : "",

    lockIndicator: item?.LockIndicator ? "X" : "",
  }));
};
export const transformCostCenterResponseChange = (apiResponses) => {

  return apiResponses.map((item) => {
    const data = item?.ToCostCenterData?.[0] || {};
    return {
      id: data?.CostCenterID,
      costCenterId: data?.CostCenterID,
      costCenterHeaderID: item?.CostCenterHeaderID,
      costCenterErrorId: item?.ToCostCenterErrorData?.CostCenterErrorId,
      costCenter: data?.Costcenter,
      controllingArea: item?.ControllingArea,
      requestStatus: item?.RequestStatus,
      requestType: item?.RequestType,
      massRequestStatus: item?.MassRequestStatus,
      templateName: item?.TemplateName,
      templateHeaders: item?.TemplateHeaders,
      changedFields: item?.ChangedFields,
      generalInfoID: item?.GeneralInfoID,
      requestPriority: item?.RequestPriority,
      businessJustification: item?.BusinessJustification,
      sAPorJEErrorCheck: item?.SAPorJEErrorCheck,
      fERCIndicator: item?.FERCIndicator,
      businessSegment: item?.BusinessSegment,
      hierarchyRegion: item?.HierarchyRegion,
      aAMNumber: item?.AAMNumber,
      totalIntermediateTasks: item?.TotalIntermediateTasks,
      isFirstSynCompleted: item?.IsFirstSynCompleted,
      tempLockIsSelectedForSyn: item?.TempLockIsSelectedForSyn,
      CostcenterType: data?.CostcenterType,
      functionalArea: data?.FuncAreaLong,
      currency: data?.Currency,
      profitCtr: data?.ProfitCtr,
      compCode: data?.CompCode,
      name: data?.Name,
      description: data?.Descript || "",
      userResponsible: data?.PersonInCharge || "",
      personResponsible: data?.PersonInChargeUser || "",
      createdBy: item?.historyTabDto?.ReqCreatedBy || "",
      validFrom: data?.ValidFrom || "",
      validTo: data?.ValidTo || "",
      city: data?.City || "",
      street: data?.Street || "",
      country: data?.Country || "",
      addrRegion: data?.AddrRegion || "",
      regio: data?.Regio || "", // renamed to avoid duplicate
      pocode: data?.PoBox || "",
      name1: data?.Name1 || "",
      name2: data?.Name2 || "",
      name3: data?.Name3 || "",
      name4: data?.Name4 || "",
      lockIndActualPrimaryCosts:
        data?.LockIndActualPrimaryCosts,
      lockIndPlanPrimaryCosts:
        data?.LockIndPlanPrimaryCosts,
      lockIndActSecondaryCosts:
        data?.LockIndActSecondaryCosts,
      lockIndPlanSecondaryCosts:
        data?.LockIndPlanSecondaryCosts,
      lockIndActualRevenues:
        data?.LockIndActualRevenues,
      lockIndPlanRevenues:
        data?.LockIndPlanRevenues,
      included: item?.SelectedByRequestorToDisplay ?? true,
      massEditId: item?.MassEditId ?? "",
      // ... (rest of the fields can stay as you had them)
    };
  });
};

export {
  getTimestamp,
  captureScreenShot,
  idGenerator,
  dynamicAddressValidation,
  timeStampToDate,
  showToastMessage,
  getColor_Status,
  getColor_priority,
  handleMultipleDownload,
  controller_UrlRedirecting,
  setStatusRecord,
  formValidator,
  getAddressList,
  fetchAddressData,
  capitalize,
  ASNidGenerator,
  getUserEmailId,
  checkIwaAccess,
  RETidGenerator,
  capitalizeByWord,
  multiConfirmationCheck,
  SESIDGenerator,
  PlanningMgmtIDGenerator,
  generateAccessList,
  ValidNavigateTo,
  updateTaskStatus,
  saveExcel,
  saveExcelMultiSheets,
  savePDF,
  exportFileAsPdf,
  downloadFileAsPdf,
};

export const createBOMPayload = (bomRows, tabFieldValues, payloadFields, createdRequestId, taskData) => {
  return bomRows.map((row) => {
    const rowId = row.id;
    const materialTabRows = tabFieldValues[rowId]?.Material || [];
    const documentTabRows = tabFieldValues[rowId]?.Document || [];
    let childRequestHeader;
    if (tabFieldValues[rowId]?.ChildRequestHeader) {
      childRequestHeader = {
        ...tabFieldValues[rowId].ChildRequestHeader,
        TaskId: taskData?.taskId || tabFieldValues[rowId].ChildRequestHeader.TaskId || "",
        CurrentLevel: taskData?.ATTRIBUTE_3 || "",
        CurrentLevelName: taskData?.ATTRIBUTE_4 || "",
        TaskName: taskData?.taskDesc || "",
        ApproverGroup: taskData?.ATTRIBUTE_5 || "",
      };
    } else {
      childRequestHeader = {};
    }

    return {
      BOMHeaderId: row?.BOMHeaderId || "",
      Material: row.material || "",
      Plant: row.plant?.code || row?.plant || "",
      TemplateName: "",
      MassCreationId: ((payloadFields?.RequestType === "Create" ) ? createdRequestId || payloadFields?.RequestId : ""),
      MassEditId: "",
      MassChildCreationId: childRequestHeader?.ChildRequestId || "",
      MassChildEditId: "",
      ChangeFields: "",
      ToBOMitem: materialTabRows.map((matRow) => ({
        BOMItemId: matRow.BOMItemId || "",
        ItemCateg: matRow.ItemCateg?.code || "",
        ItemNo: matRow.ItemNo || "",
        Component: matRow.Component || "",
        CompQty: matRow.CompQty || "",
        CompUnit: matRow.CompUnit || "",
        FixedQty: matRow.FixedQty || "",
        ItemText1: matRow.ItemText1 || "",
        ItemText2: matRow.ItemText2 || "",
        Sortstring: matRow.Sortstring || "",
        RelCost: matRow.RelCost || "",
        RelEngin: matRow.RelEngin || "",
        RelPmaint: matRow.RelPmaint || "",
        RelProd: matRow.RelProd || "",
        RelSales: matRow.RelSales || "",
        SparePart: matRow.SparePart || "",
        MatProvis: matRow.MatProvis || "",
        BulkMat: matRow.BulkMat || "",
        RecAllowd: matRow.RecAllowd || "",
        CompScrap: matRow.CompScrap || "",
        OpScrap: matRow.OpScrap || "",
        OpNetInd: matRow.OpNetInd || "",
        DistrKey: matRow.DistrKey || "",
        ExplType: matRow.ExplType || "",
        Spproctype: matRow.Spproctype || "",
        Supplyarea: matRow.Supplyarea || "",
        IssueLoc: matRow.IssueLoc || "",
        LeadTime: matRow.LeadTime || "",
        OpLeadTm: matRow.OpLeadTm || "",
        OpLtUnit: matRow.OpLtUnit || "",
        CoProduct: matRow.CoProduct || "",
        DisconGrp: matRow.DisconGrp || "",
        FollowGrp: matRow.FollowGrp || "",
        AiGroup: matRow.AiGroup || "",
        AiStrateg: matRow.AiStrateg || "",
        AiPrio: matRow.AiPrio || "",
        UsageProb: matRow.UsageProb || "",
        Refpoint: matRow.Refpoint || "",
        PmAssmbly: matRow.PmAssmbly || "",
        CostElem: matRow.CostElem || "",
        DelivTime: matRow.DelivTime || "",
        GrpTime: matRow.GrpTime || "",
        MatGroup: matRow.MatGroup || "",
        Price: matRow.Price || "",
        PriceUnit: matRow.PriceUnit || "",
        Currency: matRow.Currency || "",
        PurchGrp: matRow.PurchGrp || "",
        PurchOrg: matRow.PurchOrg || "",
        Vendor: matRow.Vendor || "",
        VsiNo: matRow.VsiNo || "",
        VsiQty: matRow.VsiQty || "",
        VsiSize1: matRow.VsiSize1 || "",
        VsiSize2: matRow.VsiSize2 || "",
        VsiSize3: matRow.VsiSize3 || "",
        VsiSzunit: matRow.VsiSzunit || "",
        VsiFormul: matRow.VsiFormul || "",
        Document: matRow.Document || "",
        DocType: matRow.DocType || "",
        DocPart: matRow.DocPart || "",
        DocVers: matRow.DocVers || "",
        Class: matRow.Class || "",
        ClassType: matRow.ClassType || "",
        ResItmCt: matRow.ResItmCt || "",
        SelCond: matRow.SelCond || "",
        ReqdComp: matRow.ReqdComp || "",
        MultSelec: matRow.MultSelec || "",
        RelHlconf: matRow.RelHlconf || "",
        CadInd: matRow.CadInd || "",
        ItmIdent: matRow.ItmIdent || "",
        ItemGuid: matRow.ItemGuid || "",
        ValidFrom: matRow.ValidFrom || "",
        ChangeNo: matRow.ChangeNo || "",
        Identifier: matRow.Identifier || "",
        UnloadPt: matRow.UnloadPt || "",
        GrRcpt: matRow.GrRcpt || "",
        SegmentValue: matRow.SegmentValue || "",
        FshCriticalComp: matRow.FshCriticalComp || "",
        FshCriticalLevel: matRow.FshCriticalLevel || "",
        Cufactor: matRow.Cufactor || "",
      })),
      ToBomdecription: [],
      ToBomdocumentation: documentTabRows.map((docRow) => ({
        BOMDocumentationId: docRow.BOMDocumentationId || "",
        ObjectId: docRow.ObjectId || "",
        Identifier: docRow.Identifier || "",
        BomNo: docRow.BomNo || "",
        ItemNode: docRow.ItemNode || "",
        ItemCount: docRow.ItemCount || "",
        DepIntern: docRow.DepIntern || "",
        DepExtern: docRow.DepExtern || "",
        Language: docRow.Language || "",
        LineNo: docRow.LineNo || "",
        TxtForm: docRow.TxtForm || "",
        TxtLine: docRow.TxtLine || "",
        LanguageIso: docRow.LanguageIso || "",
      })),
      Torequestheaderdata: {
        RequestId: createdRequestId ? createdRequestId : payloadFields?.RequestId || "",
        ReqCreatedBy: payloadFields?.ReqCreatedBy || "",
        ReqCreatedOn: payloadFields?.ReqCreatedOn || "",
        ReqUpdatedOn: payloadFields?.ReqUpdatedOn || "",
        RequestType: payloadFields?.RequestType || "",
        RequestPrefix: payloadFields?.RequestPrefix || "",
        RequestPriority: payloadFields?.RequestPriority || "",
        RequestDesc: payloadFields?.RequestDesc || "",
        RequestStatus: payloadFields?.RequestStatus || "",
        TemplateName: payloadFields?.TemplateName || "",
        FieldName: payloadFields?.FieldName || "",
        Region: payloadFields?.Region || "",
        filterDetails: payloadFields?.filterDetails || "",
        SapSystem: payloadFields?.SapSystem || "",
        IsBifurcated: payloadFields?.IsBifurcated || "",
        IncompleteChildTasks: payloadFields?.IncompleteChildTasks || "",
      },
      Tochildrequestheaderdata: childRequestHeader,
      Usage: row.bomUsage?.code || "",
      Alternative: row.altBom || "",
      ValidFrom: row.validFrom || "",
      ValidTo: row.validTo || "",
      ChangeNumber: "",
      RevisionLevel: "",
      BaseQuan: "",
      BaseUnit: "",
      BomStatus: "",
      AltText: "",
      Laboratory: "",
      DeleteInd: "",
      BomText: "",
      BomGroup: "",
      AuthGroup: "",
      CadInd: "",
      IdGuid: "",
      BomNo: "",
    };
  });
};

export const transformBOMDisplayResponseToRedux = (apiResponse) => {
  if (!apiResponse || !Array.isArray(apiResponse.body) || apiResponse.body.length === 0) {
    return {
      bomPayload: {},
      bomRows: [],
      tabFieldValues: {},
    };
  }

  const bomPayload = {
    ...(apiResponse.body[0]?.Torequestheaderdata || {}),
    childRequestId: apiResponse.body[0]?.Tochildrequestheaderdata?.ChildRequestId || "",
  };

  const bomRows = [];
  const tabFieldValues = {};

  apiResponse.body.forEach((bom) => {
    const key = bom.BOMHeaderId || bom.Torequestheaderdata?.RequestId || `bom-${Date.now()}-${Math.random()}`;
    bomRows.push({
      id: key,
      BOMHeaderId: bom.BOMHeaderId,
      material: bom.Material,
      plant: bom.Plant,
      bomUsage: bom.Usage,
      altBom: bom.Alternative,
      validFrom: bom.ValidFrom ? dayjs(bom.ValidFrom) : null,
      validTo: bom.ValidTo ? dayjs(bom.ValidTo) : null,
      BomNo: bom.BomNo,
    });
    const materialRows = (bom.ToBOMitem || []).map((item, idx) => ({
      ...item,
      id: item.BOMItemId || `mat-${idx}-${Date.now()}`,
    }));
    const documentRows = (bom.ToBomdocumentation || []).map((doc, idx) => ({
      ...doc,
      id: doc.BOMDocumentationId || `doc-${idx}-${Date.now()}`,
    }));
    tabFieldValues[key] = {
      Material: materialRows,
      Document: documentRows,
      General: bom.ToBomdecription || [],
      BOMErrorData: bom.Tobomerrordata || {},
      ChildRequestHeader: bom.Tochildrequestheaderdata || {},
    };
  });

  return {
    bomPayload,
    bomRows,
    tabFieldValues,
  };
};
