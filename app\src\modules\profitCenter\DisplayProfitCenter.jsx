import React, { useEffect, useState } from "react";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import {
  Backdrop,
  BottomNavigation,
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  Paper,
  Stack,
  Step,
  StepLabel,
  Stepper,
  Tab,
  Tabs,
  TextField,
  Typography,
} from "@mui/material";
import {
  button_Outlined,
  iconButton_SpacingSmall,
  container_Padding,
  button_Primary,
  outermostContainer_Information,
} from "@components/Common/commonStyles";

import { CheckCircleOutlineOutlined } from "@mui/icons-material";
import MarkunreadOutlinedIcon from "@mui/icons-material/MarkunreadOutlined";
import CloseIcon from "@mui/icons-material/Close";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
// import { checkIwaAccess, idGenerator, formValidator } from "src/functions";
import { checkIwaAccess,idGenerator,formValidator } from "./../../functions";

import { useDispatch, useSelector } from "react-redux";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import {
  destination_DocumentManagement,
  destination_ProfitCenter_Mass
} from "./../../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import moment from "moment/moment";
import { setDropDown } from "@app/dropDownDataSlice";
import ReusableDialog from "@components/Common/ReusableDialog";
import { MatDownload, MatView } from "@components/DocumentManagement/UtilDoc";

import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineSeparator,
  timelineItemClasses,
} from "@mui/lab";
import ReusableTable from "@components/Common/ReusableTable";
import {
  clearProfitCenter,
  clearProfitCenterPayload,
  setProfitCenterViewData,
} from "@app/profitCenterTabsSlice";

import { setPayloadWhole } from "@app/editPayloadSlice";
// import lookup from "../../../data/lookup.json";

import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import ChangeLog from "@components/Changelog/ChangeLog";
import ReusableAttachementAndComments from "@components/Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import { clearTaskData } from "@app/userManagementSlice";

import { clearCostCenterPayload } from "@app/costCenterTabSliceET";
import { clearSingleGLPayloadGI } from "@app/generalLedgerTabSlice";
import { clearCostCenter } from "@app/costCenterTabsSlice";
import EditableFieldForProfitCenter from "./EditableFieldForProfitCenter";
const DisplayProfitCenter = () => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [isDisplayMode, setIsDisplayMode] = useState(true);
  const [responseFromAPI, setResponseFromAPI] = useState({});
  const [factorsArray, setFactorsArray] = useState([]);
  const [costCenterDetails, setCostCenterDetails] = useState([]);
  const [iDs, setIds] = useState();
  const [value, setValue] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [successMsg, setsuccessMsg] = useState(false);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [blurLoading, setBlurLoading] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [validateFlag, setValidateFlag] = useState(false);
  const [openCorrectionDialog, setOpenCorrectionDialog] = useState(false);
  const [remarks, setRemarks] = useState("");
  const appSettings = useSelector((state) => state.appSettings);
  const [dispCompCode, setDispCompCode] = useState([]);
  const [comments, setComments] = useState([]);
  const [attachments, setAttachments] = useState([]);
  const [openRemarkDialog, setOpenRemarkDialog] = useState(false);
  const [submitForReviewDisabled, setSubmitForReviewDisabled] = useState(true);
  const [pcNumber, setPcNumber] = useState("");
  const [handleExtrabutton, setHandleExtrabutton] = useState(false);
  const [handleExtraText, setHandleExtraText] = useState("");
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [dialogType, setDialogType] = useState("");
  const [testRunStatus, setTestrunStatus] = useState(true);
  const [errorsFields, setErrorsFields] = useState([]);
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]); //chiranjit
  const [displayApiError, setDisplayApiError] = useState(false);
  const [openSnackbarValidation, setOpenSnackbarValidation] = useState(false); //chiranjit
  const [profitCenterNameinRow, setProfitCenterNameinRow] = useState([]);

  const [displayData,setDisplayData]=useState({})
  const [isRolePresent, setIsRolePresent] = useState(false);

  console.log("displayData",displayData)

  const [pageName, setPageName] = useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const profitCenterViewData = useSelector(
    (state) => state.profitCenter.profitCenterViewData
  );
  // console.log("profitCenterViewData",profitCenterViewData.length)
  let iwaAccessDataDisplay = useSelector(
    (state) =>
      state?.userManagement?.entitiesAndActivities?.["DisplayProfitCenter"]
  );
  let iwaAccessData = useSelector(
    (state) => state?.userManagement?.entitiesAndActivities?.["Profit Center"]
  );
  let iwaAccessDataChange = useSelector(
    (state) =>
      state?.userManagement?.entitiesAndActivities?.["ChangeProfitCenter"]
  );
  let userData = useSelector((state) => state?.userManagement?.userData);
  let taskData = useSelector((state) => state?.initialData?.IWMMyTask);

  const maskingRoleIDM = useSelector(
    (state) => state.applicationConfig.maskingRolesIDM
  );
  const maskingRoleApplication = useSelector(
    (state) => state.applicationConfig.maskingRolesApplication
  );

  let profitCenterRowData = location.state;
  console.log("profitCenterRowData", profitCenterRowData);
  let pcNumberFromCC = profitCenterRowData?.viewData?.["Basic Data"]?.["Basic Data"].find(field=>field?.fieldName==="Profit Center")?.value;
  console.log("pcNumberFromCC",pcNumberFromCC);
  //console.log(taskRowDetails?.requestId,"taskRowDetails?.requestId")
  const singleCCPayload = useSelector(
    (state) => state.costCenter.singleCCPayload
  );
  const compCodesTabDetails = useSelector(
    (state) => state.profitCenter.profitCenterCompCodes
  );
  let singlePCPayloadAfterChange = useSelector((state) => state.edit.payload);
  console.log(singlePCPayloadAfterChange, "singlePCPayloadAfterChange");
  const editedData = useSelector((state) => state.edit.payload);
  let taskRowDetails = useSelector((state) => state?.userManagement?.taskData);
  console.log(taskRowDetails?.requestId, "taskRowDetails?.requestId");
  console.log(taskData, "taskData==================");
  let requiredFieldTabWise = useSelector(
    (state) => state.profitCenter.requiredFields
  );
  console.log(requiredFieldTabWise, "required_field_for_data");
  var payload = {
    TaskId: taskRowDetails?.taskId ? taskRowDetails?.taskId : "",
    ProfitCenterID: iDs?.profitCenterId ? iDs?.profitCenterId : "",
    RequestID: "",
    Action:
      profitCenterRowData?.requestType === "Create"
        ? "I"
        : profitCenterRowData?.requestType === "Change"
        ? "U"
        : taskRowDetails?.requestType === "Create"
        ? "I"
        : taskRowDetails?.requestType === "Change"
        ? "U"
        : "U",
    TaskStatus: "",
    ReqCreatedBy: taskRowDetails?.createdBy ? taskRowDetails?.createdBy : userData?.emailId? userData?.emailId : "",
    ReqCreatedOn: taskRowDetails?.createdOn
      ? "/Date(" + taskRowDetails?.createdOn + ")/"
      : profitCenterRowData?.createdOn
      ? "/Date(" + Date.parse(profitCenterRowData?.createdOn) + ")/"
      : "",
    RequestStatus: "",
    CreationId:
      taskRowDetails?.requestType === "Create"
        ? taskRowDetails?.requestId.slice(8)
        : profitCenterRowData?.requestType === "Create"
        ? profitCenterRowData?.requestId.slice(8)
        : "",
    EditId:
      taskRowDetails?.requestType === "Change"
        ? taskRowDetails?.requestId.slice(8)
        : profitCenterRowData?.requestType === "Change"
        ? profitCenterRowData?.requestId.slice(8)
        : "",
    DeleteId: "",
    MassCreationId: "",
    MassEditId: "",
    MassDeleteId: "",
    RequestType: "",
    MassRequestStatus: "",
    Remarks: remarks ? remarks : "",
    PrctrName: editedData?.Name ? editedData?.Name : "",
    LongText: editedData?.Description ? editedData?.Description : "",
    InChargeUser: editedData?.UserResponsible
      ? editedData?.UserResponsible
      : "",
    InCharge: editedData?.PersonResponsible
      ? editedData?.PersonResponsible
      : "",
    Department: editedData?.Department ? editedData?.Department : "",
    PrctrHierGrp: editedData?.ProfitCtrGroup ? editedData?.ProfitCtrGroup : "",
    Segment: editedData?.Segment ? editedData?.Segment : "",
    LockInd: editedData?.Lockindicator === true ? "X" : "",
    Template: editedData?.FormPlanningTemp ? editedData?.FormPlanningTemp : "",
    Title: editedData?.Title ? editedData?.Title : "",
    Name1: editedData?.Name1 ? editedData?.Name1 : "",
    Name2: editedData?.Name2 ? editedData?.Name2 : "",
    Name3: editedData?.Name3 ? editedData?.Name3 : "",
    Name4: editedData?.Name4 ? editedData?.Name4 : "",
    Street: editedData?.Street ? editedData?.Street : "",
    City: editedData?.City ? editedData?.City : "",
    District: editedData?.District ? editedData?.District : "",
    Country: editedData?.CountryReg ? editedData?.CountryReg : "",
    Taxjurcode: editedData?.TaxJur ? editedData?.TaxJur : "",
    PoBox: editedData?.POBox ? editedData?.POBox : "",
    PostlCode: editedData?.PostalCode ? editedData?.PostalCode : "",
    PobxPcd: editedData?.POBoxPCode ? editedData?.POBoxPCode : "",
    Region: editedData?.Region ? editedData?.Region : "",
    Langu: editedData?.Language ? editedData?.Language : "EN",
    Telephone: editedData?.Telephone1 ? editedData?.Telephone1 : "",
    Telephone2: editedData?.Telephone2 ? editedData?.Telephone2 : "",
    Telebox: editedData?.Telebox ? editedData?.Telebox : "",
    Telex: editedData?.Telex ? editedData?.Telex : "",
    FaxNumber: editedData?.FaxNumber ? editedData?.FaxNumber : "",
    Teletex: editedData?.Teletex ? editedData?.Teletex : "",
    Printer: editedData?.Printername ? editedData?.Printername : "",
    DataLine: editedData?.Dataline ? editedData?.Dataline : "",
    ProfitCenter: profitCenterRowData?.profitCenter
      ? profitCenterRowData?.profitCenter
      : taskData?.body?.profitCenter,
    ControllingArea: iDs?.controllingArea
      ? iDs?.controllingArea
      : taskData?.body?.controllingArea,
    ValidfromDate: editedData?.AnalysisPeriodFrom
      ? "/Date(" + editedData?.AnalysisPeriodFrom + ")/"
      : "",
    ValidtoDate: editedData?.AnalysisPeriodTo
      ? "/Date(" + editedData?.AnalysisPeriodTo + ")/"
      : "",
    Testrun: testRunStatus,
    Countryiso: "",
    LanguIso: "",
    Logsystem: "",
    ToCompanycode: dispCompCode?.map((compCode) => {
      return {
        CompCodeID: compCode?.ccId ? compCode?.ccId : "",
        CompanyName: compCode?.companyName ? compCode?.companyName : "",
        AssignToPrctr: compCode?.assigned === true ? "X" : "",
        CompCode: compCode?.companyCodes ? compCode?.companyCodes : "",
      };
    }),
  };
  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1,
      hide: true,
    },
    // {
    //   field: "docType",
    //   headerName: "Document Type",
    //   flex: 1,
    // },
    {
      field: "docName",
      headerName: "Document Name",
      flex: 1,
    },
    {
      field: "uploadedOn",
      headerName: "Uploaded On",
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "uploadedBy",
      headerName: "Uploaded By",
      sortable: false,
      flex: 1,
    },
    {
      field: "attachmentType",
      headerName: "Attachment Type",
      sortable: false,
      flex: 1,
    },
    {
      field: "action",
      headerName: "Action",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => {
        return (
          <>
            {/* <MatView index={cellValues.row.id} name={cellValues.row.docName} /> */}
            <MatDownload
              index={cellValues.row.id}
              name={cellValues.row.docName}
            />
          </>
        );
      },
    },
  ];
  function removeHiddenAndEmptyObjects(obj) {
    for (let prop in obj) {
      if (obj.hasOwnProperty(prop)) {
        if (Array.isArray(obj[prop])) {
          // If property is an array, iterate over its elements
          obj[prop] = obj[prop].filter((item) => item.visibility !== "Hidden");
          console.log(obj[prop], "obj[prop]===")
          if (obj[prop].length === 0) {
            // Remove the property if the array is empty
            delete obj[prop];
          }
        } else if (typeof obj[prop] === "object") {
          // If property is an object, recursively call the function
          obj[prop] = removeHiddenAndEmptyObjects(obj[prop]);
          if (Object.keys(obj[prop]).length === 0) {
            // Remove the property if the object is empty
            delete obj[prop];
          }
        }
      }
    }
    return obj;
  }
  const getProfitCenterDisplayData = () => {
    setIsLoading(true);
    var displayPayload = {
          id: profitCenterRowData?.reqStatus ? profitCenterRowData?.id : "",
          requestId: profitCenterRowData?.requestId ? profitCenterRowData?.requestId?.slice(8) : "",
          profitCenter: profitCenterRowData?.profitCenter
            ? profitCenterRowData?.profitCenter
            : pcNumberFromCC
            ? pcNumberFromCC
            : "",
          controllingArea: profitCenterRowData?.controllingArea
            ? profitCenterRowData?.controllingArea
            : "",
          reqStatus: profitCenterRowData?.reqStatus
            ? profitCenterRowData?.reqStatus
            : "Approved",
            dtName: "MDG_PC_FIELD_CONFIG",
            version: "v1",
          screenName: "Display",
          // costCenter: profitCenterRowData.costCenters?profitCenterRowData?.costCenters:"",
        };

    const hSuccess = (data) => {
      // let data=demoData
      if (data.statusCode === 200) {
        setIsLoading(false);
        const responseBody = data.body.viewData;
        let removeHiddenVisibilityEachlevel = removeHiddenAndEmptyObjects(responseBody)
        console.log(removeHiddenVisibilityEachlevel, "removeHiddenVisibilityEachlevel");
        //setOnlyDispayKey(removeHiddenVisibilityEachlevel)
        const responseIDs = data.body;
        setDisplayData(data.body)
        dispatch(setProfitCenterViewData(removeHiddenVisibilityEachlevel));
        // dispatch(setProfitCenterViewData(responseBody));
        // console.log(responseBody,"dcdcdc");
        setProfitCenterNameinRow(
          getValueForFieldName(
            responseBody["Basic Data"]?.["General Data"],
            "Name"
          )
        );
        const categoryKeys = Object.keys(removeHiddenVisibilityEachlevel);
        console.log("categoryKeys",categoryKeys)
        setFactorsArray(categoryKeys);
        const mappedData = categoryKeys.map((category) => ({
          category,
          data: responseBody[category],
          setIsEditMode,
        }));

        setCostCenterDetails(mappedData);
        checkErrorFields(responseBody);
        setIds(responseIDs);
        let compcodeData =
          data?.body?.viewData["Comp Codes"][
            "Company Code Assignment for Profit Center"
          ];
        console.log("compcodedata", compcodeData);
        setDispCompCode(
          _.zip(
            compcodeData[0].value,
            compcodeData[1].value,
            compcodeData[2].value
          ).map((item, index) => {
            console.log("ccitem", item);
            return {
              id: index,
              companyCodes: item[0]?.split("$$$")[0]
                ? item[0]?.split("$$$")[0]
                : "",
              companyName: item[1]?.split("$$$")[0]
                ? item[1]?.split("$$$")[0]
                : "",
              assigned: item[2] ? item[2] : "",
              ccId: item[0]?.split("$$$")[1] ?? "",
            };
          })
        );

        getRegion(
          responseBody["Address"]["Address Data"].find(
            (field) => field?.fieldName === "Country/Reg."
          ).value
        );
      } else {
        setDisplayApiError(true);
        setsuccessMsg(false);
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Unable to fetch data of Profit Center");
        setDialogType("danger");
        setMessageDialogSeverity("danger");
        // setMessageDialogExtra(true);
        setMessageDialogOK(true);
        setOpenMessageDialog(true);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/displayProfitCenter`,
      "post",
      hSuccess,
      hError,
      displayPayload
    );
    setPageName(displayPayload.screenName);
    console.log(displayPayload.screenName, "displayPayload"); //chiranjit
  };
  const onRevealClick = () => {
    const payload=displayData
    setIsLoading(true);
    

    const hSuccess = (data) => {
      // data.statusCode = 300;
      if (data.statusCode === 200) {
        setIsLoading(false);
        const responseBody = data.body.viewData;
        let removeHiddenVisibilityEachlevel = removeHiddenAndEmptyObjects(responseBody)
        console.log(removeHiddenVisibilityEachlevel, "removeHiddenVisibilityEachlevel");
        //setOnlyDispayKey(removeHiddenVisibilityEachlevel)
        const responseIDs = data.body;
        dispatch(setProfitCenterViewData(removeHiddenVisibilityEachlevel));
        // dispatch(setProfitCenterViewData(responseBody));
        // console.log(responseBody,"dcdcdc");
        setProfitCenterNameinRow(
          getValueForFieldName(
            responseBody["Basic Data"]?.["General Data"],
            "Name"
          )
        );
        const categoryKeys = Object.keys(removeHiddenVisibilityEachlevel);
        
        setFactorsArray(categoryKeys);
        const mappedData = categoryKeys.map((category) => ({
          category,
          data: responseBody[category],
          setIsEditMode,
        }));

        setCostCenterDetails(mappedData);
        checkErrorFields(responseBody);
        setIds(responseIDs);
        let compcodeData =
          data?.body?.viewData["Comp Codes"][
            "Company Code Assignment for Profit Center"
          ];
        console.log("compcodedata", compcodeData);
        setDispCompCode(
          _.zip(
            compcodeData[0].value,
            compcodeData[1].value,
            compcodeData[2].value
          ).map((item, index) => {
            console.log("ccitem", item);
            return {
              id: index,
              companyCodes: item[0]?.split("$$$")[0]
                ? item[0]?.split("$$$")[0]
                : "",
              companyName: item[1]?.split("$$$")[0]
                ? item[1]?.split("$$$")[0]
                : "",
              assigned: item[2] ? item[2] : "",
              ccId: item[0]?.split("$$$")[1] ?? "",
            };
          })
        );

        getRegion(
          responseBody["Address"]["Address Data"].find(
            (field) => field?.fieldName === "Country/Reg."
          ).value
        );
      } else {
        setDisplayApiError(true);
        setsuccessMsg(false);
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Unable to fetch data of Profit Center");
        setDialogType("danger");
        setMessageDialogSeverity("danger");
        // setMessageDialogExtra(true);
        setMessageDialogOK(true);
        setOpenMessageDialog(true);
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/unMask`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  console.log("dispcomp", dispCompCode);
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };
  const requiredFields = useSelector(
    //chiranjit
    (state) => state.profitCenter.requiredFields
  );
  const handleSnackBarCloseValidation = () => {
    setOpenSnackbarValidation(false);
  };

  const handleCheckValidationError = () => {
    //chiranjit
    return formValidator(
      singlePCPayloadAfterChange,
      requiredFieldTabWise,
      setFormValidationErrorItems
    );
  };
  console.log(requiredFields, "requiredFields");
  console.log(profitCenterNameinRow, "profitCenterNameinRow");
  const getRegion = (value) => {
    console.log("compcode", value);
    const hSuccess = (data) => {
      console.log("value", data);
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      console.log(error, "error in dojax");
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getRegionBasedOnCountry?country=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenterGroupBasedOnControllingArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCtrGroup", data: data.body }));
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCtrGroup?controllingArea=${
        taskData?.body?.controllingArea || profitCenterRowData?.controllingArea
      }`,
      "get",
      hSuccess,
      hError
    );
  };

  console.log(errorsFields, "error_field_arr");
  const checkErrorFields = (viewData) => {
    //chiranjit
    //console.log(viewData,"viewData===============")
    let error_field_arr = [];
    for (const section in viewData) {
      if (viewData.hasOwnProperty(section)) {
        for (const fieldGroup in viewData[section]) {
          if (viewData[section].hasOwnProperty(fieldGroup)) {
            const fields = viewData[section][fieldGroup];
            //console.log(fields,"fieldsview_data")
            for (const field of fields) {
              if (field.visibility === "0" || field.visibility === "Required") {
                console.log(field.fieldName, "field.fieldName");
                let required_field_name = field.fieldName.replace(/\s/g, "");
                error_field_arr.push(required_field_name);
              }
            }
          }
        }
      }
      setErrorsFields((prevState) => ({
        ...prevState,
        error_field_arr,
      }));
    }
    //return result;
  };

  // Loader and lookup for independent apis start
  const [apiCount, setApiCount] = useState(0);
  const fetchDynamicApiData = (keyName, endPoint) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: keyName, data: data.body }));
      // setIsLoading(false);
      setApiCount((prev) => prev + 1);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/data/${endPoint}`,
      "get",
      hSuccess,
      hError
    );
  };
  // const getAllLookups = () => {
  //   lookup?.profitCenter?.map((item) => {
  //     fetchDynamicApiData(item?.keyName, item?.endPoint);
  //   });
  // };
  // const loaderCount = () => {
  //   if (apiCount == lookup?.profitCenter?.length) {
  //     setIsLoading(false);
  //   } else {
  //     setIsLoading(true);
  //   }
  // };
  // useEffect(() => {
  //   loaderCount();
  // }, [apiCount]);

  // Loader and lookup for independent apis end

  useEffect(() => {
    setPcNumber(idGenerator("PC"));
  }, []);
  useEffect(() => {
    getProfitCenterDisplayData();
    getProfitCenterGroupBasedOnControllingArea();
  }, []);





useEffect(() => {
  if (
    !profitCenterViewData || 
    Object.keys(profitCenterViewData).length === 0
  ) {
    return;
  }

  handleSetEditedPayload();
}, [profitCenterViewData]);


  // useEffect(() => {
  //   // Extract the role from data2
  //   console.log("maskingRoleIDM",maskingRoleIDM);
  //   console.log("maskingRoleApplication",maskingRoleApplication);
  //   const roleExists = maskingRoleIDM.some(role =>
  //     maskingRoleApplication.includes(role.MDG_MASKING_ROLES)
  //   );
  // //   const roleExists = maskingRoleIDM?.MDG_MASKING_ACTION_TYPE?.some(action =>
  // //     maskingRoleApplication.includes(action.MDG_MASKING_ROLES)
  // // );
  // console.log('check role',roleExists)
  //   setIsRolePresent(roleExists);
  // }, [maskingRoleIDM, maskingRoleApplication]);

  useEffect(() => {
    // Extract the role from data2
    console.log("maskingRoleIDM",maskingRoleIDM);
    console.log("maskingRoleApplication",maskingRoleApplication);
    const roleExists = maskingRoleIDM?.some(role =>
      maskingRoleApplication.includes(role.MDG_MASKING_ROLES)
    );
  //   const roleExists = maskingRoleIDM?.MDG_MASKING_ACTION_TYPE?.some(action =>
  //     maskingRoleApplication.includes(action.MDG_MASKING_ROLES)
  // );
  console.log('check role',roleExists)
  if(maskingRoleApplication?.includes("Z4S:ALL:RTR:FI:UNMASK_FIN_MD")){
    setIsRolePresent(false);  
  }else{
    setIsRolePresent(roleExists);
  }
  }, [maskingRoleIDM, maskingRoleApplication]);

  const onEdit = () => {
    setIsEditMode(true);
    setIsDisplayMode(false);
  };
  const handleSnackBarOpenValidation = () => {
    setOpenSnackbarValidation(true);
  };
  const handleBack = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep - 1);
    //setActiveStep((prevActiveStep) => prevActiveStep - 1);
    //setActiveStep((prevActiveStep) => prevActiveStep - 1);
    setSubmitForReviewDisabled(true);
    const isValidation = handleCheckValidationError();

    if (isEditMode) {
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
        // dispatch(clearProfitCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
      // dispatch(clearProfitCenter());
    }
  };
  const handleNext = () => {
    //setActiveStep((prevActiveStep) => prevActiveStep + 1);
    //setActiveStep((prevActiveStep) => prevActiveStep + 1);
    //alert("coming1")
    const isValidation = handleCheckValidationError();
    if (isEditMode) {
      if (isValidation) {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        dispatch(clearProfitCenter());
      } else {
        handleSnackBarOpenValidation();
      }
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      // dispatch(clearProfitCenter());
    }
    //setActiveStep((prevActiveStep) => prevActiveStep + 1);
    //checkEveryrequiredfilledhavevalueornot()
  };
  const onCostCenterReview = () => {
    handleCostCenterReview();
  };
  const onCostCenterRereview = () => {
    handleCostCenterRereview();
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };
  const handleProfitCenterSubmitForApprovalCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Approval with ID NPS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Profit Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setTestrunStatus(true);
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/alter/profitCenterApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCostCenterReview = () => {
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body} `
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/profitCenterSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterSaveAsDraft = () => {
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body} `
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/alter/profitCenterAsDraft`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterCorrection = () => {
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/profitCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterApproveCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message} `);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Approving Profit Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setTestrunStatus(true);
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/createProfitCenterApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterRereview = () => {
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/profitCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterCorrectionCreate = () => {
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Profit Center has been Sent for Correction CMS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/profitCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterReviewCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Review with ID NPS${data.body} `
        );
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        const secondApiPayload = {
          artifactId: pcNumber,
          createdBy: userData?.emailId,
          artifactType: "ProfitCenter",
          requestId: `NPS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Profit Center");
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setTestrunStatus(true);
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/profitCenterSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleProfitCenterSaveAsDraftChange = () => {
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft ?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
    setDialogType("Change");
  };

  
  const handleProfitCenterSaveAsDraftCreate = () => {
    setMessageDialogSeverity(false);
    handleMessageDialogClickOpen();
    setMessageDialogTitle("Confirm");
    setMessageDialogMessage(`Do You Want to Save as Draft ?`);
    setHandleExtrabutton(true);
    setHandleExtraText("proceed");
    setDialogType("Create");
  };
  const handleProceedbutton = () => {
    handleMessageDialogClose();
    setIsLoading(true);
    console.log(dialogType, "dialogType");
    if (dialogType === "Change") {
      const hSuccess = (data) => {
        setIsLoading(false);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Profit Center Saved As Draft with ID CPS${data.body} `
          );
          setHandleExtrabutton(false);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          const secondApiPayload = {
            artifactId: pcNumber,
            createdBy: userData?.emailId,
            artifactType: "ProfitCenter",
            requestId: `CPS${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };

          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage("Failed Saving Profit Center");
          setHandleExtrabutton(false);
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
        `/${destination_ProfitCenter_Mass}/alter/changeProfitCenterAsDraft`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      const hSuccess = (data) => {
        setIsLoading(false);
        if (data.statusCode === 200) {
          console.log("success");
          setMessageDialogTitle("Create");
          setMessageDialogMessage(
            `Profit Center Saved As Draft with ID NPS${data.body} `
          );
          setHandleExtrabutton(false);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          const secondApiPayload = {
            artifactId: pcNumber,
            createdBy: userData?.emailId,
            artifactType: "ProfitCenter",
            requestId: `NPS${data?.body}`,
          };
          const secondApiSuccess = (secondApiData) => {
            console.log("Second API success", secondApiData);
            // Handle success for the second API if needed
          };

          const secondApiError = (secondApiError) => {
            console.error("Second API error", secondApiError);
            // Handle error for the second API if needed
          };
          // {requestId&&
          doAjax(
            `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
            "post",
            secondApiSuccess,
            secondApiError,
            secondApiPayload
          );
        } else {
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage("Failed Saving Profit Center");
          setHandleExtrabutton(false);
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          // setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        console.log(error);
      };
      doAjax(
        // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
        `/${destination_ProfitCenter_Mass}/alter/profitCenterAsDraft`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  const handleProfitCenterReviewChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Review with ID CPS${data.body} `
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        const secondApiPayload = {
          artifactId: pcNumber,
          createdBy: userData?.emailId,
          artifactType: "ProfitCenter",
          requestId: `CPS${data?.body}`,
        };
        const secondApiSuccess = (secondApiData) => {
          console.log("Second API success", secondApiData);
          // Handle success for the second API if needed
        };

        const secondApiError = (secondApiError) => {
          console.error("Second API error", secondApiError);
          // Handle error for the second API if needed
        };
        // {requestId&&
        doAjax(
          `/${destination_DocumentManagement}/documentManagement/updateDocRequestId`,
          "post",
          secondApiSuccess,
          secondApiError,
          secondApiPayload
        );
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Profit Center");
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setTestrunStatus(true);
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/changeProfitCenterSubmitForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleProfitCenterApproveChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 201) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(`${data.message} `);
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Saving Profit Center");
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setTestrunStatus(true);
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/changeProfitCenterApproved`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterSubmitChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Approval with ID CPS${data.body} `
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage("Failed Submitting Profit Center");
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setTestrunStatus(true);
        // setIsLoading(false);
      }
      handleClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/changeProfitCenterApprovalSubmit`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleProfitCenterCorrectionChange = () => {
    console.log("apicallllllllll");
    const hSuccess = (data) => {
      setMessageDialogMessage(
        `Create id generated for Data Owners CPS${data.body}`
      );
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/changeProfitCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const onValidateProfitCenterApprover = () => {
    setBlurLoading(true);
    const hSuccess = (data) => {
      if (data.statusCode === 201) {
        // Handle success
        setBlurLoading(false);
        setMessageDialogTitle("Create");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. Profit Center can be Sent for Review`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);
        setSubmitForReviewDisabled(false);
      } else {
        // Handle error
        setBlurLoading(false);
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        if(data?.body?.message?.length != 0){
          const content = (
            <Typography component="div">
              <ul>
                {data?.body?.message.map((item, index) => (
                  <li key={index}>
                    {item}
                  </li>
                ))}
              </ul>
            </Typography>
          );
          setMessageDialogMessage(content);
        }else{
          const content  = data.body.value
          setMessageDialogMessage(content);
        }
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };
    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_ProfitCenter_Mass}/alter/validateSingleProfitCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const onValidateProfitCenter = () => {
    setBlurLoading(true);
    console.log(
      (profitCenterRowData?.requestType,
      "++++",
      taskRowDetails?.requestType,
      "requesttype4")
    );
    console.log(pageName, "screenName56"); //
    //chiranjit
    // Define duplicateCheckPayload outside of the onValidateCostCenter function
    console.log(iDs, "action4");
    const duplicateCheckPayload = {
      coArea: iDs?.controllingArea
        ? iDs?.controllingArea
        : taskData?.body?.controllingArea,
      name: editedData?.Name ? editedData?.Name?.toUpperCase() : "",
    };
    console.log(duplicateCheckPayload, "duplicateCheckPayload");

    // const isValidation = handleCheckValidationError();
    // if (isValidation) {
    const hSuccess = (data) => {
      if (data.statusCode === 201) {
        // Handle success
        setMessageDialogTitle("Create");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `All Data has been Validated. Profit Center can be Sent for Review`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
        setValidateFlag(true);

        // Now, make the duplicate check API call
        // Ensure that the conditions for making the duplicate check API call are met

        if (
          duplicateCheckPayload.coArea !== "" ||
          duplicateCheckPayload.name !== ""
        ) {
          // if (
          //   duplicateCheckPayload?.name?.toUpperCase() ===
          //     profitCenterRowData?.profitCenterName?.toUpperCase() &&
          //   pageName === "Change"
          // ) {
          setSubmitForReviewDisabled(false);
          if (
            duplicateCheckPayload?.name?.toUpperCase() ===
            profitCenterNameinRow?.toUpperCase()
          ) {
            // new && (pageName === 'Change') added for skip duplicate check if not any changes
            setBlurLoading(false);
          } else {
            setBlurLoading(false);
            doAjax(
              `/${destination_ProfitCenter_Mass}/alter/fetchPCDescriptionDupliChk`,
              "post",
              hDuplicateCheckSuccess,
              hDuplicateCheckError,
              duplicateCheckPayload
            );
          }
          //}
        }
      } else {
        // Handle error
        setBlurLoading(false);
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        if(data?.body?.message?.length != 0){
          const content = (
            <Typography component="div">
              <ul>
                {data?.body?.message.map((item, index) => (
                  <li key={index}>
                    {item}
                  </li>
                ))}
              </ul>
            </Typography>
          );
          setMessageDialogMessage(content);
        }else{
          const content  = data.body.value
          setMessageDialogMessage(content);
        }
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
    };

    const hDuplicateCheckSuccess = (data) => {
      // Handle success of duplicate check
      if (
        data.body.length === 0 ||
        !data.body.some(
          (item) => item.toUpperCase() === duplicateCheckPayload.name
        )
      ) {
        // No direct match, enable the "Submit for Review" button
        setBlurLoading(false);
        setSubmitForReviewDisabled(false);
      } else {
        // Handle direct match
        setBlurLoading(false);
        setMessageDialogTitle("Duplicate Check");
        setsuccessMsg(false);
        setMessageDialogMessage(
          `There is a direct match for the Profit Center name. Please change the name.`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        setSubmitForReviewDisabled(true);
      }
    };

    const hDuplicateCheckError = (error) => {
      // Handle error of duplicate check
      console.log(error);
    };

    const hError = (error) => {
      console.log(error);
    };

    // Call the main API for validation
    doAjax(
      `/${destination_ProfitCenter_Mass}/alter/validateSingleProfitCenter`,
      "post",
      hSuccess,
      hError,
      payload
    );
    // } else {
    //   handleSnackBarOpenValidation();
    // }
  };
  // const onValidateProfitCenter = () => {
  //   const hSuccess = (data) => {
  //     setIsLoading();
  //     if (data.statusCode === 400) {
  //       setMessageDialogTitle("Error");
  //       setsuccessMsg(false);
  //       setMessageDialogMessage(
  //         `${
  //           data?.body?.message[0] ? data?.body?.message[0] : data?.body?.value
  //         }`
  //       );
  //       setMessageDialogSeverity("danger");
  //       setMessageDialogOK(false);
  //       setMessageDialogExtra(true);
  //       handleMessageDialogClickOpen();
  //       setIsLoading(false);
  //     } else {
  //       setMessageDialogTitle("Create");
  //       console.log("success");
  //       setMessageDialogTitle("Create");
  //       setMessageDialogMessage(
  //         `All Data has been Validated. Profit Center can be Send for Review`
  //       );
  //       setMessageDialogSeverity("success");
  //       setMessageDialogOK(false);
  //       setsuccessMsg(true);
  //       handleSnackBarOpen();
  //       setMessageDialogExtra(true);
  //       setIsLoading(false);
  //       setValidateFlag(true);
  //     }
  //     handleClose();
  //   };
  //   const hError = (error) => {
  //     console.log(error);
  //   };
  //   doAjax(
  //     `/${destination_ProfitCenter_Mass}/alter/validateSingleProfitCenter`,
  //     "post",
  //     hSuccess,
  //     hError,
  //     payload
  //   );
  // };

  const handleSnackBarClose = () => {
    if (validateFlag) {
      setopenSnackbar(false);
      setValidateFlag(false);
    } else {
      setopenSnackbar(false);
      navigate("/masterDataCockpit/profitCenter");
    }
  };
  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };
  const handleMessageDialogNavigate = () => {
    navigate("/masterDataCockpit/profitCenter");
  };
  const onProfitCenterApproveCreate = () => {
    setIsLoading(true);
    handleProfitCenterApproveCreate();
  };
  const onProfitCenterSubmitCreate = () => {
    setIsLoading(true);
    handleProfitCenterSubmitForApprovalCreate();
  };
  const onProfitCenterCorrectionCreate = () => {
    handleProfitCenterCorrectionCreate();
  };
  const onProfitCenterReviewCreate = () => {
    setIsLoading(true);
    handleProfitCenterReviewCreate();
  };
  const onProfitCenterSaveAsDraftChange = () => {
    //setIsLoading(true);

    handleProfitCenterSaveAsDraftChange();
  };
  const onProfitCenterSaveAsDraftCreate = () => {
    //setIsLoading(true);
    handleProfitCenterSaveAsDraftCreate();
  };
  const onProfitCenterApproveChange = () => {
    setIsLoading(true);
    handleProfitCenterApproveChange();
  };
  const onProfitCenterReviewChange = () => {
    setIsLoading(true);
    handleProfitCenterReviewChange();
  };
  const onProfitCenterSubmitChange = () => {
    setIsLoading(true);
    handleProfitCenterSubmitChange();
  };
  const onProfitCenterCorrectionChange = () => {
    handleProfitCenterCorrectionChange();
  };
  const onProfitCenterCorrection = () => {
    if (
      userData?.role === "MDM Steward" &&
      (profitCenterRowData?.requestType === "Create" ||
        taskRowDetails?.requestType === "Create")
    ) {
      setIsLoading(true);
      handleCorrectionMDMCreate();
    } else if (
      userData?.role === "MDM Steward" &&
      (profitCenterRowData?.requestType === "Change" ||
        taskRowDetails?.requestType === "Change")
    ) {
      setIsLoading(true);
      handleCorrectionMDMChange();
    } else if (
      userData?.role === "Approver" &&
      (profitCenterRowData?.requestType === "Create" ||
        taskRowDetails?.requestType === "Create")
    ) {
      setIsLoading(true);
      handleCorrectionApproverCreate();
    } else if (
      userData?.role === "Approver" &&
      (profitCenterRowData?.requestType === "Change" ||
        taskRowDetails?.requestType === "Change")
    ) {
      setIsLoading(true);
      handleCorrectionApproverChange();
    }
  };
  const handleCorrectionMDMCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Correction with ID NPS${data.body}`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting Profit Center for Correction"
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/profitCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionMDMChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Correction with ID CPS${data.body}`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting Profit Center for Correction"
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/changeProfitCenterSendForCorrection`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionApproverCreate = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Correction with ID NPS${data.body}`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting Profit Center for Correction"
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/profitCenterSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleCorrectionApproverChange = () => {
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        console.log("success");
        setMessageDialogTitle("Create");
        setMessageDialogMessage(
          `Profit Center Submitted for Correction with ID CPS${data.body}`
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("success");
        setMessageDialogOK(false);
        setsuccessMsg(true);
        handleSnackBarOpen();
        setMessageDialogExtra(true);
        // setIsLoading(false);
      } else {
        setMessageDialogTitle("Error");
        setsuccessMsg(false);
        setMessageDialogMessage(
          "Failed Submitting Profit Center for Correction"
        );
        setHandleExtrabutton(false);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        handleMessageDialogClickOpen();
        // setIsLoading(false);
      }
      handleCorrectionDialogClose();
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      // `/${destination_MaterialMgmt}/alter/editBasicDataApprovalSubmit`,
      `/${destination_ProfitCenter_Mass}/alter/changeProfitCenterSendForReview`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleOpenCorrectionDialog = () => {
    setTestrunStatus(false);
    setOpenCorrectionDialog(true);
  };
  const handleCorrectionDialogClose = () => {
    setRemarks("");
    setTestrunStatus(true);
    setOpenCorrectionDialog(false);
  };
  const handleRemarks = (e, value) => {
    //setRemarks(e.target.value);
    const newValue = e.target.value;
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
    } else {
      //let costCenterValue = e.target.value;
      let remarksUpperCase = newValue;
      setRemarks(remarksUpperCase);
    }
  };
  const handleRemarksDialogClose = () => {
    setRemarks("");
    setTestrunStatus(true);
    setOpenRemarkDialog(false);
  };
  const handleOpenRemarkDialog = () => {
    setTestrunStatus(false);
    setOpenRemarkDialog(true);
  };
  const onProfitCenterSubmitRemarks = () => {
    if (
      // userData?.role === "Finance" &&
      (profitCenterRowData?.requestType === "Create" ||
        taskRowDetails?.requestType === "Create") &&
      isEditMode
    ) {
      handleRemarksDialogClose();
      onProfitCenterReviewCreate();
    } else if (
      // userData?.role === "MDM Steward" &&
      (profitCenterRowData?.requestType === "Create" ||
        taskRowDetails?.requestType === "Create") &&
      !isEditMode
    ) {
      handleRemarksDialogClose();
      onProfitCenterSubmitCreate();
    } else if (
      // userData?.role === "Approver" &&
      (profitCenterRowData?.requestType === "Create" ||
        taskRowDetails?.requestType === "Create") &&
      !isEditMode
    ) {
      handleRemarksDialogClose();
      onProfitCenterApproveCreate();
    } else if (
      // userData?.role === "Finance" &&
      !profitCenterRowData?.requestType && //for change from master table
      isEditMode
    ) {
      handleRemarksDialogClose();
      onProfitCenterReviewChange();
    } else if (
      // userData?.role === "Finance" &&
      (profitCenterRowData?.requestType === "Change" ||
        taskRowDetails?.requestType === "Change") &&
      isEditMode
    ) {
      handleRemarksDialogClose();
      onProfitCenterReviewChange();
    } else if (
      // userData?.role === "MDM Steward" &&
      (profitCenterRowData?.requestType === "Change" ||
        taskRowDetails?.requestType === "Change") &&
      !isEditMode
    ) {
      handleRemarksDialogClose();
      onProfitCenterSubmitChange();
    } else if (
      // userData?.role === "Approver" &&
      (profitCenterRowData?.requestType === "Change" ||
        taskRowDetails?.requestType === "Change") &&
      !isEditMode
    ) {
      handleRemarksDialogClose();
      onProfitCenterApproveChange();
    }
  };

  const getAttachments = () => {
    let requestId = taskRowDetails?.requestId
      ? taskRowDetails?.requestId
      : profitCenterRowData?.requestId;

    let hSuccess = (data) => {
      //alert(data.documentDetailDtoList)
      console.log(data.documentDetailDtoList, "data.documentDetailDtoList");
      var attachmentRows = [];
      data.documentDetailDtoList.forEach((doc) => {
        console.log(data.documentDetailDtoList, "data.");
        var tempRow = {
          id: doc.documentId,
          docType: doc.fileType,
          docName: doc.fileName,
          uploadedOn: moment(doc.docCreationDate).format(appSettings.date),
          uploadedBy: doc.createdBy,
          attachmentType: doc.attachmentType,
        };
        console.log(tempRow, "tempRow");
        attachmentRows.push(tempRow);
        //
      });
      setAttachments(attachmentRows);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_DocumentManagement}/documentManagement/getDocByRequestId/${requestId}`,
      "get",
      hSuccess
    );
  };

  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };
  const openChangeLog = () => {
    setisChangeLogopen(true);
  };

  const handleSetEditedPayload = () => {
    console.log("hitttttt")
    let activeTabName = factorsArray[activeStep];
    console.log("activeTabName", activeTabName, factorsArray);
    let viewDataArray = Object.entries(profitCenterViewData);
    console.log("viewDataArray", viewDataArray);
    const toSetArray = {};
    viewDataArray.map((item) => {
      console.log("bottle", item[1]);
      let temp = Object.entries(item[1]);
      console.log("notebook", temp);
      temp?.forEach((fieldGroup) => {
        fieldGroup[1]?.forEach((field) => {
          toSetArray[
            field?.fieldName
              .replaceAll("(", "")
              .replaceAll(")", "")
              .replaceAll("/", "")
              .replaceAll("-", "")
              .replaceAll(".", "")
              .split(" ")
              .join("")
          ] = field.value;
        });
      });
      return item;
    });
    console.log("toSetArray", toSetArray);
    dispatch(setPayloadWhole(toSetArray));
  };

  const getValueForFieldName = (data, fieldName) => {
    //chiranjit
    //console.log("getvalueforfieldname", data, fieldName);
    const field = data?.find((field) => field?.fieldName === fieldName);
    return field ? field.value : "";
  };

  const getComments = () => {
    let requestId = taskRowDetails?.requestId
      ? taskRowDetails?.requestId
      : profitCenterRowData?.requestId;
    let hSuccess = (data) => {
      console.log("commentsdata", data);

      var commentRows = [];
      data.body.forEach((cmt) => {
        var tempRow = {
          id: cmt.requestId,
          comment: cmt.comment,
          user: cmt.createdByUser,
          createdAt: cmt.updatedAt,
        };
        commentRows.push(tempRow);
      });
      setComments(commentRows);
      console.log("commentrows", commentRows.length);
    };

    let hError = (error) => {
      console.log(error);
    };
    // invoiceHeaderData?.extInvNum &&
    doAjax(
      `/${destination_ProfitCenter_Mass}/activitylog/fetchTaskDetailsForRequestId?requestId=${requestId}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getAttachments();
    getComments();
  }, []);

  console.log("factorsarray", factorsArray);
  const filteredFactorsArray = factorsArray.filter(
    (factor) =>
      factor !== "General Information" && factor !== "Attachments & Comments"
  );
  console.log("filteredFactorsArray",filteredFactorsArray)
  const tabContents = filteredFactorsArray
    .map((item) => {
      const mdata = costCenterDetails.filter(
        (ii) => ii.category?.split(" ")[0] == item?.split(" ")[0]
      );
      if (mdata.length != 0) {
        return { category: item?.split(" ")[0], data: mdata[0].data };
      }
      // return { category: item?.split(" ")[0], data: ddata };
    })
    .map((categoryData, index) => {
      console.log("categorydata", categoryData);
      if (categoryData?.category == "Basic" && activeStep == 0) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        console.log("fieldDatatttt", field);
                        return (
                          <EditableFieldForProfitCenter
                            label={field.fieldName}
                            value={field.value}
                            length={field.maxLength}
                            data={editedData}
                            visibility={field.visibility}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            isEditMode={isEditMode}
                            type={field.fieldType}
                            field={field}
                            taskRequestId={profitCenterRowData?.requestId}
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } 
      else if (categoryData?.category == "Indicators" && activeStep == 1) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        return (
                          <EditableFieldForProfitCenter
                            label={field.fieldName}
                            value={field.value}
                            length={field.maxLength}
                            data={editedData}
                            visibility={field.visibility}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            isEditMode={isEditMode}
                            type={field.fieldType}
                            field={field}
                            taskRequestId={profitCenterRowData?.requestId}
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } 
      else if (categoryData?.category == "Comp" && activeStep == 2) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    {/* <CompanyCodesDisplayTable displayCompCode={dispCompCode} /> */}
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Address" && activeStep == 3) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        return (
                          <EditableFieldForProfitCenter
                            label={field.fieldName}
                            value={field.value}
                            length={field.maxLength}
                            data={editedData}
                            visibility={field.visibility}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            isEditMode={isEditMode}
                            type={field.fieldType}
                            field={field}
                            taskRequestId={profitCenterRowData?.requestId}
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Communication" && activeStep == 4) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        return (
                          <EditableFieldForProfitCenter
                            label={field.fieldName}
                            value={field.value}
                            length={field.maxLength}
                            data={editedData}
                            visibility={field.visibility}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            isEditMode={isEditMode}
                            type={field.fieldType}
                            field={field}
                            taskRequestId={profitCenterRowData?.requestId}
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if ((categoryData?.category == "History") & (activeStep == 5)) {
        return [
          <Grid
            key={categoryData.category}
            container
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              mt: 1,
              mb: 1,
            }}
          >
            {Object.keys(categoryData.data).map((fieldGroup) => (
              <Grid
                key={fieldGroup}
                item
                md={12}
                sx={{
                  backgroundColor: "white",
                  maxHeight: "max-content",
                  height: "max-content",
                  borderRadius: "8px",
                  border: "1px solid #E0E0E0",
                  mt: 0.25,
                  boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                  ...container_Padding,
                }}
              >
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    margin: "0px !important",
                  }}
                >
                  {fieldGroup}
                </Typography>
                <Box sx={{ width: "100%" }}>
                  <CardContent
                    sx={{
                      padding: "0",
                      paddingBottom: "0 !important",
                      paddingTop: "10px !important",
                    }}
                  >
                    <Grid
                      container
                      style={{
                        display: "grid",
                        gridTemplateColumns: "repeat(6,1fr)",
                        gap: "15px",
                      }}
                      justifyContent="space-between"
                      alignItems="flex-start"
                      md={12}
                    >
                      {categoryData.data[fieldGroup].map((field) => {
                        return (
                          <EditableFieldForProfitCenter
                            label={field.fieldName}
                            value={field.value}
                            length={field.maxLength}
                            data={editedData}
                            visibility={field.visibility}
                            onSave={(newValue) =>
                              handleFieldSave(field.fieldName, newValue)
                            }
                            isEditMode={isEditMode}
                            type={field.fieldType}
                            field={field}
                            taskRequestId={profitCenterRowData?.requestId}
                          />
                        );
                      })}
                    </Grid>
                  </CardContent>
                </Box>
              </Grid>
            ))}
          </Grid>,
        ];
      } else if (categoryData?.category == "Attachments" && activeStep == 6) {
        return [
          <>
            {!isEditMode ? (
              <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                <Grid
                  container
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                  }}
                >
                  <Typography variant="h6">
                    <strong>Attachments</strong>
                  </Typography>
                </Grid>
                {Boolean(attachments.length) && (
                  <ReusableTable
                    width="100%"
                    rows={attachments}
                    columns={attachmentColumns}
                    hideFooter={false}
                    getRowIdValue={"id"}
                    disableSelectionOnClick={true}
                    stopPropagation_Column={"action"}
                  />
                )}
                {!Boolean(attachments.length) && (
                  <Typography variant="body2">No Attachments Found</Typography>
                )}
                <br />
                <Typography variant="h6">Comments</Typography>
                {Boolean(comments.length) && (
                  <Timeline
                    sx={{
                      [`& .${timelineItemClasses.root}:before`]: {
                        flex: 0,
                        padding: 0,
                      },
                    }}
                  >
                    {comments.map((comment) => (
                      <TimelineItem>
                        <TimelineSeparator>
                          <TimelineDot>
                            <CheckCircleOutlineOutlined
                              sx={{ color: "#757575" }}
                            />
                          </TimelineDot>
                          <TimelineConnector />
                        </TimelineSeparator>
                        <TimelineContent sx={{ py: "12px", px: 2 }}>
                          <Card
                            elevation={0}
                            sx={{
                              border: 1,
                              borderColor: "#C4C4C4",
                              borderRadius: "8px",
                              width: "650px",
                            }}
                          >
                            <Box sx={{ padding: "1rem" }}>
                              <Stack spacing={1}>
                                <Grid
                                  sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                  }}
                                >
                                  <Typography
                                    sx={{
                                      textAlign: "right",
                                      color: " #757575",
                                      fontWeight: "500",
                                      fontSize: "12px",
                                    }}
                                  >
                                    {moment(comment.createdAt).format(
                                      "DD MMM YYYY"
                                    )}
                                  </Typography>
                                </Grid>

                                <Typography
                                  sx={{
                                    fontSize: "12px",

                                    color: " #757575",
                                    fontWeight: "500",
                                  }}
                                >
                                  {comment.user}
                                </Typography>
                                <Typography
                                  sx={{
                                    fontSize: "12px",
                                    color: "#1D1D1D",
                                    fontWeight: "600",
                                    wordWrap: "break-word",
                                  }}
                                >
                                  {comment.comment}
                                </Typography>
                              </Stack>
                            </Box>
                          </Card>
                        </TimelineContent>
                      </TimelineItem>
                    ))}
                  </Timeline>
                )}
                {!Boolean(comments.length) && (
                  <Typography variant="body2">No Comments Found</Typography>
                )}
                <br />
              </Card>
            ) : (
              <>
                <ReusableAttachementAndComments
                  title="ProfitCenter"
                  useMetaData={false}
                  artifactId={pcNumber}
                  artifactName="ProfitCenter"
                />
                <Card sx={{ padding: "1rem 1rem 0rem 1rem" }}>
                  <Grid
                    container
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                    }}
                  >
                    <Typography variant="h6">
                      <strong>Attachments</strong>
                    </Typography>
                  </Grid>
                  {Boolean(attachments.length) && (
                    <ReusableTable
                      width="100%"
                      rows={attachments}
                      columns={attachmentColumns}
                      hideFooter={false}
                      getRowIdValue={"id"}
                      disableSelectionOnClick={true}
                      stopPropagation_Column={"action"}
                    />
                  )}
                  {!Boolean(attachments.length) && (
                    <Typography variant="body2">
                      No Attachments Found
                    </Typography>
                  )}
                  <br />
                  <Typography variant="h6">Comments</Typography>
                  {Boolean(comments.length) && (
                    <Timeline
                      sx={{
                        [`& .${timelineItemClasses.root}:before`]: {
                          flex: 0,
                          padding: 0,
                        },
                      }}
                    >
                      {comments.map((comment) => (
                        <TimelineItem>
                          <TimelineSeparator>
                            <TimelineDot>
                              <CheckCircleOutlineOutlined
                                sx={{ color: "#757575" }}
                              />
                            </TimelineDot>
                            <TimelineConnector />
                          </TimelineSeparator>
                          <TimelineContent sx={{ py: "12px", px: 2 }}>
                            <Card
                              elevation={0}
                              sx={{
                                border: 1,
                                borderColor: "#C4C4C4",
                                borderRadius: "8px",
                                width: "650px",
                              }}
                            >
                              <Box sx={{ padding: "1rem" }}>
                                <Stack spacing={1}>
                                  <Grid
                                    sx={{
                                      display: "flex",
                                      justifyContent: "space-between",
                                    }}
                                  >
                                    <Typography
                                      sx={{
                                        textAlign: "right",
                                        color: " #757575",
                                        fontWeight: "500",
                                        fontSize: "12px",
                                      }}
                                    >
                                      {moment(comment.createdAt).format(
                                        "DD MMM YYYY"
                                      )}
                                    </Typography>
                                  </Grid>

                                  <Typography
                                    sx={{
                                      fontSize: "12px",

                                      color: " #757575",
                                      fontWeight: "500",
                                    }}
                                  >
                                    {comment.user}
                                  </Typography>
                                  <Typography
                                    sx={{
                                      fontSize: "12px",
                                      color: "#1D1D1D",
                                      fontWeight: "600",
                                      wordWrap: "break-word",
                                    }}
                                  >
                                    {comment.comment}
                                  </Typography>
                                </Stack>
                              </Box>
                            </Card>
                          </TimelineContent>
                        </TimelineItem>
                      ))}
                    </Timeline>
                  )}
                  {!Boolean(comments.length) && (
                    <Typography variant="body2">No Comments Found</Typography>
                  )}
                  <br />
                </Card>
              </>
            )}
          </>,
        ];
      }
    });

  const handleWarningDialogClose = () => {
    setOpenMessageDialog(false);
  };

  const formatFieldName = (fieldName) => {
    return fieldName
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .replace(/([A-Z])([A-Z][a-z])/g, '$1 $2');
  };
  

  return (
    <>
      {/* {isLoading === true ? (
        <LoadingComponent />
      ) : ( */}
        <div style={{ backgroundColor: "#FAFCFF" }}>
          <ReusableDialog
            dialogState={openMessageDialog}
            openReusableDialog={handleMessageDialogClickOpen}
            closeReusableDialog={handleMessageDialogClose}
            dialogTitle={messageDialogTitle}
            dialogMessage={messageDialogMessage}
            handleDialogConfirm={handleMessageDialogClose}
            dialogOkText={"OK"}
            //handleExtraButton={handleMessageDialogNavigate}
            showCancelButton={true}
            showExtraButton={handleExtrabutton}
            dialogSeverity={messageDialogSeverity}
            handleDialogReject={handleWarningDialogClose}
            handleExtraText={handleExtraText}
            handleExtraButton={handleProceedbutton}
          />

          {successMsg && (
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={messageDialogMessage}
              handleSnackBarClose={handleSnackBarClose}
            />
          )}

          {formValidationErrorItems.length != 0 && (
            <ReusableSnackBar
              openSnackBar={openSnackbarValidation}
              alertMsg={
                "Please enter the following Field: " +
                formValidationErrorItems.map(formatFieldName).join(", ")
              }
              handleSnackBarClose={handleSnackBarCloseValidation}
            />
          )}

          <Dialog
            open={openCorrectionDialog}
            onClose={handleCorrectionDialogClose}
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                // borderBottom: "1px solid grey",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleCorrectionDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"Enter Remarks for Correction"}
                      inputProps={{ maxLength: 200 }}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleCorrectionDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onProfitCenterCorrection}
                variant="contained"
              >
                OK
              </Button>
            </DialogActions>
          </Dialog>

          <Dialog
            hideBackdrop={false}
            elevation={2}
            PaperProps={{
              sx: { boxShadow: "none" },
            }}
            open={openRemarkDialog}
            onClose={handleRemarksDialogClose}
          >
            <DialogTitle
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
                height: "max-content",
                padding: ".5rem",
                paddingLeft: "1rem",
                backgroundColor: "#EAE9FF40",
                display: "flex",
              }}
            >
              <Typography variant="h6">Remarks</Typography>

              <IconButton
                sx={{ width: "max-content" }}
                onClick={handleRemarksDialogClose}
                children={<CloseIcon />}
              />
            </DialogTitle>
            <DialogContent sx={{ padding: ".5rem 1rem" }}>
              <Stack>
                <Box sx={{ minWidth: 400 }}>
                  <FormControl sx={{ height: "auto" }} fullWidth>
                    <TextField
                      sx={{ backgroundColor: "#F5F5F5" }}
                      value={remarks}
                      onChange={handleRemarks}
                      multiline
                      placeholder={"Enter Remarks"}
                      inputProps={{ maxLength: 200 }}
                    ></TextField>
                  </FormControl>
                </Box>
              </Stack>
            </DialogContent>
            <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleRemarksDialogClose}
              >
                Cancel
              </Button>
              <Button
                className="button_primary--normal"
                type="save"
                onClick={onProfitCenterSubmitRemarks}
                variant="contained"
              >
                Submit
              </Button>
            </DialogActions>
          </Dialog>

          <Backdrop
            sx={{ color: "#fff", zIndex: (theme) => theme.zIndex.drawer + 1 }}
            open={blurLoading}
            // onClick={handleClose}
          >
            <CircularProgress color="inherit" />
          </Backdrop>

          <Grid container sx={outermostContainer_Information}>
            <Grid item md={12} style={{ padding: "16px", display: "flex" }}>
              <Grid md={12} sx={{ display: "flex" }}>
                <Grid>
                  <IconButton
                    color="primary"
                    aria-label="upload picture"
                    component="label"
                    sx={iconButton_SpacingSmall}
                  >
                    <ArrowCircleLeftOutlinedIcon
                      sx={{
                        fontSize: "25px",
                        color: "#000000",
                      }}
                      onClick={() => {
                        dispatch(clearTaskData())
                        dispatch(clearProfitCenterPayloadGI())
                        dispatch(clearCostCenterPayload())
                        dispatch(clearSingleGLPayloadGI())
                        dispatch(clearProfitCenterPayload())
                        dispatch(clearCostCenter())
                        navigate(-1);
                      }}
                    />
                  </IconButton>
                </Grid>

                <Grid>
                  {!profitCenterRowData?.requestType && isEditMode ? (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Create ET Profit Center</strong>
                      </Typography>
                      <Typography variant="body2" color="#777">
                      This view creates a new ET Profit Center 
                      </Typography>
                    </Grid>
                  ) : (
                    ""
                  )}

                  {isEditMode &&
                  profitCenterRowData?.requestType === "Change" ? (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Change Profit Center </strong>
                      </Typography>
                      <Typography variant="body2" color="#777">
                        This view changes the details of the Profit Center
                      </Typography>
                    </Grid>
                  ) : (
                    ""
                  )}

                  {isEditMode &&
                  profitCenterRowData?.requestType === "Create" ? (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Create Profit Center </strong>
                      </Typography>
                      <Typography variant="body2" color="#777">
                        This view creates a new Profit Center
                      </Typography>
                    </Grid>
                  ) : (
                    ""
                  )}

                  {isDisplayMode ? (
                    <Grid item md={12}>
                      <Typography variant="h3">
                        <strong>Display Profit Center </strong>
                      </Typography>

                      <Typography variant="body2" color="#777">
                        This view displays the details of Profit Center
                      </Typography>
                    </Grid>
                  ) : (
                    ""
                  )}
                </Grid>
              </Grid>
              <Grid
                md={3}
                sx={{ display: "flex", justifyContent: "flex-end" }}
                gap={2}
              >
                {profitCenterRowData?.requestId ||
                taskRowDetails?.requestType ? (
                  <Grid>
                    <Button
                      variant="outlined"
                      size="small"
                      sx={button_Outlined}
                      onClick={openChangeLog}
                      title="Change Log"
                    >
                      <TrackChangesTwoToneIcon
                        sx={{ padding: "2px" }}
                        fontSize="small"
                      />
                    </Button>
                  </Grid>
                ) : (
                  ""
                )}

                {isChangeLogopen && (
                  <ChangeLog
                    open={true}
                    closeModal={handleClosemodalData}
                    requestId={
                      profitCenterRowData?.requestId
                        ? profitCenterRowData?.requestId
                        : taskRowDetails?.requestId
                    }
                    requestType={
                      profitCenterRowData?.requestType
                        ? profitCenterRowData?.requestType
                        : taskData?.body?.requestType
                    }
                    pageName={"profitCenter"}
                    controllingArea={
                      profitCenterRowData?.controllingArea
                        ? profitCenterRowData?.controllingArea
                        : taskData?.body?.controllingArea
                    }
                    centerName={
                      profitCenterRowData?.profitCenter
                        ? profitCenterRowData?.profitCenter
                        : taskData?.body?.profitCenter
                    }
                  />
                )}
                {/* <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button> */}
                {checkIwaAccess(iwaAccessData, "Profit Center", "ChangePC") &&
                  (userData?.role === "Super User" && //when request type exist as create and change
                  profitCenterRowData?.requestType &&
                  taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                  isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        {/* <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </> */}
                      </Grid>
                    </Grid>
                  ) : userData?.role === "Finance" && //when request type exist as create and change
                    (profitCenterRowData?.requestType ||
                      taskRowDetails?.requestType) &&
                    taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                    isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Fill Details
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </>
                      </Grid>
                    </Grid>
                  ) : userData?.role === "Super User" &&
                    !profitCenterRowData?.requestType &&
                    taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" && // when change from master data screen
                    isDisplayMode ? (
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        {/* <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </> */}
                      </Grid>
                    </Grid>
                  ) : userData?.role === "Finance" &&
                    !profitCenterRowData?.requestType &&
                    taskRowDetails?.itmStatus?.toUpperCase() !== "OPEN" &&
                    isDisplayMode ? ( // when change from master data screen
                    <Grid gap={1} sx={{ display: "flex" }}>
                      <Grid
                        gap={1}
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                        }}
                      >
                        {/* <>
                          <Grid item>
                            <Button
                              variant="outlined"
                              size="small"
                              sx={button_Outlined}
                              onClick={onEdit}
                            >
                              Change
                              <EditOutlinedIcon
                                sx={{ padding: "2px" }}
                                fontSize="small"
                              />
                            </Button>
                          </Grid>
                        </> */}
                      </Grid>
                    </Grid>
                  ) : (
                    ""
                  ))}
              </Grid>
            </Grid>
            <Grid
              container
              display="flex"
              flexDirection="row"
              flexWrap="nowrap"
            >
              <Box width="70%" sx={{ marginLeft: "40px" }}>
                <Grid item sx={{ paddingTop: "2px !important" }}>
                  <Stack flexDirection="row">
                    <div style={{ width: "10%" }}>
                      <Typography variant="body2" color="#777">
                        Profit Center
                      </Typography>
                    </div>
                    <Typography
                      variant="body2"
                      fontWeight="bold"
                      justifyContent="flex-start"
                    >
                      :{" "}
                      {profitCenterRowData?.profitCenter
                        ? profitCenterRowData?.profitCenter
                        : pcNumberFromCC
                        ? pcNumberFromCC
                        : taskData?.body?.profitCenter}
                     
                    </Typography>
                  </Stack>
                </Grid>
                {/* <Grid item sx={{ paddingTop: "2px !important" }}>
              <Stack flexDirection="row">
                <div style={{ width: "10%" }}>
                  <Typography variant="body2" color="#777">
                    Profit Center Name
                  </Typography>
                </div>
                <Typography variant="body2" fontWeight="bold">
                  :{" "}
                  {profitCenterRowData?.profitCenterName
                    ? profitCenterRowData?.profitCenterName
                    : taskData?.body?.profitCenterName}
                </Typography>
                <Typography variant="body2" fontWeight="bold"></Typography>
              </Stack>
            </Grid> */}

                <Grid item sx={{ paddingTop: "2px !important" }}>
                  <Stack flexDirection="row">
                    <div style={{ width: "10%" }}>
                      <Typography variant="body2" color="#777">
                        Controlling Area
                      </Typography>
                    </div>
                    <Typography variant="body2" fontWeight="bold">
                      :{" "}
                      {profitCenterRowData?.controllingArea
                        ? profitCenterRowData?.controllingArea
                        : taskData?.body?.controllingArea}
                    </Typography>
                  </Stack>
                </Grid>
              </Box>
            </Grid>

            <Grid container style={{ marginLeft: 25 }}>
              <Stepper
                activeStep={activeStep}
                sx={{
                  background: "#FFFFFF",
                  borderBottom: "1px solid #BDBDBD",
                  width: "100%",
                  height: "48px",
                }}
                aria-label="mui tabs example"
              >
                {filteredFactorsArray.map((factor, index) => (
                  <Step key={factor}>
                    <StepLabel sx={{ fontWeight: "700" }}>{factor}</StepLabel>
                  </Step>
                ))}
              </Stepper>

              {/* Display the cards of the currently active Step */}

              {tabContents &&
                tabContents[activeStep]?.map((cardContent, index) => (
                  <Box key={index} sx={{ mb: 2, width: "100%" }}>
                    <Typography variant="body2">{cardContent}</Typography>
                  </Box>
                ))}
            </Grid>
          </Grid>

          <Grid
            gap={1}
            sx={{ display: "flex", justifyContent: "space-between" }}
          >
            {/* {checkIwaAccess(iwaAccessData, "Profit Center", "ChangePC") && */}
              {/* (!profitCenterRowData?.requestType && !isEditMode ? ( */}
                <Paper
                  sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
                  elevation={2}
                >
                  <BottomNavigation
                    className="container_BottomNav"
                    showLabels
                    sx={{ display: "flex", justifyContent: "flex-end" }}
                  >
                    {isRolePresent ? (
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ ...button_Primary, mr: 1 }}
                        onClick={onRevealClick}
                        // disabled={activeStep === 0}
                      >
                        Reveal
                      </Button>
                    ) : (
                      ""
                    )}
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleBack}
                      disabled={activeStep === 0}
                    >
                      Back
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ ...button_Primary, mr: 1 }}
                      onClick={handleNext}
                      disabled={
                        activeStep === filteredFactorsArray.length - 1 ? true : false
                      }
                    >
                      Next
                    </Button>
                  </BottomNavigation>
                </Paper>
          
              {/* } */}

          </Grid>
        </div>
      {/* )} */}
    </>
  );
};

export default DisplayProfitCenter;
