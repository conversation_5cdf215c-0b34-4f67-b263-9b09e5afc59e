import React, { useState, useEffect, forwardRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  Select,
  Tooltip,
  MenuItem,
  FormControl,
  Slide,
  tooltipClasses,
  InputLabel,
  Typography,
  IconButton,
  Box,
  Paper,
  Checkbox,
  ListItemText,
  TextField,
  RadioGroup,
  FormControlLabel,
  Radio,
} from "@mui/material";
import { destination_ProfitCenter_Mass } from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import { useLocation, useNavigate } from "react-router-dom";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";

import DownloadDialog from "../../components/Common/DownloadDialog";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";

import {
  API_CODE,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  LOADER_MESSAGES,
  MODULE_MAP,
} from "@constant/enum";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { REQUEST_TYPE } from "@constant/enum";
import {
  transformProfitCenterResponseChange,
  showToast,
} from "../../functions";
import FilterChangeDropdown from "../../components/Common/ui/dropdown/FilterChangeDropdown";
import { APP_END_POINTS } from "@constant/appEndPoints";
import {
  setFetchedProfitCenterDataPc,
  setOriginalProfitCenterDataPc,
  setFetchReqBenchDataPc,
  setOriginalReqBenchDataPc,
  updateProfitCenterRowPc,
  setShowGrid,
  updateReqBenchRowPc,
  setChangedFieldsMapPc,
  resetProfitCenterStatePc,
} from "@app/profitCenterTabsSlice";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import ReusableDataTable from "../../components/Common/ReusableTable";

import BottomNavGlobal from "../../components/RequestBench/RequestPages/BottomNavGlobal";
import useLang from "@hooks/useLang";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import useProfitCenterChangeFieldConfig from "@hooks/useProfitCenterChangeFieldConfig";
import { getFieldsForTemplate } from "@helper/fieldHelper";
import { colors } from "@constant/colors";
import ObjectLockDialog from "@components/Common/ObjectLockDialog";
import { changePayloadForPC } from "../../functions";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const RequestDetailsChangePC = ({
  reqBench,
  requestId,
  apiResponses,
  downloadClicked,
  setDownloadClicked,
  module,
  template,
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { t } = useLang();
  const { fetchedProfitCenterData, fetchReqBenchData, changedFieldsMap } =
    useSelector((state) => state.profitCenter);
  const showGrid = useSelector((state) => state.profitCenter.showGrid);

  const requestHeaderData = useSelector(
    (state) => state.profitCenter.payload.requestHeaderData
  );

  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );

  

  const { getChangeTemplate } =
    useProfitCenterChangeFieldConfig("Profit Center");

  const initialPayload = useSelector((state) => state.request.requestHeader);

  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const task = useSelector((state) => state?.userManagement.taskData);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");

  const [open, setOpen] = useState(true);
  const [dropdown1Value, setDropdown1Value] = useState("");
  const [dropdown2Value, setDropdown2Value] = useState("");
  const [selectedRow, setSelectedRow] = useState(null);
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [selectedCompanyCodes, setSelectedCompanyCodes] = useState([]);
  const [profitCenterOptions, setProfitCenterOptions] = useState([]);
  const [selectedProfitCenters, setSelectedProfitCenters] = useState([]);
  const [successMsg, setSuccessMsg] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [dropdownDataCountry, setDropdownDataCountry] = useState([]);
  const [regionOptionsMap, setRegionOptionsMap] = useState({});
  const [selectedCountry, setSelectedCountry] = useState("");
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  const [dropdownAamnum, setDropdwnAamnum] = useState([]);
  const [dropdownBusinessSegment, setDropdownBusinessSegment] = useState([]);
  const [profitcenterResponse, setProfitcenterResponse] = useState([]);
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [isLoading, setIsLoading] = useState(false);
  const [isValidated, setIsValidated] = useState(false);
  const [duplicateFieldsData, setDuplicateFieldsData] = useState([]);
  const [showTableInDialog, setShowTableInDialog] = useState(false);

  const { getButtonsDisplayGlobal } = useButtonDTConfig();

  const changeFieldDataRaw = useSelector(
    (state) => state?.payload?.changeFieldSelectiondata
  );
  const changeFieldData = Array.isArray(changeFieldDataRaw)
    ? changeFieldDataRaw
    : [];

  const fieldNameListRaw = requestHeaderData?.FieldName;
  const reqBenchFieldName = apiResponses?.[0]?.Torequestheaderdata?.FieldName;

  const rawFieldName = reqBench ? reqBenchFieldName : fieldNameListRaw;
  const fieldNameList = Array.isArray(rawFieldName)
    ? rawFieldName.map((f) => f.trim())
    : typeof rawFieldName === "string"
    ? rawFieldName.split(",").map((f) => f.trim())
    : [];

  const { allFields, mandatoryFields, headerFields, fieldMetaMap } =
    getFieldsForTemplate(changeFieldData, fieldNameList);

  useEffect(() => {
    if (task?.ATTRIBUTE_1 || RequestId) {
      getButtonsDisplayGlobal("Profit Center", "MDG_DYN_BTN_DT", "v3");
    }
  }, [task]);

  // Function to handle opening and closing the popup
  const handleClose = () => {
    setOpen(false);
    setDownloadClicked(false);
    navigate("/requestbench");
  };
  const getObjectLock = (params) => {
    if (!selectedProfitCenters.length || !dropdown1Value) return;
    const objectLockPayload = selectedProfitCenters?.map((pc) => ({
      controllingArea: dropdown1Value,
      profitCenter: pc,
      changedFieldsToCheck: initialPayload?.fieldName,
    }));

    const successHandler = (response) => {
      const hasError = response?.some((item) => item?.statusCode !== 200);
      if (!hasError) {
        if (params === "OK") {
          setOpen(false);
          dispatch(setShowGrid(true));
          fetchProfitCenterDetails();
        } else if (params === "Download") {
          handleDownloadDialogOpen();
        }
      } else {
        const filteredData = response.filter((item) => item.statusCode === 400);
        let duplicateFieldsArr = [];
        filteredData?.forEach((item, index) => {
          const dataHash = {
            id: `${item?.body?.profitCenter}_${index}`, // ✅ UNIQUE ID
            objectNo: item?.body?.profitCenter,
            reqId: item?.body?.matchingRequests
              ?.map((req) => req?.matchingRequestHeaderId)
              ?.filter(Boolean),
            childReqId: item?.body?.matchingRequests
              ?.map((req) => req?.matchingChildHeaderIdsSet)
              ?.filter(Boolean),
            requestedBy: item?.body?.matchingRequests
              ?.map((req) => req?.RequestCreatedBy)
              ?.filter(Boolean),
          };

          duplicateFieldsArr.push(dataHash);
        });
        setDuplicateFieldsData(duplicateFieldsArr);
        setShowTableInDialog(true);
      }
    };

    const errorHandler = (err) => {
      console.error("Failed to fetch profit center details", err);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/alter/checkDuplicatePCRequest`,
      "post",
      successHandler,
      errorHandler,
      objectLockPayload
    );
  };

  const handleOk = (params) => {
    getObjectLock(params);
  };
  const allColumns = [
   {
  field: "included",
  // headerName: "Included",
  width: 120,
  align: "center",
  headerAlign: "center",
  renderHeader: () => {
    const data = reqBench ? fetchReqBenchData : fetchedProfitCenterData;
    const allSelected = data.every((row) => row.included);
    const someSelected = data.some((row) => row.included);

    return (
      <Checkbox
        checked={allSelected}
        indeterminate={!allSelected && someSelected}
        onChange={(e) => {
          const updatedData = data.map((row) => ({
            ...row,
            included: e.target.checked,
          }));
          if (reqBench) {
            dispatch(setFetchReqBenchDataPc(updatedData)); // adjust to your actual setter
          } else {
            dispatch(setFetchedProfitCenterDataPc(updatedData)); // adjust to your actual setter
          }
        }}
      />
    );
  },
  renderCell: (params) => {
    const data = reqBench ? fetchReqBenchData : fetchedProfitCenterData;
    const rowIndex = data.findIndex((row) => row.id === params.row.id);

    return (
      <Checkbox
        checked={data[rowIndex]?.included || false}
        onChange={(e) => {
          const updatedData = [...data];
          updatedData[rowIndex] = {
            ...updatedData[rowIndex],
            included: e.target.checked,
          };
          if (reqBench) {
            dispatch(setFetchReqBenchDataPc(updatedData));
          } else {
            dispatch(setFetchedProfitCenterDataPc(updatedData));
          }
        }}
      />
    );
  },
},

    {
      field: "lineNumber",
      headerName: "Sl No",
      flex: 0.2,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = (
          reqBench ? fetchReqBenchData : fetchedProfitCenterData
        ).findIndex((row) => row.id === params.row.id);

        return <div>{rowIndex + 1}</div>;
      },
    },

    {
      field: "controllingArea",
      headerName: "Controlling Area",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "companyCode",
      headerName: "Company Codes",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "profitCenter",
      headerName: "Profit Center",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },

    {
      field: "profitCenterName",
      headerName: "Short Description",
      width: 250,
      editable: true,
    },
    {
      field: "description",
      headerName: "Long Description",
      width: 300,
      editable: true,
    },
    {
      field: "segment",
      headerName: "Segment",
      width: 150,
      editable: false,
      renderCell: (params) => {
        const field = params.field;

        const options = dropdownDataSegment || [];

        // Try to find the current value in dropdown options
        const selectedValue =
          options.find(
            (item) =>
              item.code === params.row[field] || item.desc === params.row[field]
          ) || null;

        return (
          <SingleSelectDropdown
            options={options}
            value={selectedValue}
            onChange={(selectedOption) => {
              const newValue = selectedOption?.code || "";

              const updatedRow = {
                ...params.row,
                [field]: newValue,
              };

              // dispatch(updateProfitCenterRowPc(updatedRow));

              if (reqBench) {
                dispatch(updateReqBenchRowPc(updatedRow));
              } else {
                dispatch(updateProfitCenterRowPc(updatedRow));
              }
            }}
            placeholder={`Select ${params.colDef.headerName}`}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },

    // {
    //   field: "pcaamnum",
    //   headerName: "PC AAM Number",
    //   width: 150,
    //   editable: true,
    // },
    {
      field: "businessSegment",
      headerName: "Business Segment",
      width: 200,
      editable: true,
      renderCell: (params) => {
        const field = params.field;
        const options = dropdownBusinessSegment || [];
        const selectedValue =
          options.find(
            (item) =>
              item.code === params.row[field] || item.desc === params.row[field]
          ) || null;

        return (
          <SingleSelectDropdown
            options={options}
            value={selectedValue}
            onChange={(selectedOption) => {
              const newValue = selectedOption?.code || "";

              const updatedRow = {
                ...params.row,
                [field]: newValue,
              };

              // dispatch(updateProfitCenterRowPc(updatedRow));
              if (reqBench) {
                dispatch(updateReqBenchRowPc(updatedRow));
              } else {
                dispatch(updateProfitCenterRowPc(updatedRow));
              }
            }}
            placeholder={`Select ${params.colDef.headerName}`}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "userResponsible",
      headerName: "PC User Responsible",
      width: 250,
      editable: true,
    },
    {
      field: "personResponsible",
      headerName: "PC Person Responsible",
      width: 250,
      editable: true,
    },
    {
      field: "lockIndicator",
      headerName: "Lock Indicator",
      width: 250,
      editable: false, // disable row editing from DataGrid
      renderCell: (params) => {
        const value =
          params.row.lockIndicator === true || params.row.lockIndicator === "X";
        const label = value ? "Block" : "Unblock";

        return (
          <RadioGroup row value={value ? "true" : "false"}>
            <FormControlLabel
              value={value ? "true" : "false"}
              control={<Radio disabled />}
              label={label}
            />
          </RadioGroup>
        );
      },
    },

    { field: "createdBy", headerName: "Created By", width: 150 },
    {
      field: "validFrom",
      headerName: "Valid From",
      width: 150,
    },
    { field: "validTo", headerName: "Valid To", width: 150 },
    { field: "city", headerName: "City", width: 150, editable: true },

    {
      field: "country",
      headerName: "Country/Reg.",
      width: 150,
      editable: true,
      renderCell: (params) => {
        const options = dropdownDataCountry || [];
        const selectedValue =
          options.find(
            (item) =>
              item.code === params.row.country ||
              item.desc === params.row.country
          ) || null;

        return (
          <SingleSelectDropdown
            options={options}
            value={selectedValue}
            onChange={(selectedOption) => {
              const newValue = selectedOption?.code || "";

              const updatedRow = {
                ...params.row,
                country: newValue,
                region: "", // Clear region on country change
              };

              getRegionBasedOnCountry(newValue);

              if (reqBench) {
                dispatch(updateReqBenchRowPc(updatedRow));
              } else {
                dispatch(updateProfitCenterRowPc(updatedRow));
              }
            }}
            placeholder="Select Country"
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "region",
      headerName: "Region",
      width: 150,
      editable: true,
      renderCell: (params) => {
        const selectedCountry = params.row.country;
        const options = regionOptionsMap[selectedCountry] || [];

        const selectedValue =
          options.find((item) => item.code === params.row.region) || null;

        return (
          <SingleSelectDropdown
            options={options}
            value={selectedValue}
            onChange={(selectedOption) => {
              const newValue = selectedOption?.code || "";

              const updatedRow = {
                ...params.row,
                region: newValue,
              };

              if (reqBench) {
                dispatch(updateReqBenchRowPc(updatedRow));
              } else {
                dispatch(updateProfitCenterRowPc(updatedRow));
              }
            }}
            placeholder="Select Region"
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },

    { field: "street", headerName: "Street", width: 150, editable: true },
    { field: "pocode", headerName: "Postal Code", width: 150, editable: true },
    // { field: "region", headerName: "Region", width: 150, editable: true },
    { field: "name1", headerName: "Name 1", width: 150, editable: true },
    { field: "name2", headerName: "Name 2", width: 150, editable: true },
    { field: "name3", headerName: "Name 3", width: 150, editable: true },
    { field: "name4", headerName: "Name 4", width: 150, editable: true },
  ];

  const fixedColumns = allColumns.slice(0, 2); // Included & Sl No
  const businessSegmentColumn = {
    field: "businessSegment",
    headerName: "Business Segment",
    width: 200,
    editable: true,
    renderCell: (params) => {
      const field = params.field;
      const options = dropdownBusinessSegment || [];

      const selectedValue =
        options.find(
          (item) =>
            item.code === params.row[field] || item.desc === params.row[field]
        ) || null;

      return (
        <SingleSelectDropdown
          options={options}
          value={selectedValue}
          onChange={(selectedOption) => {
            const newValue = selectedOption?.code || "";
            const updatedRow = {
              ...params.row,
              [field]: newValue,
            };
            dispatch(
              reqBench
                ? updateReqBenchRowPc(updatedRow)
                : updateProfitCenterRowPc(updatedRow)
            );
          }}
          placeholder="Select Business Segment"
          minWidth="90%"
          listWidth={235}
        />
      );
    },
    renderHeader: () => (
      <span>
        Business Segment{" "}
        {mandatoryFields.includes("Business Segment") && (
          <span style={{ color: "red" }}>*</span>
        )}
      </span>
    ),
  };

  const dynamicColumns = allColumns
    .slice(2)
    .filter((col) => allFields.includes(col.headerName?.trim()))
    .map((col) => {
      const trimmedHeader = col.headerName?.trim();
      const isMandatory = mandatoryFields.includes(trimmedHeader);

      const isEditableField = [
        "description",
        "profitCenterName",
        "personResponsible",
        "city",
        "street",
        "pocode",
        "name1",
        "name2",
        "name3",
        "name4",
      ].includes(col.field);

      const normalizedField = col.field?.toLowerCase();

      const fieldMeta = fieldMetaMap[normalizedField] || {};
      const maxLength = fieldMeta.maxLength;

      return {
        ...col,
        headerName: trimmedHeader,
        editable: true,
        renderHeader: () =>
          isMandatory ? (
            <span>
              {trimmedHeader} <span style={{ color: "red" }}>*</span>
            </span>
          ) : (
            trimmedHeader
          ),
        ...(isEditableField && {
          renderCell: (params) => (
            <TextField
              value={params.value || ""}
              onChange={(e) =>
                params.api.setEditCellValue({
                  id: params.id,
                  field: params.field,
                  value: e.target.value.toUpperCase(),
                })
              }
              variant="outlined"
              size="small"
              fullWidth
            />
          ),
          renderEditCell: (params) => {
            const currentLength = (params.value || "").length;

            return (
              <TextField
                value={params.value || ""}
                onChange={(e) =>
                  params.api.setEditCellValue({
                    id: params.id,
                    field: params.field,
                    value: e.target.value.toUpperCase(),
                  })
                }
                variant="outlined"
                size="small"
                fullWidth
                placeholder={`Enter ${trimmedHeader}`}
                inputProps={{ maxLength }}
                // helperText={`${currentLength}/${maxLength}`}
              />
            );
          },
        }),
      };
    });

  // Final columns array
  const columns = [
    ...fixedColumns,
    ...dynamicColumns.filter((col) => col.field !== "businessSegment"), // Avoid duplicates
    businessSegmentColumn, // Force-inject the working custom column
  ];

  const processRowUpdate = (newRow) => {
    dispatch(updateProfitCenterRowPc(newRow));

    return newRow;
  };

  const processRowUpdateReqBench = (newRow) => {
    dispatch(updateReqBenchRowPc(newRow));

    return newRow;
  };

  const handleRowClick = (params) => {
    setSelectedRow(params.row);
  };

  const handleCreateNew = () => {
    setDropdown1Value("");
    setSelectedCompanyCodes([]);
    setSelectedProfitCenters([]);

    // Then open the dialog
    setOpen(true);
  };

  useEffect(() => {
    if (dropdown1Value && selectedCompanyCodes.length > 0) {
      fetchProfitCenters();
    }
  }, [dropdown1Value, selectedCompanyCodes]);

  useEffect(() => {
    getCompanyCode();
  }, []);

  const getCompanyCode = () => {
    const hSuccess = (data) => {
      setDropdownDataCompany(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "CompanyCode", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompCodeBasedOnControllingArea?controllingArea=ETCA&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };

  const getSegment = () => {
    const hSuccess = (data) => {
      setDropdownDataSegment(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Segment", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSegment`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBusinessSegment = () => {
    const hSuccess = (data) => {
      const formatted = (data.body || []).map((item) => ({
        code: item,
        desc: item,
      }));

      setDropdownBusinessSegment(formatted);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "businessSegment", data: formatted },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getBusinessSegment`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCountryData = () => {
    const hSuccess = (data) => {
      setDropdownDataCountry(data.body || []);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Country", data: data.body || [] },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCountryOrReg`,
      "get",
      hSuccess,
      hError
    );
  };

  const getRegionBasedOnCountry = (countryCode) => {
    const hSuccess = (data) => {
      setRegionOptionsMap((prev) => ({
        ...prev,
        [countryCode]: data.body || [],
      }));

      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: `Region_${countryCode}`, data: data.body || [] },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getRegionBasedOnCountry?country=${countryCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getCountryData();
  }, []);
  useEffect(() => {}, [dropdownDataCountry]);

  useEffect(() => {
    getRegionBasedOnCountry(selectedCountry.code);
  }, []);

  useEffect(() => {
    getSegment();
    getBusinessSegment();
  }, []);

  useEffect(() => {
    if (selectedCompanyCodes.length === 0) {
      setProfitCenterOptions([]);
      setSelectedProfitCenters([]);
    } else {
      fetchProfitCenters(selectedCompanyCodes);
    }
  }, [selectedCompanyCodes]);

  const fetchProfitCenters = (companyCodes = []) => {
    setProfitCenterOptions([]); // clear before fetching

    const controllingArea = `${dropdown1Value}`;

    companyCodes.forEach((companyCode) => {
      const payload = {
        controllingArea,
        companyCode,
        top: "100",
        skip: "0",
      };

      doAjax(
        `/${destination_ProfitCenter_Mass}/data/getProfitCentersNo`,
        "post",
        (data) => {
          if (Array.isArray(data.body?.list)) {
            const newProfitCenters = data.body.list.map((item) => ({
              code: item.code,
              desc: item.desc,
              companyCode: companyCode, // Needed for filtering
            }));

            // Merge without duplicates
            setProfitCenterOptions((prev) => {
              const existingCodes = new Set(prev.map((pc) => pc.code));
              const uniqueNew = newProfitCenters.filter(
                (pc) => !existingCodes.has(pc.code)
              );
              return [...prev, ...uniqueNew];
            });
          }
        },
        (err) => console.error("Profit Center fetch failed", err),
        payload
      );
    });
  };

  // const fetchProfitCenters = () => {
  //   const controllingArea = `${dropdown1Value}`;

  //   selectedCompanyCodes.forEach((companyCode) => {
  //     const payload = {
  //       controllingArea,
  //       companyCode,
  //       top: "100",
  //       skip: "0",
  //     };

  //     doAjax(
  //       `/${destination_ProfitCenter_Mass}/data/getProfitCentersNo`,
  //       "post",
  //       (data) => {
  //         if (Array.isArray(data.body?.list)) {
  //           // Get unique profit center codes
  //           const newProfitCenters = data.body.list
  //             .map((item) => item.code)
  //             .filter(
  //               (code, index, self) => code && self.indexOf(code) === index
  //             );

  //           // Merge with existing, avoiding duplicates
  //           setProfitCenterOptions((prev) => [
  //             ...new Set([...prev, ...newProfitCenters]),
  //           ]);
  //         }
  //       },
  //       (err) => console.error("Profit Center fetch failed", err),
  //       payload
  //     );
  //   });
  // };

  const transformProfitCenterData = (data) => {
    const isBlockTemplate = template?.toLowerCase().includes("block");

    return data.map((item) => ({
      id: item.profitCenter,
      profitCenter: item.profitCenter,
      controllingArea: item.controllingArea,
      profitCenterName: item.profitCenterName,
      description: item.basicDataTabDto?.Description || "",
      segment: item.basicDataTabDto?.Segment || "",
      pcaamnum: item.basicDataTabDto?.PCAAMNumber || "",
      lockIndicator:
        isBlockTemplate && !item?.indicatorsTabDto?.LockIndicator ? "X" : "",
      userResponsible: item.basicDataTabDto?.UserResponsible || "",
      personResponsible: item.basicDataTabDto?.PersonResponsible || "",
      createdBy: item.historyTabDto?.ReqCreatedBy || "",
      validFrom: item.basicDataTabDto?.ValidfromDate || "",
      validTo: item.basicDataTabDto?.ValidtoDate || "",
      city: item.addressTabDto?.City || "",
      country: item.addressTabDto?.Country || "",
      street: item.addressTabDto?.Street || "",
      pocode: item.addressTabDto?.PostalCode || "",
      region: item.addressTabDto?.Regio || "",
      name1: item.addressTabDto?.Name1 || "",
      name2: item.addressTabDto?.Name2 || "",
      name3: item.addressTabDto?.Name3 || "",
      name4: item.addressTabDto?.Name4 || "",

      // ✅ Add this to fetch first company code (if array is not empty)
      companyCode: item.compCodesTabDto?.CompanyCode?.[0] || "",
    }));
  };

  const fetchProfitCenterDetails = () => {
    if (!selectedProfitCenters.length || !dropdown1Value) return;

    const payload = {
      coAreaPCs: selectedProfitCenters.map((pc) => ({
        profitCenter: pc,
        controllingArea: dropdown1Value,
      })),
    };

    const successHandler = (data) => {
      const rawData = data?.body || [];

      setProfitcenterResponse(rawData);

      const transformed = transformProfitCenterData(rawData);

      dispatch(setFetchedProfitCenterDataPc(transformed));
      dispatch(setOriginalProfitCenterDataPc(transformed));
    };

    const errorHandler = (err) => {
      console.error("Failed to fetch profit center details", err);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCentersData`,
      "post",
      successHandler,
      errorHandler,
      payload
    );
  };

  useEffect(() => {
    if (
      reqBench === "true" &&
      template &&
      Array.isArray(apiResponses) &&
      apiResponses.length > 0 &&
      fetchReqBenchData.length === 0
    ) {
      const transformedData = transformProfitCenterResponseChange(
        apiResponses,
        template
      );

      // setFetchReqBenchData(transformedData);
      // setOriginalReqBenchData(transformedData);

      dispatch(setFetchReqBenchDataPc(transformedData));
      dispatch(setOriginalReqBenchDataPc(transformedData));
    }
  }, [apiResponses, reqBench, template]);

  useEffect(() => {
    if (downloadClicked) {
      setOpen(true);
    }
  }, [downloadClicked]);

  const parsedData = (apiResponses ?? []).map((item) => {
    let changedFields = {};

    // Prioritize the already parsed object if present
    if (typeof item.changedFields === "object" && item.changedFields !== null) {
      changedFields = item.changedFields;
    } else if (typeof item.ChangedFields === "string") {
      try {
        changedFields = JSON.parse(item.ChangedFields);
      } catch {
        changedFields = {};
      }
    }

    const { changedFields: _, ChangedFields, ...rest } = item;

    return {
      ...rest,
      changedFields,
    };
  });

  useEffect(() => {
    if (!parsedData || parsedData.length === 0) return;

    const newChangedFieldsMap = {};
    parsedData.forEach((row) => {
      newChangedFieldsMap[row.ProfitCenterID] = row.changedFields || {};
    });

    dispatch(setChangedFieldsMapPc(newChangedFieldsMap));
  }, [apiResponses]);

  const validateMandatoryFields = (rows, mandatoryFields) => {
    const errors = [];

    rows.forEach((row, index) => {
      const missingFields = mandatoryFields.filter((field) => {
        const value = row[field];
        return (
          value === undefined ||
          value === null ||
          value.toString().trim() === ""
        );
      });

      if (missingFields.length > 0) {
        errors.push({
          rowIndex: index + 1,
          rowId: row.id,
          missingFields,
        });
      }
    });

    if (errors.length > 0) {
      const errorMessages = errors.map(
        (err) =>
          `Row ${err.rowIndex} (ID: ${
            err.rowId || "N/A"
          }) is missing: ${err.missingFields.join(", ")}`
      );
      alert("Validation Failed:\n" + errorMessages.join("\n"));
      return false;
    }

    alert("Validation Passed!");
    return true;
  };

  const handleSaveAsDraft = () => {
    setBlurLoading(true);
    const allData = reqBench ? fetchReqBenchData : fetchedProfitCenterData;

    const Payload = allData.map((pcData) => {
      return {
        requestInProcess: "",
        ProfitCenterID: pcData.ProfitCenterID || "",
        TemplateName: requestHeaderData?.TemplateName || "",
        TemplateHeaders: "",
        IsScheduled: null,
        Action: "",
        RequestID: "",
        TaskStatus: null,
        TaskId: null,
        ReqCreatedBy: pcData?.createdBy || "",
        ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
        RequestStatus: requestHeaderData?.requestStatus || "",
        CreationId: "",
        EditId: "",
        DeleteId: "",
        MassCreationId: "",
        MassEditId: pcData?.MassEditId || "",
        MassDeleteId: "",
        ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
        RequestType: requestHeaderData?.requestType || "",
        MassRequestStatus: "",
        Remarks: null,
        TempLockRemarks: null,
        Info: "",
        ChangedFields: "",
        ProfitCenterName: pcData?.profitCenterName || "",
        Description: pcData?.description || "",
        UserResponsible: pcData?.userResponsible || "",
        PersonResponsible: pcData?.personResponsible || "",
        Department: pcData?.Department || "",
        PrctrHierGrp: "ET_PCA",
        Segment: pcData?.segment || "",
        LockIndicator: pcData?.lockIndicator ? "X" : "",
        // LockIndicator: isBlockTemplate && !pcData?.lockIndicator ? "X" : "",
        Template: "",
        Title: pcData?.addressTabDto?.Title || "",
        Name1: pcData?.name1 || "",
        Name2: pcData?.name2 || "",
        Name3: pcData?.name3 || "",
        Name4: pcData?.name4 || "",
        Street: pcData?.street || "",
        City: pcData?.city || "",
        District: pcData?.district || "",
        Country: pcData?.country || "",
        Taxjurcode: pcData?.TaxJurisdiction || "",
        PoBox: pcData?.PoBox || "",
        PostalCode: pcData?.pocode || pcData?.PostalCode || "",
        PobxPcd: pcData?.PobxPcd || "",
        Regio: pcData?.region || "",
        Language: pcData?.communicationTabDto?.Language || "",
        Telephone: pcData?.communicationTabDto?.Telephone || "",
        Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
        Telebox: pcData?.communicationTabDto?.Telebox || "",
        Telex: pcData?.communicationTabDto?.Telex || "",
        FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
        Teletex: pcData?.communicationTabDto?.Teletex || "",
        Printer: pcData?.communicationTabDto?.Printer || "",
        DataLine: pcData?.communicationTabDto?.DataLine || "",
        ProfitCenter: pcData?.profitCenter || "",
        COArea: pcData?.controllingArea || "ETCA",
        ValidfromDate:
          pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
        ValidtoDate:
          pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
        Testrun: null,
        IsFirstSynCompleted: false,
        TempLockIsSelectedForSyn: false,
        SelectedByRequestorToDisplay: true,
        IsSunoco: false,
        Countryiso: "",
        LanguIso: "",
        Logsystem: "",
        GeneralInfoID: null,
        RequestPriority: requestHeaderData?.requestPriority || "",
        BusinessJustification: null,
        SAPorJEErrorCheck: null,
        BusinessSegment: pcData.businessSegment || "CRUDE",
        HierarchyRegion: "ET_PCA",
        PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
        ValidationDoneBy: "",
        TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

        ToCompanycode: [
          {
            CompCodeID: pcData.CompCodeID || "",
            CompanyCode: pcData.companyCode || "",
            CompanyName: "",
            AssignToPrctr: "",
            Venture: "",
            RecInd: "",
            EquityTyp: "",
            JvOtype: "",
            JvJibcl: "",
            JvJibsa: "",
          },
        ],

        Torequestheaderdata: {
          RequestId:
            requestHeaderData?.RequestId || initialPayload?.requestId || "",
          ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
          ReqCreatedOn:
            requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
          ReqUpdatedOn:
            requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
          RequestType: requestHeaderData?.RequestType || "",
          RequestPrefix: requestHeaderData?.RequestPrefix || "",
          RequestPriority: requestHeaderData?.RequestPriority || "",
          RequestDesc: requestHeaderData?.RequestDesc || "",
          RequestStatus: "Draft",
          FirstProd: "",
          LaunchDate: "",
          LeadingCat: "Anesthesia/Pain Management",
          Division: "00",
          TemplateName: "",
          FieldName: "",
          Region: "",
          FilterDetails: null,
          IsBifurcated: null,
        },

        TochangeLogData: {
          ChangeLogId: pcData.ChangeLogId || "",
          RequestId: null,
          RequestHeaderId: "",
          ChangeLogData: null,
        },

        ToProfitCenterErrorData: {
          ProfitCenterErrorId: pcData?.ProfitCenterErrorID || "",
          RequestId: "",
          ProfitCenter: pcData?.profitCenter || "",
          CompanyCode: pcData?.companyCode || "",
          Segment: pcData?.basicDataTabDto?.Segment || "",
          PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
          ControllingArea: pcData?.controllingArea || "",
          SapMessage: "",
          ObjectSapError: "",
          ObjectDbError: "",
          ObjectExcelError: "",
          ShortDescSapError: "",
          ShortDescDbError: "",
          ShortDescExcelError: "",
          LongDescSapError: "",
          LongDescDbError: "",
          LongDescExcelError: "",
          AddrValidationError: "",
          PersonResponsibleError: "",
          DmsAttachmentErrorStatus: "",
          SapAttachmentErrorStatus: "",
          RemovalNodeErrorStatus: "",
          AddNodeErrorStatus: "",
        },

        ToGeneralInfoData: {
          GeneralInfoId: pcData.GeneralInfoId || "",
          RequestPriority: "",
          BusinessJustification: "",
          SAPorJEErrorCheck: "",
          BusinessSegment: pcData.businessSegment || "CRUDE",
          Region: "",
        },
      };
    });

    const hSuccess = (data) => {
      setBlurLoading(false);
      setAlertType("success");
      setAlertMsg(
        "Profit Centers change submission for save as draft initiated"
      );
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
     ;
      setBlurLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/changeProfitCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };
  const validateAllRows = (row) => {
    setBlurLoading(true);
    const allData = reqBench ? fetchReqBenchData : fetchedProfitCenterData;

    // const errors = [];

    // allData.forEach((row, index) => {
    //   const missingFields = mandatoryFields.filter((fieldName) => {
    //     const value = row?.[fieldName];
    //     return (
    //       value === undefined ||
    //       value === null ||
    //       value.toString().trim() === ""
    //     );
    //   });

    //   if (missingFields.length > 0) {
    //     errors.push({
    //       rowIndex: index + 1,
    //       rowId: row?.id || row?.ProfitCenterID || "N/A",
    //       missingFields,
    //     });
    //   }
    // });

    // if (errors.length > 0) {
    //   const errorMessages = errors.map(
    //     (err) =>
    //       `Row ${err.rowIndex} (ID: ${
    //         err.rowId
    //       }): Missing fields - ${err.missingFields.join(", ")}`
    //   );

    //   alert("Validation Failed:\n" + errorMessages.join("\n"));
    //   return;
    // }
    const Payload = allData.map((pcData) => {
      return {
        requestInProcess: "",
        ProfitCenterID: pcData.ProfitCenterID || "",
        TemplateName: requestHeaderData?.TemplateName || "",
        TemplateHeaders: "",
        IsScheduled: null,
        Action: "",
        RequestID: "",
        TaskStatus: null,
        TaskId: null,
        ReqCreatedBy: pcData?.createdBy || "",
        ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
        RequestStatus: requestHeaderData?.requestStatus || "",
        CreationId: "",
        EditId: "",
        DeleteId: "",
        MassCreationId: "",
        MassEditId: pcData?.MassEditId || "",
        MassDeleteId: "",
        ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
        RequestType: requestHeaderData?.requestType || "",
        MassRequestStatus: "",
        Remarks: null,
        TempLockRemarks: null,
        Info: "",
        ChangedFields: "",
        ProfitCenterName: pcData?.profitCenterName || "",
        Description: pcData?.description || "",
        userResponsible: pcData?.userResponsible || "",
        PersonResponsible: pcData?.personResponsible || "",
        Department: pcData?.Department || "",
        PrctrHierGrp: "ET_PCA",
        Segment: pcData?.segment || "",
        LockIndicator: pcData?.lockIndicator ? "X" : "",
        // LockIndicator: isBlockTemplate && !pcData?.lockIndicator ? "X" : "",
        Template: "",
        Title: pcData?.addressTabDto?.Title || "",
        Name1: pcData?.name1 || "",
        Name2: pcData?.name2 || "",
        Name3: pcData?.name3 || "",
        Name4: pcData?.name4 || "",
        Street: pcData?.street || "",
        City: pcData?.city || "",
        District: pcData?.district || "",
        Country: pcData?.country || "",
        Taxjurcode: pcData?.TaxJurisdiction || "",
        PoBox: pcData?.PoBox || "",
        PostalCode: pcData?.pocode || pcData?.PostalCode || "",
        PobxPcd: pcData?.PobxPcd || "",
        Regio: pcData?.region || "",
        Language: pcData?.communicationTabDto?.Language || "",
        Telephone: pcData?.communicationTabDto?.Telephone || "",
        Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
        Telebox: pcData?.communicationTabDto?.Telebox || "",
        Telex: pcData?.communicationTabDto?.Telex || "",
        FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
        Teletex: pcData?.communicationTabDto?.Teletex || "",
        Printer: pcData?.communicationTabDto?.Printer || "",
        DataLine: pcData?.communicationTabDto?.DataLine || "",
        ProfitCenter: pcData?.profitCenter || "",
        COArea: pcData?.controllingArea || "ETCA",
        ValidfromDate:
          pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
        ValidtoDate:
          pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
        Testrun: null,
        IsFirstSynCompleted: false,
        TempLockIsSelectedForSyn: false,
        SelectedByRequestorToDisplay: true,
        IsSunoco: false,
        Countryiso: "",
        LanguIso: "",
        Logsystem: "",
        GeneralInfoID: null,
        RequestPriority: requestHeaderData?.requestPriority || "",
        BusinessJustification: null,
        SAPorJEErrorCheck: null,
        BusinessSegment: pcData.businessSegment || "CRUDE",
        HierarchyRegion: "ET_PCA",
        PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
        ValidationDoneBy: "",
        TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

        ToCompanycode: [
          {
            CompCodeID: pcData.CompCodeID || "",
            CompanyCode: pcData.companyCode || "",
            CompanyName: "",
            AssignToPrctr: "",
            Venture: "",
            RecInd: "",
            EquityTyp: "",
            JvOtype: "",
            JvJibcl: "",
            JvJibsa: "",
          },
        ],

        Torequestheaderdata: {
          RequestId:
            requestHeaderData?.RequestId || initialPayload?.requestId || "",
          ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
          ReqCreatedOn:
            requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
          ReqUpdatedOn:
            requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
          RequestType: requestHeaderData?.RequestType || "",
          RequestPrefix: requestHeaderData?.RequestPrefix || "",
          RequestPriority: requestHeaderData?.RequestPriority || "",
          RequestDesc: requestHeaderData?.RequestDesc || "",
          RequestStatus: "Draft",
          FirstProd: "",
          LaunchDate: "",
          LeadingCat: "Anesthesia/Pain Management",
          Division: "00",
          TemplateName: "",
          FieldName: "",
          Region: "",
          FilterDetails: null,
          IsBifurcated: null,
        },

        TochangeLogData: {
          ChangeLogId: pcData.ChangeLogId || "",
          RequestId: null,
          RequestHeaderId: "",
          ChangeLogData: null,
        },

        ToProfitCenterErrorData: {
          ProfitCenterErrorId: pcData?.ProfitCenterErrorID || "",
          RequestId: "",
          ProfitCenter: pcData?.profitCenter || "",
          CompanyCode: pcData?.companyCode || "",
          Segment: pcData?.basicDataTabDto?.Segment || "",
          PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
          ControllingArea: pcData?.controllingArea || "",
          SapMessage: "",
          ObjectSapError: "",
          ObjectDbError: "",
          ObjectExcelError: "",
          ShortDescSapError: "",
          ShortDescDbError: "",
          ShortDescExcelError: "",
          LongDescSapError: "",
          LongDescDbError: "",
          LongDescExcelError: "",
          AddrValidationError: "",
          PersonResponsibleError: "",
          DmsAttachmentErrorStatus: "",
          SapAttachmentErrorStatus: "",
          RemovalNodeErrorStatus: "",
          AddNodeErrorStatus: "",
        },

        ToGeneralInfoData: {
          GeneralInfoId: pcData.GeneralInfoId || "",
          RequestPriority: "",
          BusinessJustification: "",
          SAPorJEErrorCheck: "",
          BusinessSegment: pcData.businessSegment || "CRUDE",
          Region: "",
        },
      };
    });

  

    const hSuccess = (data) => {
      setBlurLoading(false);
      setAlertType("success");
      setAlertMsg(
        "Profit Centers change submission for save as draft initiated"
      );
      setOpenSnackBar(true);
      setIsValidated(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/validateMassProfitCenter`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const handleSubmitForReview = () => {
    setBlurLoading(true);
    const allData = reqBench ? fetchReqBenchData : fetchedProfitCenterData;

    const Payload = allData.map((pcData) => {
      return {
        requestInProcess: "",
        ProfitCenterID: pcData.ProfitCenterID || "",
        TemplateName: requestHeaderData?.TemplateName || "",
        TemplateHeaders: "",
        IsScheduled: null,
        Action: "",
        RequestID: "",
        TaskStatus: null,
        TaskId: null,
        ReqCreatedBy: pcData?.createdBy || "",
        ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
        RequestStatus: requestHeaderData?.requestStatus || "",
        CreationId: "",
        EditId: "",
        DeleteId: "",
        MassCreationId: "",
        MassEditId: pcData?.MassEditId || "",
        MassDeleteId: "",
        ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
        RequestType: requestHeaderData?.requestType || "",
        MassRequestStatus: "",
        Remarks: null,
        TempLockRemarks: null,
        Info: "",
        ChangedFields: "",
        ProfitCenterName: pcData?.profitCenterName || "",
        Description: pcData?.description || "",
        userResponsible: pcData?.userResponsible || "",
        PersonResponsible: pcData?.personResponsible || "",
        Department: pcData?.Department || "",
        PrctrHierGrp: pcData?.PrctrHierGrp || "",
        Segment: pcData?.segment || "",
        LockIndicator: pcData?.lockIndicator ? "X" : "",
        Template: "",
        Title: pcData?.addressTabDto?.Title || "",
        Name1: pcData?.name1 || "",
        Name2: pcData?.name2 || "",
        Name3: pcData?.name3 || "",
        Name4: pcData?.name4 || "",
        Street: pcData?.street || "",
        City: pcData?.city || "",
        District: pcData?.district || "",
        Country: pcData?.country || "",
        Taxjurcode: pcData?.TaxJurisdiction || "",
        PoBox: pcData?.PoBox || "",
        PostalCode: pcData?.pocode || pcData?.PostalCode || "",
        PobxPcd: pcData?.PobxPcd || "",
        Regio: pcData?.region || "",
        Language: pcData?.communicationTabDto?.Language || "",
        Telephone: pcData?.communicationTabDto?.Telephone || "",
        Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
        Telebox: pcData?.communicationTabDto?.Telebox || "",
        Telex: pcData?.communicationTabDto?.Telex || "",
        FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
        Teletex: pcData?.communicationTabDto?.Teletex || "",
        Printer: pcData?.communicationTabDto?.Printer || "",
        DataLine: pcData?.communicationTabDto?.DataLine || "",
        ProfitCenter: pcData?.profitCenter || "",
        COArea: pcData?.controllingArea || "ETCA",
        ValidfromDate:
          pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
        ValidtoDate:
          pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
        Testrun: null,
        IsFirstSynCompleted: false,
        TempLockIsSelectedForSyn: false,
        SelectedByRequestorToDisplay: true,
        IsSunoco: false,
        Countryiso: "",
        LanguIso: "",
        Logsystem: "",
        GeneralInfoID: null,
        RequestPriority: requestHeaderData?.requestPriority || "",
        BusinessJustification: null,
        SAPorJEErrorCheck: null,
        BusinessSegment: pcData.businessSegment || "CRUDE",
        HierarchyRegion: null,
        PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
        ValidationDoneBy: "",
        TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

        ToCompanycode: [
          {
            CompCodeID: pcData.CompCodeID || "",
            CompanyCode: pcData.companyCode || "",
            CompanyName: "",
            AssignToPrctr: "",
            Venture: "",
            RecInd: "",
            EquityTyp: "",
            JvOtype: "",
            JvJibcl: "",
            JvJibsa: "",
          },
        ],

        Torequestheaderdata: {
          RequestId:
            requestHeaderData?.RequestId || initialPayload?.requestId || "",
          ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
          ReqCreatedOn:
            requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
          ReqUpdatedOn:
            requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
          RequestType: requestHeaderData?.RequestType || "",
          RequestPrefix: requestHeaderData?.RequestPrefix || "",
          RequestPriority: requestHeaderData?.RequestPriority || "",
          RequestDesc: requestHeaderData?.RequestDesc || "",
          RequestStatus: "Draft",
          FirstProd: "",
          LaunchDate: "",
          LeadingCat: "Anesthesia/Pain Management",
          Division: "00",
          TemplateName: "",
          FieldName: "",
          Region: "",
          FilterDetails: null,
          IsBifurcated: null,
        },

        TochangeLogData: {
          ChangeLogId: pcData.ChangeLogId || "",
          RequestId: null,
          RequestHeaderId: "",
          ChangeLogData: null,
        },

        ToProfitCenterErrorData: {
          ProfitCenterErrorId: pcData?.ProfitCenterErrorID || "",
          RequestId: "",
          ProfitCenter: pcData?.profitCenter || "",
          CompanyCode: pcData?.companyCode || "",
          Segment: pcData?.basicDataTabDto?.Segment || "",
          PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
          ControllingArea: pcData?.controllingArea || "",
          SapMessage: "",
          ObjectSapError: "",
          ObjectDbError: "",
          ObjectExcelError: "",
          ShortDescSapError: "",
          ShortDescDbError: "",
          ShortDescExcelError: "",
          LongDescSapError: "",
          LongDescDbError: "",
          LongDescExcelError: "",
          AddrValidationError: "",
          PersonResponsibleError: "",
          DmsAttachmentErrorStatus: "",
          SapAttachmentErrorStatus: "",
          RemovalNodeErrorStatus: "",
          AddNodeErrorStatus: "",
        },

        ToGeneralInfoData: {
          GeneralInfoId: pcData.GeneralInfoId || "",
          RequestPriority: "",
          BusinessJustification: "",
          SAPorJEErrorCheck: "",
          BusinessSegment: pcData.businessSegment || "CRUDE",
          Region: "",
        },
      };
    });

 

    const hSuccess = (data) => {
     setBlurLoading(false);
      setAlertType("success");
      setAlertMsg("Profit Centers submit for review successful");
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      console.log("Errordata", error);
      setBlurLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/changeProfitCentersSubmitForReview`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const handleSubmitForApprove = () => {
    setBlurLoading(true);
    const Payload = fetchReqBenchData.map((pcData) => {
      return {
        requestInProcess: "",
        ProfitCenterID: pcData.ProfitCenterID || "",
        TemplateName: requestHeaderData?.TemplateName || "",
        TemplateHeaders: "",
        IsScheduled: null,
        Action: "",
        RequestID: pcData.RequestID || "",
        TaskStatus: null,
        TaskId: task?.taskId,
        ReqCreatedBy: pcData?.createdBy || "",
        ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
        RequestStatus: requestHeaderData?.requestStatus || "",
        CreationId: "",
        EditId: "",
        DeleteId: "",
        MassCreationId: "",
        MassEditId: requestId || "",
        MassDeleteId: "",
        ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
        RequestType: requestHeaderData?.requestType || "",
        MassRequestStatus: "",
        Remarks: null,
        TempLockRemarks: null,
        Info: "",
        ChangedFields: "",
        ProfitCenterName: pcData?.profitCenterName || "",
        Description: pcData?.description || "",
        userResponsible: pcData?.userResponsible || "",
        PersonResponsible: pcData?.personResponsible || "",
        Department: pcData?.Department || "",
        PrctrHierGrp: pcData?.PrctrHierGrp || "",
        Segment: pcData?.segment || "",
        LockIndicator: pcData?.lockIndicator ? "X" : "",
        Template: "",
        Title: pcData?.addressTabDto?.Title || "",
        Name1: pcData?.name1 || "",
        Name2: pcData?.name2 || "",
        Name3: pcData?.name3 || "",
        Name4: pcData?.name4 || "",
        Street: pcData?.street || "",
        City: pcData?.city || "",
        District: pcData?.district || "",
        Country: pcData?.country || "",
        Taxjurcode: pcData?.TaxJurisdiction || "",
        PoBox: pcData?.PoBox || "",
        PostalCode: pcData?.pocode || pcData?.PostalCode || "",
        PobxPcd: pcData?.PobxPcd || "",
        Regio: pcData?.region || "",
        Language: pcData?.communicationTabDto?.Language || "",
        Telephone: pcData?.communicationTabDto?.Telephone || "",
        Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
        Telebox: pcData?.communicationTabDto?.Telebox || "",
        Telex: pcData?.communicationTabDto?.Telex || "",
        FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
        Teletex: pcData?.communicationTabDto?.Teletex || "",
        Printer: pcData?.communicationTabDto?.Printer || "",
        DataLine: pcData?.communicationTabDto?.DataLine || "",
        ProfitCenter: pcData?.profitCenter || "",
        COArea: pcData?.controllingArea || "ETCA",
        ValidfromDate:
          pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
        ValidtoDate:
          pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
        Testrun: null,
        IsFirstSynCompleted: false,
        TempLockIsSelectedForSyn: false,
        SelectedByRequestorToDisplay: true,
        IsSunoco: false,
        Countryiso: "",
        LanguIso: "",
        Logsystem: "",
        GeneralInfoID: null,
        RequestPriority: requestHeaderData?.requestPriority || "",
        BusinessJustification: null,
        SAPorJEErrorCheck: null,
        BusinessSegment: pcData.businessSegment || "CRUDE",
        HierarchyRegion: null,
        PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
        ValidationDoneBy: "",
        TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

        ToChildHeaderdata: {
          RequestId: pcData.RequestID ?? "",
          Status: requestHeaderData?.RequestStatus || "",
          IntermediateTaskCount: null,
          TotalTaskCount: null,
          BifurcatedValue: "",
          TaskId: null,
          CreatedOn: "2025-06-05T06:34:55.995+00:00",
          UpdatedOn: "2025-06-05T06:34:58.653+00:00",
          RequestType: "Mass Change",

          CurrentLevel: task?.ATTRIBUTE_3 || 0,
          CurrentLevelName: task?.ATTRIBUTE_4 || "",
          IsScheduled: "",
          ParticularLevel: "",
          TaskCreatedOn: "",
          TaskName: "",
        },

        ToCompanycode: [
          {
            CompCodeID: pcData.CompCodeID,
            CompanyCode: pcData.companyCode || "",
            CompanyName: "",
            AssignToPrctr: "",
            Venture: "",
            RecInd: "",
            EquityTyp: "",
            JvOtype: "",
            JvJibcl: "",
            JvJibsa: "",
          },
        ],

        Torequestheaderdata: {
          RequestId: requestHeaderData?.RequestId || "",
          ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
          ReqCreatedOn:
            requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
          ReqUpdatedOn:
            requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
          RequestType: requestHeaderData?.RequestType || "",
          RequestPrefix: requestHeaderData?.RequestPrefix || "",
          RequestPriority: requestHeaderData?.RequestPriority || "",
          RequestDesc: requestHeaderData?.RequestDesc || "",
          RequestStatus: "Draft",
          FirstProd: "",
          LaunchDate: "",
          LeadingCat: "Anesthesia/Pain Management",
          Division: "00",
          TemplateName: "",
          FieldName: "",
          Region: "",
          FilterDetails: null,
          IsBifurcated: null,
        },

        TochangeLogData: {
          ChangeLogData: null,
          ChangeLogId: pcData.ChangeLogId || "",
          RequestHeaderId: "",
          RequestId: "",
        },

        ToProfitCenterErrorData: {
          ProfitCenterErrorId: pcData.ProfitCenterErrorID || "",
          RequestId: pcData.RequestID || "",
          ProfitCenter: pcData?.profitCenter || "",
          CompanyCode: pcData?.companyCode || "",
          Segment: pcData?.basicDataTabDto?.Segment || "",
          PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
          ControllingArea: pcData?.controllingArea || "",
          SapMessage: "",
          ObjectSapError: "",
          ObjectDbError: "",
          ObjectExcelError: "",
          ShortDescSapError: "",
          ShortDescDbError: "",
          ShortDescExcelError: "",
          LongDescSapError: "",
          LongDescDbError: "",
          LongDescExcelError: "",
          AddrValidationError: "",
          PersonResponsibleError: "",
          DmsAttachmentErrorStatus: "",
          SapAttachmentErrorStatus: "",
          RemovalNodeErrorStatus: "",
          AddNodeErrorStatus: "",
        },

        ToGeneralInfoData: {
          GeneralInfoId: pcData.GeneralInfoId || "",
          RequestPriority: "",
          BusinessJustification: "",
          SAPorJEErrorCheck: "",
          BusinessSegment: pcData.businessSegment || "CRUDE",
          Region: "",
        },
      };
    });
  
    const hSuccess = (data) => {
      setBlurLoading(false);
      setAlertType("success");
      setAlertMsg("Profit Centers successfuly Approved");
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      console.log("Errordata", error);
      setBlurLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/changeProfitCentersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const handleSendBack = () => {
    setBlurLoading(true);
    const Payload = fetchReqBenchData.map((pcData) => {
      return {
        requestInProcess: "",
        ProfitCenterID: pcData.ProfitCenterID || "",
        TemplateName: requestHeaderData?.TemplateName || "",
        TemplateHeaders: "",
        IsScheduled: null,
        Action: "",
        RequestID: pcData.RequestID || "",
        TaskStatus: null,
        TaskId: task?.taskId,
        ReqCreatedBy: pcData?.createdBy || "",
        ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
        RequestStatus: requestHeaderData?.requestStatus || "",
        CreationId: "",
        EditId: "",
        DeleteId: "",
        MassCreationId: "",
        MassEditId: requestId || "",
        MassDeleteId: "",
        ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
        RequestType: requestHeaderData?.requestType || "",
        MassRequestStatus: "",
        Remarks: null,
        TempLockRemarks: null,
        Info: "",
        ChangedFields: "",
        ProfitCenterName: pcData?.profitCenterName || "",
        Description: pcData?.description || "",
        userResponsible: pcData?.userResponsible || "",
        PersonResponsible: pcData?.personResponsible || "",
        Department: pcData?.Department || "",
        PrctrHierGrp: pcData?.PrctrHierGrp || "",
        Segment: pcData?.segment || "",
        LockIndicator: pcData?.lockIndicator ? "X" : "",
        Template: "",
        Title: pcData?.addressTabDto?.Title || "",
        Name1: pcData?.name1 || "",
        Name2: pcData?.name2 || "",
        Name3: pcData?.name3 || "",
        Name4: pcData?.name4 || "",
        Street: pcData?.street || "",
        City: pcData?.city || "",
        District: pcData?.district || "",
        Country: pcData?.country || "",
        Taxjurcode: pcData?.TaxJurisdiction || "",
        PoBox: pcData?.PoBox || "",
        PostalCode: pcData?.pocode || pcData?.PostalCode || "",
        PobxPcd: pcData?.PobxPcd || "",
        Regio: pcData?.region || "",
        Language: pcData?.communicationTabDto?.Language || "",
        Telephone: pcData?.communicationTabDto?.Telephone || "",
        Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
        Telebox: pcData?.communicationTabDto?.Telebox || "",
        Telex: pcData?.communicationTabDto?.Telex || "",
        FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
        Teletex: pcData?.communicationTabDto?.Teletex || "",
        Printer: pcData?.communicationTabDto?.Printer || "",
        DataLine: pcData?.communicationTabDto?.DataLine || "",
        ProfitCenter: pcData?.profitCenter || "",
        COArea: pcData?.controllingArea || "ETCA",
        ValidfromDate:
          pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
        ValidtoDate:
          pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
        Testrun: null,
        IsFirstSynCompleted: false,
        TempLockIsSelectedForSyn: false,
        SelectedByRequestorToDisplay: true,
        IsSunoco: false,
        Countryiso: "",
        LanguIso: "",
        Logsystem: "",
        GeneralInfoID: null,
        RequestPriority: requestHeaderData?.requestPriority || "",
        BusinessJustification: null,
        SAPorJEErrorCheck: null,
        BusinessSegment: pcData.businessSegment || "CRUDE",
        HierarchyRegion: null,
        PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
        ValidationDoneBy: "",
        TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

        ToChildHeaderdata: {
          RequestId: pcData.RequestID ?? "",
          Status: requestHeaderData?.RequestStatus || "",
          IntermediateTaskCount: null,
          TotalTaskCount: null,
          BifurcatedValue: "",
          TaskId: null,
          CreatedOn: "2025-06-05T06:34:55.995+00:00",
          UpdatedOn: "2025-06-05T06:34:58.653+00:00",
          RequestType: "Mass Change",

          CurrentLevel: task?.ATTRIBUTE_3 || 0,
          CurrentLevelName: task?.ATTRIBUTE_4 || "",
          IsScheduled: "",
          ParticularLevel: "",
          TaskCreatedOn: "",
          TaskName: "",
        },

        ToCompanycode: [
          {
            CompCodeID: pcData.CompCodeID,
            CompanyCode: pcData.companyCode || "",
            CompanyName: "",
            AssignToPrctr: "",
            Venture: "",
            RecInd: "",
            EquityTyp: "",
            JvOtype: "",
            JvJibcl: "",
            JvJibsa: "",
          },
        ],

        Torequestheaderdata: {
          RequestId: requestHeaderData?.RequestId || "",
          ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
          ReqCreatedOn:
            requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
          ReqUpdatedOn:
            requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
          RequestType: requestHeaderData?.RequestType || "",
          RequestPrefix: requestHeaderData?.RequestPrefix || "",
          RequestPriority: requestHeaderData?.RequestPriority || "",
          RequestDesc: requestHeaderData?.RequestDesc || "",
          RequestStatus: "Draft",
          FirstProd: "",
          LaunchDate: "",
          LeadingCat: "Anesthesia/Pain Management",
          Division: "00",
          TemplateName: "",
          FieldName: "",
          Region: "",
          FilterDetails: null,
          IsBifurcated: null,
        },

        TochangeLogData: {
          ChangeLogData: null,
          ChangeLogId: pcData.ChangeLogId || "",
          RequestHeaderId: "",
          RequestId: "",
        },

        ToProfitCenterErrorData: {
          ProfitCenterErrorId: pcData.ProfitCenterErrorID || "",
          RequestId: pcData.RequestID || "",
          ProfitCenter: pcData?.profitCenter || "",
          CompanyCode: pcData?.companyCode || "",
          Segment: pcData?.basicDataTabDto?.Segment || "",
          PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
          ControllingArea: pcData?.controllingArea || "",
          SapMessage: "",
          ObjectSapError: "",
          ObjectDbError: "",
          ObjectExcelError: "",
          ShortDescSapError: "",
          ShortDescDbError: "",
          ShortDescExcelError: "",
          LongDescSapError: "",
          LongDescDbError: "",
          LongDescExcelError: "",
          AddrValidationError: "",
          PersonResponsibleError: "",
          DmsAttachmentErrorStatus: "",
          SapAttachmentErrorStatus: "",
          RemovalNodeErrorStatus: "",
          AddNodeErrorStatus: "",
        },

        ToGeneralInfoData: {
          GeneralInfoId: pcData.GeneralInfoId || "",
          RequestPriority: "",
          BusinessJustification: "",
          SAPorJEErrorCheck: "",
          BusinessSegment: pcData.businessSegment || "CRUDE",
          Region: "",
        },
      };
    });

 
    const hSuccess = (data) => {
      setBlurLoading(false);
      setAlertType("success");
      setAlertMsg("Profit Centers successfuly Approved");
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      console.log("Errordata", error);
      setBlurLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/changeProfitCentersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const handleRejectAndCancel = () => {
    setBlurLoading(true);
    const Payload = fetchReqBenchData.map((pcData) => {
      return {
        requestInProcess: "",
        ProfitCenterID: pcData.ProfitCenterID || "",
        TemplateName: requestHeaderData?.TemplateName || "",
        TemplateHeaders: "",
        IsScheduled: null,
        Action: "",
        RequestID: pcData.RequestID || "",
        TaskStatus: null,
        TaskId: task?.taskId,
        ReqCreatedBy: pcData?.createdBy || "",
        ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
        RequestStatus: requestHeaderData?.requestStatus || "",
        CreationId: "",
        EditId: "",
        DeleteId: "",
        MassCreationId: "",
        MassEditId: requestId || "",
        MassDeleteId: "",
        ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
        RequestType: requestHeaderData?.requestType || "",
        MassRequestStatus: "",
        Remarks: null,
        TempLockRemarks: null,
        Info: "",
        ChangedFields: "",
        ProfitCenterName: pcData?.profitCenterName || "",
        Description: pcData?.description || "",
        userResponsible: pcData?.userResponsible || "",
        PersonResponsible: pcData?.personResponsible || "",
        Department: pcData?.Department || "",
        PrctrHierGrp: pcData?.PrctrHierGrp || "",
        Segment: pcData?.segment || "",
        LockIndicator: pcData?.lockIndicator ? "X" : "",
        Template: "",
        Title: pcData?.addressTabDto?.Title || "",
        Name1: pcData?.name1 || "",
        Name2: pcData?.name2 || "",
        Name3: pcData?.name3 || "",
        Name4: pcData?.name4 || "",
        Street: pcData?.street || "",
        City: pcData?.city || "",
        District: pcData?.district || "",
        Country: pcData?.country || "",
        Taxjurcode: pcData?.TaxJurisdiction || "",
        PoBox: pcData?.PoBox || "",
        PostalCode: pcData?.pocode || pcData?.PostalCode || "",
        PobxPcd: pcData?.PobxPcd || "",
        Regio: pcData?.region || "",
        Language: pcData?.communicationTabDto?.Language || "",
        Telephone: pcData?.communicationTabDto?.Telephone || "",
        Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
        Telebox: pcData?.communicationTabDto?.Telebox || "",
        Telex: pcData?.communicationTabDto?.Telex || "",
        FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
        Teletex: pcData?.communicationTabDto?.Teletex || "",
        Printer: pcData?.communicationTabDto?.Printer || "",
        DataLine: pcData?.communicationTabDto?.DataLine || "",
        ProfitCenter: pcData?.profitCenter || "",
        COArea: pcData?.controllingArea || "ETCA",
        ValidfromDate:
          pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
        ValidtoDate:
          pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
        Testrun: null,
        IsFirstSynCompleted: false,
        TempLockIsSelectedForSyn: false,
        SelectedByRequestorToDisplay: true,
        IsSunoco: false,
        Countryiso: "",
        LanguIso: "",
        Logsystem: "",
        GeneralInfoID: null,
        RequestPriority: requestHeaderData?.requestPriority || "",
        BusinessJustification: null,
        SAPorJEErrorCheck: null,
        BusinessSegment: pcData.businessSegment || "CRUDE",
        HierarchyRegion: null,
        PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
        ValidationDoneBy: "",
        TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

        ToChildHeaderdata: {
          RequestId: pcData.RequestID ?? "",
          Status: requestHeaderData?.RequestStatus || "",
          IntermediateTaskCount: null,
          TotalTaskCount: null,
          BifurcatedValue: "",
          TaskId: null,
          CreatedOn: "2025-06-05T06:34:55.995+00:00",
          UpdatedOn: "2025-06-05T06:34:58.653+00:00",
          RequestType: "Mass Change",

          CurrentLevel: task?.ATTRIBUTE_3 || 0,
          CurrentLevelName: task?.ATTRIBUTE_4 || "",
          IsScheduled: "",
          ParticularLevel: "",
          TaskCreatedOn: "",
          TaskName: "",
        },

        ToCompanycode: [
          {
            CompCodeID: pcData.CompCodeID,
            CompanyCode: pcData.companyCode || "",
            CompanyName: "",
            AssignToPrctr: "",
            Venture: "",
            RecInd: "",
            EquityTyp: "",
            JvOtype: "",
            JvJibcl: "",
            JvJibsa: "",
          },
        ],

        Torequestheaderdata: {
          RequestId: requestHeaderData?.RequestId || "",
          ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
          ReqCreatedOn:
            requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
          ReqUpdatedOn:
            requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
          RequestType: requestHeaderData?.RequestType || "",
          RequestPrefix: requestHeaderData?.RequestPrefix || "",
          RequestPriority: requestHeaderData?.RequestPriority || "",
          RequestDesc: requestHeaderData?.RequestDesc || "",
          RequestStatus: "Draft",
          FirstProd: "",
          LaunchDate: "",
          LeadingCat: "Anesthesia/Pain Management",
          Division: "00",
          TemplateName: "",
          FieldName: "",
          Region: "",
          FilterDetails: null,
          IsBifurcated: null,
        },

        TochangeLogData: {
          ChangeLogData: null,
          ChangeLogId: pcData.ChangeLogId || "",
          RequestHeaderId: "",
          RequestId: "",
        },

        ToProfitCenterErrorData: {
          ProfitCenterErrorId: pcData.ProfitCenterErrorID || "",
          RequestId: pcData.RequestID || "",
          ProfitCenter: pcData?.profitCenter || "",
          CompanyCode: pcData?.companyCode || "",
          Segment: pcData?.basicDataTabDto?.Segment || "",
          PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
          ControllingArea: pcData?.controllingArea || "",
          SapMessage: "",
          ObjectSapError: "",
          ObjectDbError: "",
          ObjectExcelError: "",
          ShortDescSapError: "",
          ShortDescDbError: "",
          ShortDescExcelError: "",
          LongDescSapError: "",
          LongDescDbError: "",
          LongDescExcelError: "",
          AddrValidationError: "",
          PersonResponsibleError: "",
          DmsAttachmentErrorStatus: "",
          SapAttachmentErrorStatus: "",
          RemovalNodeErrorStatus: "",
          AddNodeErrorStatus: "",
        },

        ToGeneralInfoData: {
          GeneralInfoId: pcData.GeneralInfoId || "",
          RequestPriority: "",
          BusinessJustification: "",
          SAPorJEErrorCheck: "",
          BusinessSegment: pcData.businessSegment || "CRUDE",
          Region: "",
        },
      };
    });

  
    const hSuccess = (data) => {
      setBlurLoading(false);
      setAlertType("success");
      setAlertMsg("Profit Centers successfuly Approved");
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      console.log("Errordata", error);
      setBlurLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/changeProfitCentersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const handleValidateAndSyndicate = (type) => {
    setBlurLoading(true);
    const Payload = fetchReqBenchData.map((pcData) => {
      return {
        requestInProcess: "",
        ProfitCenterID: pcData.ProfitCenterID || "",
        TemplateName: requestHeaderData?.TemplateName || "",
        TemplateHeaders: "",
        IsScheduled: false,
        Action: "",
        RequestID: pcData.RequestID || "",
        TaskStatus: null,
        TaskId: task?.taskId,
        ReqCreatedBy: pcData?.createdBy || "",
        ReqCreatedOn: pcData?.historyTabDto?.ReqCreatedOn || "",
        RequestStatus: requestHeaderData?.requestStatus || "",
        CreationId: "",
        EditId: "",
        DeleteId: "",
        MassCreationId: "",
        MassEditId: requestId || "",
        MassDeleteId: "",
        ProfitCenterErrorID: pcData.ProfitCenterErrorID || "",
        RequestType: requestHeaderData?.requestType || "",
        MassRequestStatus: "",
        Remarks: null,
        TempLockRemarks: null,
        Info: "",
        ChangedFields: "",
        ProfitCenterName: pcData?.profitCenterName || "",
        Description: pcData?.description || "",
        userResponsible: pcData?.userResponsible || "",
        PersonResponsible: pcData?.personResponsible || "",
        Department: pcData?.Department || "",
        PrctrHierGrp: pcData?.PrctrHierGrp || "",
        Segment: pcData?.segment || "",
        LockIndicator: pcData?.lockIndicator ? "X" : "",
        Template: "",
        Title: pcData?.addressTabDto?.Title || "",
        Name1: pcData?.name1 || "",
        Name2: pcData?.name2 || "",
        Name3: pcData?.name3 || "",
        Name4: pcData?.name4 || "",
        Street: pcData?.street || "",
        City: pcData?.city || "",
        District: pcData?.district || "",
        Country: pcData?.country || "",
        Taxjurcode: pcData?.TaxJurisdiction || "",
        PoBox: pcData?.PoBox || "",
        PostalCode: pcData?.pocode || pcData?.PostalCode || "",
        PobxPcd: pcData?.PobxPcd || "",
        Regio: pcData?.region || "",
        Language: pcData?.communicationTabDto?.Language || "",
        Telephone: pcData?.communicationTabDto?.Telephone || "",
        Telephone2: pcData?.communicationTabDto?.Telephone2 || "",
        Telebox: pcData?.communicationTabDto?.Telebox || "",
        Telex: pcData?.communicationTabDto?.Telex || "",
        FaxNumber: pcData?.communicationTabDto?.FaxNumber || "",
        Teletex: pcData?.communicationTabDto?.Teletex || "",
        Printer: pcData?.communicationTabDto?.Printer || "",
        DataLine: pcData?.communicationTabDto?.DataLine || "",
        ProfitCenter: pcData?.profitCenter || "",
        COArea: pcData?.controllingArea || "ETCA",
        ValidfromDate:
          pcData?.basicDataTabDto?.ValidfromDate || "/Date(-2208988800000)/",
        ValidtoDate:
          pcData?.basicDataTabDto?.ValidtoDate || "/Date(253402214400000)/",
        Testrun: null,
        IsFirstSynCompleted: false,
        TempLockIsSelectedForSyn: false,
        SelectedByRequestorToDisplay: true,
        IsSunoco: false,
        Countryiso: "",
        LanguIso: "",
        Logsystem: "",
        GeneralInfoID: null,
        RequestPriority: requestHeaderData?.requestPriority || "",
        BusinessJustification: null,
        SAPorJEErrorCheck: null,
        BusinessSegment: pcData.businessSegment || "CRUDE",
        HierarchyRegion: null,
        PCAAMNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
        ValidationDoneBy: "",
        TotalIntermediateTasks: pcData?.totalIntermediateTasks ?? "",

        ToChildHeaderdata: {
          RequestId: pcData.RequestID ?? "",
          Status: requestHeaderData?.RequestStatus || "",
          IntermediateTaskCount: null,
          TotalTaskCount: null,
          BifurcatedValue: "",
          TaskId: null,
          CreatedOn: "2025-06-05T06:34:55.995+00:00",
          UpdatedOn: "2025-06-05T06:34:58.653+00:00",
          RequestType: "Mass Change",

          CurrentLevel: task?.ATTRIBUTE_3 || 0,
          CurrentLevelName: task?.ATTRIBUTE_4 || "",
          IsScheduled: "",
          ParticularLevel: "",
          TaskCreatedOn: "",
          TaskName: "",
        },

        ToCompanycode: [
          {
            CompCodeID: pcData.CompCodeID,
            CompanyCode: pcData.companyCode || "",
            CompanyName: "",
            AssignToPrctr: "",
            Venture: "",
            RecInd: "",
            EquityTyp: "",
            JvOtype: "",
            JvJibcl: "",
            JvJibsa: "",
          },
        ],

        Torequestheaderdata: {
          RequestId: requestHeaderData?.RequestId || "",
          ReqCreatedBy: requestHeaderData?.ReqCreatedBy || "",
          ReqCreatedOn:
            requestHeaderData?.ReqCreatedOn || "/Date(1743998564967)/",
          ReqUpdatedOn:
            requestHeaderData?.ReqUpdatedOn || "/Date(1743998564967)/",
          RequestType: requestHeaderData?.RequestType || "",
          RequestPrefix: requestHeaderData?.RequestPrefix || "",
          RequestPriority: requestHeaderData?.RequestPriority || "",
          RequestDesc: requestHeaderData?.RequestDesc || "",
          RequestStatus: "Draft",
          FirstProd: "",
          LaunchDate: "",
          LeadingCat: "Anesthesia/Pain Management",
          Division: "00",
          TemplateName: "",
          FieldName: "",
          Region: "",
          FilterDetails: null,
          IsBifurcated: null,
        },

        TochangeLogData: {
          ChangeLogData: null,
          ChangeLogId: pcData.ChangeLogId || "",
          RequestHeaderId: "",
          RequestId: "",
        },

        ToProfitCenterErrorData: {
          ProfitCenterErrorId: pcData.ProfitCenterErrorID || "",
          RequestId: pcData.RequestID || "",
          ProfitCenter: pcData?.profitCenter || "",
          CompanyCode: pcData?.companyCode || "",
          Segment: pcData?.basicDataTabDto?.Segment || "",
          PcAamNumber: pcData?.basicDataTabDto?.PCAAMNumber || "",
          ControllingArea: pcData?.controllingArea || "",
          SapMessage: "",
          ObjectSapError: "",
          ObjectDbError: "",
          ObjectExcelError: "",
          ShortDescSapError: "",
          ShortDescDbError: "",
          ShortDescExcelError: "",
          LongDescSapError: "",
          LongDescDbError: "",
          LongDescExcelError: "",
          AddrValidationError: "",
          PersonResponsibleError: "",
          DmsAttachmentErrorStatus: "",
          SapAttachmentErrorStatus: "",
          RemovalNodeErrorStatus: "",
          AddNodeErrorStatus: "",
        },

        ToGeneralInfoData: {
          GeneralInfoId: pcData.GeneralInfoId || "",
          RequestPriority: "",
          BusinessJustification: "",
          SAPorJEErrorCheck: "",
          BusinessSegment: pcData.businessSegment || "CRUDE",
          Region: "",
        },
      };
    });

    
    const hSuccess = (data) => {
      setBlurLoading(false);
      setAlertType("success");
      setAlertMsg("Profit Centers submit for review successful");
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      console.log("Errordata", error);
      setBlurLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      type === "VALIDATE"
        ? `/${destination_ProfitCenter_Mass}/massAction/validateMassProfitCenter`
        : `/${destination_ProfitCenter_Mass}/massAction/changeProfitCentersApproved`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleDownloadDialogOpen = () => {
    setOpenDownloadDialog(true);
  };

  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  const handleDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    setOpen(false);
    setDownloadClicked(false);
    if (!RequestId) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
    let payload = {
      coAreaPCs: selectedProfitCenters.map((pc) => ({
        profitCenter: pc,
        controllingArea: dropdown1Value,
      })),
      requestId:
        requestHeaderData?.RequestId || initialPayload?.requestId || "",
      templateHeaders: requestHeaderData?.FieldName
        ? requestHeaderData.FieldName?.join("$^$")
        : "",
      templateName: requestHeaderData?.TemplateName
        ? requestHeaderData.TemplateName
        : "",
      dtName: "MDG_CHANGE_TEMPLATE_DT",
      version: "v6",
    };
    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        showToast(ERROR_MESSAGES?.NO_DATA_FOUND, "error", {
          position: "top-center",
          largeWidth: true,
        });
        setTimeout(() => {
          navigate(APP_END_POINTS?.REQUEST_BENCH);
        }, 2600);
        return;
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute(
        "download",
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setMessageDialogMessage(
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx has been downloaded successfully.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showToast(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error", {
        position: "top-center",
      });
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const handleEmailDownload = () => {
    setBlurLoading(true);
    onClose();
    let templateKeys =
      Templates[initialPayload?.TemplateName]?.map((item) => item.key) || [];
    let payload = {};
    if (activeTab === 0) {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] = convertedValues?.[key] ? convertedValues?.[key] : "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    } else {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] =
              rowsOfMaterialData
                .map((row) => row[key]?.trim())
                .filter((value) => value !== "")
                .join(",") || "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    }
    const hSuccess = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setSuccessMsg(true);
      setMessageDialogMessage(
        "Oops! Something went wrong. Please try again later."
      );
      setAlertType("danger");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_MaterialMgmt}/excel/downloadExcelWithDataInMail`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const highlightedColumns = columns.map((col) => ({
    ...col,
    renderCell: (params) => {
      const isChanged =
        changedFieldsMap[params.row.ProfitCenterID]?.[col.field];

      return (
        <div
          style={{
            backgroundColor: isChanged ? "rgba(255, 229, 100, 0.6)" : "inherit",
            padding: "0 4px",
            borderRadius: 4,
            height: "100%",
            display: "flex",
            alignItems: "center",
          }}
        >
          {params.value}
        </div>
      );
    },
  }));

  const isChangeFieldEmpty = (changedFieldsMap) =>
    changedFieldsMap &&
    Object.values(changedFieldsMap).every(
      (fields) =>
        typeof fields === "object" &&
        fields !== null &&
        Object.keys(fields).length === 0
    );

  const isEmpty = isChangeFieldEmpty(changedFieldsMap);
  const filteredProfitCenters = profitCenterOptions
    .filter((pc) => selectedCompanyCodes.includes(pc.companyCode))
    .map((pc) => ({ code: pc.code, desc: pc.desc }));

  return (
    <div>
      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          alertType={alertType}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
      {showTableInDialog && (
        <ObjectLockDialog
          duplicateFieldsArr={duplicateFieldsData}
          moduleName={MODULE_MAP?.["PC"]}
          open={showTableInDialog}
          onClose={() => setShowTableInDialog(false)}
        />
      )}
      {(requestHeaderData?.TemplateName || downloadClicked) && (
        <>
          {!reqBench && fetchedProfitCenterData.length === 0 && (
            <>
              <Dialog
                open={open}
                // TransitionComponent={Transition}
                // keepMounted
                // onClose={(event, reason) => {
                //   if (
                //     reason === "backdropClick" ||
                //     reason === "escapeKeyDown"
                //   ) {
                //     return;
                //   }
                //   handleClose();
                // }}
                onClose={handleClose}
                maxWidth="sm"
                fullWidth
              >
                <Box
                  sx={{
                    backgroundColor: "#e3f2fd",
                    padding: "1rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <FeedOutlinedIcon
                    color="primary"
                    sx={{ marginRight: "0.5rem" }}
                  />
                  <Typography variant="h6" component="div" color="primary">
                    {requestHeaderData?.TemplateName} {t("Search Filter")}(s)
                  </Typography>
                </Box>

                <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{
                        key: "controllingArea",
                        label: t("Controlling Area"),
                      }}
                      dropDownData={{
                        controllingArea: [{ code: "ETCA", desc: "ETCA" }],
                      }}
                      selectedValues={{
                        controllingArea: dropdown1Value
                          ? [{ code: dropdown1Value }]
                          : [],
                      }}
                      handleSelectionChange={(key, value) => {
                        setDropdown1Value(
                          value.length > 0 ? value[0].code || value[0] : ""
                        );
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      errors={{}}
                    />
                  </Box>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "companyCode", label: t("Company Code") }}
                      dropDownData={{ companyCode: dropdownDataCompany || [] }}
                      selectedValues={{
                        companyCode: selectedCompanyCodes.map(
                          (code) =>
                            dropdownDataCompany.find(
                              (item) => item.code === code
                            ) || {
                              code,
                            }
                        ),
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCompanyCodes.length ===
                          dropdownDataCompany?.length
                        ) {
                          setSelectedCompanyCodes([]);
                        } else {
                          setSelectedCompanyCodes(
                            dropdownDataCompany?.map((item) => item.code) || []
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        const newCompanyCodes = value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        );
                        setSelectedCompanyCodes(newCompanyCodes);
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>

                  {/* Profit Center Dropdown */}
                  {/* Profit Center Dropdown */}
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "profitCenter", label: t("Profit Center") }}
                      dropDownData={{ profitCenter: filteredProfitCenters }}
                      selectedValues={{
                        profitCenter: selectedProfitCenters.map(
                          (code) =>
                            filteredProfitCenters.find(
                              (pc) => pc.code === code
                            ) || { code }
                        ),
                      }}
                      handleSelectAll={(key) => {
                        const filteredPCs = profitCenterOptions.filter((pc) =>
                          selectedCompanyCodes.includes(pc.companyCode)
                        );
                        if (
                          selectedProfitCenters.length ===
                          filteredProfitCenters.length
                        ) {
                          setSelectedProfitCenters([]);
                        } else {
                          setSelectedProfitCenters(
                            filteredProfitCenters.map((pc) => pc.code)
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        const pcCodes = value.map((item) =>
                          typeof item === "string" ? item : item.code
                        );
                        setSelectedProfitCenters(pcCodes);
                      }}
                      formatOptionLabel={(option) =>
                        typeof option === "string"
                          ? option
                          : `${option.code}${
                              option.desc ? ` - ${option.desc}` : ""
                            }`
                         
                      }
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>
                </DialogContent>

                <DialogActions
                  sx={{
                    padding: "0.5rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                      onClick={handleClose}
                      color="error"
                      variant="outlined"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        textTransform: "none",
                        borderColor: "#cc3300",
                        fontWeight: 500,
                      }}
                    >
                      {t("Cancel")}
                    </Button>
                    {requestHeaderData?.RequestType !==
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={() => {
                          handleOk("OK");
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        {t("OK")}
                      </Button>
                    )}
                    {requestHeaderData?.RequestType ===
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        // handleDownloadDialogOpen();
                        onClick={() => {
                          handleOk("Download");
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        {t("Download")}
                      </Button>
                    )}
                  </Box>
                </DialogActions>
              </Dialog>

              <DownloadDialog
                onDownloadTypeChange={onDownloadTypeChange}
                open={openDownloadDialog}
                downloadType={downloadType}
                handleDownloadTypeChange={handleDownloadTypeChange}
                onClose={handleDownloadDialogClose}
              />
              <ReusableBackDrop
                blurLoading={blurLoading}
                loaderMessage={loaderMessage}
              />
            </>
          )}
          {showGrid && (
            <Box sx={{ mt: 4, px: 4 }}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  mb: 2,
                }}
              >
                <Typography variant="h5" sx={{ fontWeight: 600 }}>
                  {t("Profit Center List")}
                </Typography>
                {/* <Button
                  variant="contained"
                  color="secondary"
                  // onClick={handleCreateNew}
                >
                  {t("+ Add Row")}
                </Button> */}
              </Box>

              <Paper
                elevation={3}
                sx={{
                  borderRadius: 3,
                  overflow: "hidden",
                  border: "1px solid #e0e0e0",
                  backgroundColor: "#fafbff",
                }}
              >
                <Box sx={{ p: 2 }}>
                  <ReusableDataTable
                    rows={fetchedProfitCenterData}
                    columns={columns}
                    rowHeight={70}
                    pageSize={10}
                    tempheight="50vh"
                    getRowIdValue="id"
                    editMode="row"
                    status_onRowSingleClick
                    callback_onRowSingleClick={handleRowClick}
                    processRowUpdate={processRowUpdate}
                    experimentalFeatures={{ newEditingApi: true }}
                    isCellEditable={(params) =>
                      ![
                        "profitCenter",
                        "companyCode",
                        "controllingArea",
                      ].includes(params.field)
                    }
                    getRowClassName={(params) =>
                      selectedRow?.id === params.row.id ? "Mui-selected" : ""
                    }
                  />
                </Box>
              </Paper>

              <Box
                sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSaveAsDraft}
                >
                  {t("Save as draft")}
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={validateAllRows}
                >
                  {t("Validate")}
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={handleSubmitForReview}
                  disabled={!isValidated}
                >
                  {t("Submit")}
                </Button>
              </Box>
            </Box>
          )}
        </>
      )}
      <>
        {fetchReqBenchData.length === 1 &&
          fetchReqBenchData[0]?.profitCenter === null &&
          reqBench === "true" && (
            <>
              <Dialog
                open={open}
                TransitionComponent={Transition}
                keepMounted
                onClose={(event, reason) => {
                  if (
                    reason === "backdropClick" ||
                    reason === "escapeKeyDown"
                  ) {
                    return;
                  }
                  handleClose();
                }}
                maxWidth="sm"
                fullWidth
              >
                <Box
                  sx={{
                    backgroundColor: "#e3f2fd",
                    padding: "1rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <FeedOutlinedIcon
                    color="primary"
                    sx={{ marginRight: "0.5rem" }}
                  />
                  <Typography variant="h6" component="div" color="primary">
                    {requestHeaderData?.TemplateName} {t("Search Filter")}(s)
                  </Typography>
                </Box>

                <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{
                        key: "controllingArea",
                        label: "Controlling Area",
                      }}
                      dropDownData={{
                        controllingArea: [{ code: "ETCA", desc: "ETCA" }],
                      }}
                      selectedValues={{
                        controllingArea: dropdown1Value
                          ? [{ code: dropdown1Value }]
                          : [],
                      }}
                      handleSelectionChange={(key, value) => {
                        setDropdown1Value(
                          value.length > 0 ? value[0].code || value[0] : ""
                        );
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      errors={{}}
                    />
                  </Box>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "companyCode", label: t("Company Code") }}
                      dropDownData={{ companyCode: dropdownDataCompany || [] }}
                      selectedValues={{
                        companyCode: selectedCompanyCodes.map(
                          (code) =>
                            dropdownDataCompany.find(
                              (item) => item.code === code
                            ) || {
                              code,
                            }
                        ),
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCompanyCodes.length ===
                          dropdownDataCompany?.length
                        ) {
                          setSelectedCompanyCodes([]);
                        } else {
                          setSelectedCompanyCodes(
                            dropdownDataCompany?.map((item) => item.code) || []
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        const newCompanyCodes = value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        );
                        setSelectedCompanyCodes(newCompanyCodes);
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>

                  {/* Profit Center Dropdown */}
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "profitCenter", label: t("Profit Center") }}
                      dropDownData={{
                        profitCenter: profitCenterOptions
                          .filter((pc) =>
                            selectedCompanyCodes.includes(pc.companyCode)
                          )
                          .map((pc) => ({
                            code: pc.code,
                            desc: pc.desc,
                          })),
                      }}
                      selectedValues={{
                        profitCenter: selectedProfitCenters
                          .filter((code) =>
                            profitCenterOptions.some(
                              (pc) =>
                                pc.code === code &&
                                selectedCompanyCodes.includes(pc.companyCode)
                            )
                          )
                          .map((code) => ({ code })),
                      }}
                      handleSelectAll={(key) => {
                        const filteredPCs = profitCenterOptions.filter((pc) =>
                          selectedCompanyCodes.includes(pc.companyCode)
                        );
                        if (
                          selectedProfitCenters.length === filteredPCs.length
                        ) {
                          setSelectedProfitCenters([]);
                        } else {
                          setSelectedProfitCenters(
                            filteredPCs.map((pc) => pc.code)
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        const pcCodes = value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        );
                        setSelectedProfitCenters(pcCodes);
                      }}
                      formatOptionLabel={(option) =>
                        typeof option === "string"
                          ? option
                          : `${option.code}${
                              option.desc ? ` - ${option.desc}` : ""
                            }`
                      }
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>
                </DialogContent>

                <DialogActions
                  sx={{
                    padding: "0.5rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                      onClick={handleClose}
                      color="error"
                      variant="outlined"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        textTransform: "none",
                        borderColor: "#cc3300",
                        fontWeight: 500,
                      }}
                    >
                      {t("Cancel")}
                    </Button>
                    {requestHeaderData?.RequestType !==
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={handleOk}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        {t("OK")}
                      </Button>
                    )}
                    {requestHeaderData?.RequestType ===
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={() => {
                          handleDownloadDialogOpen();
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        {t("Download")}
                      </Button>
                    )}
                  </Box>
                </DialogActions>
              </Dialog>

              <DownloadDialog
                onDownloadTypeChange={onDownloadTypeChange}
                open={openDownloadDialog}
                downloadType={downloadType}
                handleDownloadTypeChange={handleDownloadTypeChange}
                onClose={handleDownloadDialogClose}
              />
              <ReusableBackDrop
                blurLoading={blurLoading}
                loaderMessage={loaderMessage}
              />
            </>
          )}
        {reqBench === "true" && (
          <Box sx={{ marginTop: "20px", padding: "16px" }}>
            <Typography variant="h5" gutterBottom>
              {t("Profit Center Lists")}
            </Typography>
            <Paper
              elevation={4}
              sx={{ p: 0, borderRadius: 2, overflow: "hidden", mt: "50px" }}
            >
              <div>
                <ReusableDataTable
                  // isLoading={loading}
                  rows={fetchReqBenchData}
                  columns={columns}
                  pageSize={10}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  editMode="cell"
                  callback_onRowSingleClick={handleRowClick}
                  processRowUpdate={processRowUpdateReqBench}
                  experimentalFeatures={{ newEditingApi: true }}
                  isCellEditable={(params) =>
                    ![
                      "profitCenter",
                      "companyCode",
                      "controllingArea",
                    ].includes(params.field)
                  }
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
            </Paper>
            <Box
              sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
            >
              <BottomNavGlobal
                handleSaveAsDraft={handleSaveAsDraft}
                handleSubmitForReview={handleSubmitForReview}
                handleSubmitForApprove={handleSubmitForApprove}
                handleSendBack={handleSendBack}
                // handleCorrection={handleCorrection}
                handleRejectAndCancel={handleRejectAndCancel}
                handleValidateAndSyndicate={handleValidateAndSyndicate}
                validateAllRows={validateAllRows}
                // isSaveAsDraftEnabled={isSaveAsDraftEnabled}
                filteredButtons={filteredButtons}
                moduleName={module}
              />
            </Box>
          </Box>
        )}
      </>
    </div>
  );
};

export default RequestDetailsChangePC;
