import React, { useRef, useState } from "react";
import { BottomNavi<PERSON>, <PERSON>, Button, Dialog, <PERSON>alogActions, DialogContent, DialogTitle, FormControl, FormHelperText, IconButton, Paper, Stack, styled, TextField, Tooltip, tooltipClasses, Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { doAjax } from "@components/Common/fetchService";
import { destination_Admin, destination_MaterialMgmt } from "../../destinationVariables";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import { useLocation, useNavigate } from "react-router-dom";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import CloseIcon from "@mui/icons-material/Close";
import { button_Primary } from "@components/common/commonStyles";
import { API_CODE, BUTTON_NAME, DIALOUGE_BOX_MESSAGES,  ENABLE_STATUSES, REQUEST_STATUS, REQUEST_TYPE, VISIBILITY_TYPE, LOADER_MESSAGES, LOCAL_STORAGE_KEYS, MODULE_MAP } from "@constant/enum";
import { convertToDateFormat, getLocalStorage } from "@helper/helper";
import ReusableDialog from "@components/Common/ReusableDialog";
import { END_POINTS } from "@constant/apiEndPoints";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { resetPayloadData, setErrorData, setIsSubmitDisabled, setDynamicKeyValue } from "@app/payloadSlice";
import useChangePayloadCreation from "@hooks/useChangePayloadCreation";
import { v4 as uuidv4 } from "uuid";
import { TEMPLATE_KEYS } from "@constant/changeTemplates";
import useLogger from "@hooks/useLogger";
import MaterialConfirmDialog from "@components/RequestBench/RequestPages/MaterialConfirmDialog";
import useFinanceCostingRows from "@hooks/useFinanceCostingRows";
import { colors } from "@constant/colors";
import usePayloadCreation from "@hooks/usePayloadCreation";
import { useSnackbar } from "@hooks/useSnackbar";
import useLang from "@hooks/useLang";
import {TABS_NAME_VALUE} from "@constant/buttonPriority"
import ManageScheduling from "@components/RequestBench/ManageScheduling";

const BottomNav = (props) => {
  const payloadData = useSelector((state) => state.payload);
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const changeFieldRows = useSelector((state) => state.payload.changeFieldRows);
  const selectedRows = useSelector((state) => state.payload.selectedRows);
  const requestType = payloadData?.payloadData?.data ? payloadData?.payloadData?.data?.RequestType : payloadData?.payloadData?.RequestType;
  const [successMsg, setSuccessMsg] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [confirmationMessage, setConfirmationMessage] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [openRemarkDialog, setOpenRemarkDialog] = useState(false);
  const [remarks, setRemarks] = useState("");
  const [selectedLevel,setSelectedLevel] = useState('');
  const [isTable, setIsTable] = useState(false);
  const [tableRows, setTableRows] = useState([]);
  const [tableColumns, setTableColumns] = useState([]);
  const [showOkButton, setShowOkButton] = useState(false);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const { t } = useLang();

  const [remarksError, setRemarksError] = useState(false);
  const [isMandatory, setIsMandatory] = useState(false);
  const [userInput, setUserInput] = useState("");
  const [inputText, setTextInput] = useState(false);
  const [messageDialogSeverity, setMessageDialogSeverity] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [dialogTitle, setDialogTitle] = useState("");
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [currentButtonState, setCurrentButtonState] = useState({});
  const [currentActionType, setCurrentActionType] = useState("");
  const [buttonName, setButtoName] = useState("");
  let userData = useSelector((state) => state.userManagement.userData);
  let taskData = useSelector((state) => state.userManagement.taskData);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const rowData = location.state;
  const changePayloadData = useSelector((state) => state.payload.payloadData);
  const requestorPayload = useSelector((state) => state.payload.requestorPayload);
  const changeDataDT = useSelector((state) => state.tabsData.changeFieldsDT);
  const templateName = changePayloadData?.TemplateName || "";
  const { changePayloadForTemplate } = useChangePayloadCreation(templateName);
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]); // E.g., "#/requestBench/createRequest?RequestId=NMM20241123124625340"
  const requestId = urlSearchParams.get("RequestId");
  const isReqBench = urlSearchParams.get("reqBench");
  const taskDataString = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK);
  const localTask = typeof taskDataString === "string" ? JSON.parse(taskDataString) : taskDataString;
  const initialReqScreen = !Boolean(taskData?.taskId || localTask?.ATTRIBUTE_5) && !isReqBench;
  const [validated, setValidated] = useState(false);
  const { customError } = useLogger();
  const { createFCPayload } = useFinanceCostingRows();
  const {createPayloadFromReduxState} = usePayloadCreation({initialReqScreen,isReqBench,remarks,userInput,selectedLevel});
  const [isMaxLength, setIsMaxLength] = useState(false);
  const [isValidateCorrection,setIsValidateCorrection] = useState(false);
  const [isSyndicationBtn,setIsSyndicationBtn] = useState(false);
  const [syndicationType, setSyndicationType] = useState("");

  const isMassSyndication = (payloadData?.payloadData?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || payloadData?.payloadData?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD || payloadData?.payloadData?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD);
  const { showSnackbar } = useSnackbar();
  const selectedTabValue = useSelector((state) => state.request.tabValue);
  const maxLength = 200;
  const manageSchedulingRef = useRef();

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleConfirmDialogOpen = () => {
    setConfirmationDialogOpen(true);
  };

  const handleConfirmDialogClose = () => {
    setConfirmationDialogOpen(false);
  };

  const handleConfirmOk = () => {
    if (buttonName === BUTTON_NAME.SAVE) {
      handleConfirmDialogClose();
      // handleOpenRemarkDialog();
      handleBasicSubmit();
    } else if (buttonName === BUTTON_NAME?.VALIDATE) {
      handleConfirmDialogClose();
      handleValidate();
    }
  };

  const handleOpenRemarkDialog = () => {
    setOpenRemarkDialog(true);
  };
  const handleRemarksDialogClose = () => {
    setRemarks("");
    setOpenRemarkDialog(false);
  };
  const handleRemarks = (e, value) => {
    const newValue = e.target.value;
    setIsMaxLength(newValue.length >= maxLength);
    
    if (newValue.length > 0 && newValue[0] === " ") {
      setRemarks(newValue.trimStart());
      dispatch(
        setDynamicKeyValue({
          keyName: "Comments",
          data: newValue.trimStart(),
        })
      );
    } else {
      let remarksUpperCase = newValue;
      setRemarks(remarksUpperCase);
      dispatch(
        setDynamicKeyValue({
          keyName: "Comments",
          data: remarksUpperCase,
        })
      );
    }
  };
  const handleLevelChange = (event) =>{
    setSelectedLevel(event.target.value);
    dispatch(
      setDynamicKeyValue({
        keyName: "Level",
        data: event.target.value,
      })
    );
  }

  const handleBasicSubmit = () => {
    handleRemarksDialogClose();
    setBlurLoading(true);
    var submitForApprovalBasicData;
    if (payloadData?.payloadData?.RequestType === REQUEST_TYPE.CREATE||payloadData?.payloadData?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
      if (buttonName === BUTTON_NAME.SAVE) {
        submitForApprovalBasicData = `/${destination_MaterialMgmt}/massAction/createMaterialSaveAsDraft`;
      } else {
        submitForApprovalBasicData = userData?.role === "Approver" ? `/${destination_MaterialMgmt}/massAction/createBasicMaterialsApproved` : `/${destination_MaterialMgmt}/massAction/createMaterialSubmitForReview`;
      }
    }
    else if (payloadData?.payloadData?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) {
      if (buttonName === BUTTON_NAME.SAVE) {
        submitForApprovalBasicData = `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`;
      } else {
        submitForApprovalBasicData = `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.EXTEND_MATERIAL_DIRECT_APPROVED}`;
      }
    } else if (requestType === REQUEST_TYPE.CHANGE || requestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) {
      if (buttonName === BUTTON_NAME.SAVE) {
        submitForApprovalBasicData = `/${destination_MaterialMgmt}/massAction/changeMaterialSaveAsDraft`;
      }
      else {
        submitForApprovalBasicData = userData?.role === "Approver" ? `/${destination_MaterialMgmt}/massAction/changeBasicMaterialsApproved` : `/${destination_MaterialMgmt}/massAction/changeMaterialSubmitForReview`;
      }
      // else {
      //   if (changePayloadData?.TemplateName === TEMPLATE_KEYS?.MRP || changePayloadData?.TemplateName === TEMPLATE_KEYS?.ITEM_CAT || changePayloadData?.TemplateName === TEMPLATE_KEYS?.WARE_VIEW_2) {
      //     submitForApprovalBasicData = `/${destination_MaterialMgmt}/massAction/changeMaterialDirectApproved`;
      //   } else {
      //     submitForApprovalBasicData = userData?.role === "Approver" ? `/${destination_MaterialMgmt}/massAction/changeBasicMaterialsApproved` : `/${destination_MaterialMgmt}/massAction/changeMaterialSubmitForReview`;
      //   }
      // }
    }

    const hSuccess = (data) => {
      if (data.statusCode >= API_CODE.STATUS_200 && data.statusCode < API_CODE.STATUS_300) {
        setBlurLoading(false);
        let message;
        if (userData?.role === "Approver") {
          message = `Material Syndicated successfully in SAP with Material ID : ${data?.body.join(", ")}`;
        } else if (buttonName === BUTTON_NAME.SAVE) {
          message = data?.message;
        } else {
          message = `Request Submitted for Approval with Request ID ${data?.body}`;
        }
        showSnackbar(message,'success')
        handleSnackBarOpen();
          navigate("/masterDataCockpit/materialMaster/material");
     
      } else {
        setBlurLoading(false);
        showSnackbar(data?.message,"error")
      }
      setButtoName("");
    };
    const hError = (error) => {
      showSnackbar(error?.message,'error')
      setBlurLoading(false);
      setButtoName("");
    };

    var payload;
    payload =
      payloadData?.payloadData?.RequestType === REQUEST_TYPE.CREATE || payloadData?.payloadData?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || payloadData?.payloadData?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? createPayloadFromReduxState(payloadData)
        : payloadData?.payloadData?.RequestType === REQUEST_TYPE.CHANGE
        ? isReqBench
          ? changePayloadForTemplate(true)
          : changePayloadForTemplate(false)
        : payloadData?.payloadData?.data?.RequestType || payloadData?.payloadData?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
        ? changePayloadForTemplate(true)
        : payloadData?.payloadData?.data?.RequestType || payloadData?.payloadData?.RequestType === REQUEST_TYPE.CHANGE
        ? createPayloadFromReduxState(payloadData)
        : [];
    doAjax(submitForApprovalBasicData, "post", hSuccess, hError, payload);
  };
  const handleButtonAction = async (button) => {
    if ((button?.MDG_MAT_DYN_BTN_ACTION_TYPE === "handleValidate" || button?.MDG_MAT_DYN_BTN_ACTION_TYPE === "handleValidate1") && (payloadData?.payloadData?.RequestType === REQUEST_TYPE.CREATE || payloadData?.payloadData?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD)) {
      try {
        const result = await props.validateMaterials();
        setValidated(result);
      } catch (error) {
        customError(error);
        return;
      }
    }
    setMessageDialogMessage("");
    setMessageDialogSeverity("success");
    setCurrentButtonState(button);

    setDialogTitle(button.MDG_MAT_DYN_BTN_COMMENT_BOX_NAME);

    setIsMandatory(button.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT === VISIBILITY_TYPE?.MANDATORY);

    setTextInput(button.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT === VISIBILITY_TYPE?.MANDATORY || button.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT === "Optional" ? true : false);
    // setMessageDialogMessage(button.MDG_DYN_BTN_SNACKBAR_MSG);
    setCurrentActionType(button.MDG_MAT_DYN_BTN_ACTION_TYPE);
    if(button.MDG_MAT_DYN_BTN_BUTTON_NAME === BUTTON_NAME.SEND_BACK || button.MDG_MAT_DYN_BTN_BUTTON_NAME === BUTTON_NAME.CORRECTION){
      setIsValidateCorrection(true);
    }
    else{
      setIsValidateCorrection(false);
    }
    if(button.MDG_MAT_DYN_BTN_BUTTON_NAME === BUTTON_NAME.SAP_SYNDICATE){
      setIsSyndicationBtn(true);
    }
    else{
      setIsSyndicationBtn(false);
    }
    if (button.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT === VISIBILITY_TYPE?.MANDATORY || button.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT === "Optional") {
      handleMessageDialogClickOpen();
    } else {
      executeAction(button.MDG_MAT_DYN_BTN_ACTION_TYPE, button);
    }
  };

  const handleIntermediateApprove = () => {
    handleRemarksDialogClose();
    setBlurLoading(true);
    const submitForApprovalBasicData = (requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? `/${destination_MaterialMgmt}/massAction/createMaterialApprovalSubmit` : requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? `/${destination_MaterialMgmt}/massAction/extendMaterialApprovalSubmit` : `/${destination_MaterialMgmt}/massAction/changeMaterialApprovalSubmit`;
    const hSuccess = (data) => {
      if (data.statusCode >= 200 && data.statusCode < 300) {
        setBlurLoading(false);
        showSnackbar(`Request Submitted for Approval with Request ID ${data?.body}`,'success')
          navigate("/masterDataCockpit/materialMaster/material");
      } else {
        setBlurLoading(false);
        showSnackbar(data?.message,'error')
      }
    };
    const hError = () => {
      setBlurLoading(false);
      showSnackbar("Failed Submitting Request.","error")
    };

    var payload;
    payload = requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD|| requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ? createPayloadFromReduxState(payloadData) : changePayloadForTemplate(true);
    doAjax(submitForApprovalBasicData, "post", hSuccess, hError, payload);    
  };

  const handleSyndicate = (button) => {
    handleRemarksDialogClose();
    setBlurLoading(true);
    var submitForApprovalBasicData = (requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? `/${destination_MaterialMgmt}/massAction/createMaterialApproved` : requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? `/${destination_MaterialMgmt}/massAction/extendMaterialApproved` : taskData.ATTRIBUTE_2 === REQUEST_TYPE.FINANCE_COSTING ? `/${destination_MaterialMgmt}/${END_POINTS.MASS_ACTION.FINANCE_COSTING_APPROVED}` : `/${destination_MaterialMgmt}/massAction/changeMaterialApproved`;
    const hSuccess = (data) => {
      if (data.statusCode >= 200 && data.statusCode < 300) {
        setBlurLoading(false);
        showSnackbar(button?.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG,'success')
          navigate("/masterDataCockpit/materialMaster/material");

      } else {
        setBlurLoading(false);
        showSnackbar(button?.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,'error')
      }
    };
    const hError = (error) => {
      showSnackbar(error?.message || "Failed Submitting Request.",'error')
      setBlurLoading(false);
    };

    var payload;
    const financePayload = {
      requestId: payloadData?.payloadData?.RequestId,
      taskId: taskData?.taskId || "",
      taskName: taskData?.taskDesc || "",
      comments: remarks || userInput,
      creationDate: taskData?.createdOn ? convertToDateFormat(taskData?.createdOn) : null,
      dueDate: taskData?.criticalDeadline ? convertToDateFormat(taskData?.criticalDeadline) : null,
    };
    payload = requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? createPayloadFromReduxState(payloadData) : changePayloadForTemplate(true);
    //  if(checkRequiredFields(payload).isAllRequiredFieldsFilled){
    doAjax(submitForApprovalBasicData, "post", hSuccess, hError, taskData.ATTRIBUTE_2 === REQUEST_TYPE.FINANCE_COSTING ? financePayload : payload);
    //  }else{
  };

  const handleSendBack = () => {
    handleRemarksDialogClose();
    setBlurLoading(true);
    const url = (requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}` : requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}` : `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`;
    const payload = requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? createPayloadFromReduxState(payloadData) : changePayloadForTemplate(true);
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        showSnackbar(data.message,"success")
        dispatch(resetPayloadData({ data: {} }));
        navigate(APP_END_POINTS.MY_TASK);
      } else {
        showSnackbar(data.error,"error")
      }
      setBlurLoading(false);
    };
    const hError = (error) => {
      showSnackbar(error.error,"error")
      setBlurLoading(false);
    };
    doAjax(url, "post", hSuccess, hError, payload);
  };

  const handleCorrection = () => {
    handleRemarksDialogClose();
    setBlurLoading(true);
    const url = (requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}` : requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}` : `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`;
    const payload = requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? createPayloadFromReduxState(payloadData) : changePayloadForTemplate(true);
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        showSnackbar(data.message,"success")
        dispatch(resetPayloadData({ data: {} }));
        navigate(APP_END_POINTS.MY_TASK);
      } else {
        showSnackbar(data.error,"error")
      }
      setBlurLoading(false);
    };
    const hError = (error) => {
      showSnackbar(error.error,"error")
      setBlurLoading(false);
    };
    doAjax(url, "post", hSuccess, hError, payload);
  };

  const handleRejectAndCancel = () => {
    handleRemarksDialogClose();
    setBlurLoading(true);
    const url = (requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.CREATE_MATERIAL_REJECTION}` : requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.EXTEND_MATERIAL_REJECTION}` : `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.CHANGE_MATERIAL_REJECTION}`;
    const rejectPayload = requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? createPayloadFromReduxState(payloadData) : changePayloadForTemplate(true);
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        showSnackbar(data.message,"success")
        dispatch(resetPayloadData({ data: {} }));
        navigate(APP_END_POINTS.MY_TASK);
      } else {
        showSnackbar(data.error,"error")
      }
      setBlurLoading(false);
    };
    const hError = (error) => {
      showSnackbar(error.error,"error")
      setBlurLoading(false);
    };
    doAjax(url, "post", hSuccess, hError, rejectPayload);
  };

  const handleValidate = (button) => {
    setBlurLoading(true);
    const url = taskData?.ATTRIBUTE_2 === REQUEST_TYPE.FINANCE_COSTING ? `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.VALIDATE_FINANCE_COSTING}?requestId=${requestId?.slice(3)}` : `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.VALIDATE_MATERIAL}`;
    const validatePayload =
      taskData?.ATTRIBUTE_2 === REQUEST_TYPE.FINANCE_COSTING
        ? createFCPayload()
        : requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? createPayloadFromReduxState(payloadData)
        : requestType === REQUEST_TYPE.CHANGE || requestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
        ? isReqBench && requestId
          ? changePayloadForTemplate(true)
          : !isReqBench && !requestId
          ? changePayloadForTemplate(false)
          : !isReqBench && requestId
          ? changePayloadForTemplate(true)
          : []
        : [];
    const hSuccess = (data) => {
      setBlurLoading(false);
      if (data?.statusCode === API_CODE.STATUS_200) {
        showSnackbar(button?.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG || data?.message,"success")
        // if (templateName === TEMPLATE_KEYS?.MRP || templateName === TEMPLATE_KEYS?.ITEM_CAT || templateName === TEMPLATE_KEYS?.WARE_VIEW_2 || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) {
        if (((initialReqScreen || isReqBench) && (requestType === REQUEST_TYPE.CHANGE || requestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD)) || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD || requestType === REQUEST_TYPE.EXTEND) {
            navigate(APP_END_POINTS.REQUEST_BENCH);
          return;
        }
          navigate(APP_END_POINTS.MY_TASK);
      } else {
        showSnackbar(button?.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG || "Validation failed.","error")
      }
    };
    const hError = () => {
      showSnackbar(button?.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,"error")
      setBlurLoading(false);
    };
    doAjax(url, "post", hSuccess, hError, validatePayload);
  };

  const handleRequestorApprove = () => {
    handleRemarksDialogClose();
    setBlurLoading(true);
    const submitForApprovalBasicData = (requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? `/${destination_MaterialMgmt}/massAction/createMaterialApprovalSubmit` : requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? `/${destination_MaterialMgmt}/massAction/extendMaterialSubmitForReview` : `/${destination_MaterialMgmt}/massAction/changeMaterialApprovalSubmit`;

    const hSuccess = (data) => {
      if (data.statusCode === API_CODE.STATUS_200) {
        setBlurLoading(false);
        showSnackbar(`Request Submitted for Approval with Request ID ${data?.body}`,"success")
        handleSnackBarOpen();
          navigate("/masterDataCockpit/materialMaster/material");
      } else {
        showSnackbar(data?.message,"error")

      }
    };
    const hError = (error) => {
      showSnackbar(error?.error || "Failed Submitting Request.","error")
      setBlurLoading(false);
    };

    var payload;
    payload = requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? createPayloadFromReduxState(payloadData) : changePayloadForTemplate(true);
    doAjax(submitForApprovalBasicData, "post", hSuccess, hError, payload);
    setSuccessMsg(true);
    handleSnackBarOpen();
  };

  const handleDraft = () => {
    handleRemarksDialogClose();
    setBlurLoading(true);
    const draftUrl = `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`;
    const hSuccess = (data) => {
      if (data.statusCode === API_CODE.STATUS_200) {
        setBlurLoading(false);
        showSnackbar(data?.message,"success")
        navigate(APP_END_POINTS.REQUEST_BENCH);
      } else {
        setBlurLoading(false);
        showSnackbar(data?.message,"error")
      }
    };
    const hError = (error) => {
      showSnackbar(error?.error,"error")
      setBlurLoading(false);
    };

    let payload;
    payload = createPayloadFromReduxState(payloadData);
    doAjax(draftUrl, "post", hSuccess, hError, payload);
  };

  const executeAction = (actionType, button) => {
    switch (actionType) {
      case "handleSubmitForApproval":
        handleIntermediateApprove(button);
        break;
      case "handleSubmitForReview":
        handleRequestorApprove(button);
        break;
      case "handleSendBack":
        handleSendBack();
        break;
      case "handleCorrection":
        handleCorrection();
        break;
      case "handleReject":
        handleRejectAndCancel();
        break;
      case "Validate":
        handleValidateCombined(button);
        break;
      case "handleValidate":
        handleValidateCombined(button);
        break;
      case "handleSAPSyndication":
        handleSyndicate(button);
        break;
      case "handleDraft":
        handleDraft();
        break;
      case "handleSubmit":
          handleRequestorApprove(button);
        break;
      default:
        console.log("Unknown action type");
    }
  };

  const handleValidateCombined = (button) => {
    setButtoName(BUTTON_NAME?.VALIDATE);
    setConfirmationMessage(t(DIALOUGE_BOX_MESSAGES?.VALIDATE_MSG));
    if (requestType === REQUEST_TYPE.CREATE || requestType === REQUEST_TYPE.EXTEND || requestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || requestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD || taskData?.ATTRIBUTE_2 === REQUEST_TYPE.FINANCE_COSTING) {
      handleValidate(button);
    } else {
      handleMasterValidation(button);
    }
  };

  const handleMasterValidation = (button) => {
    if (Array.isArray(changeFieldRows)) {
      handleValidateChange(button);
    } else if (typeof changeFieldRows === "object") {
      handleValidateChangeMultiTable(button);
    }
  };

  const constructMandatoryFields = () => {
    const configData = changeDataDT?.["Config Data"];
    const mandatoryFieldsMap = {};

    Object.entries(configData).forEach(([key, fields]) => {
      const mandatoryFields = fields.filter((field) => field.visibility === VISIBILITY_TYPE?.MANDATORY).map((field) => ({ jsonName: field.jsonName, fieldName: field.fieldName }));

      if (!mandatoryFields?.some((field) => field.jsonName === "Material")) {
        const materialField = fields.find((field) => field.jsonName === "Material");
        if (materialField) {
          mandatoryFields.push({ jsonName: materialField.jsonName, fieldName: materialField.fieldName });
        }
      }

      if (mandatoryFields?.length > 0) {
        mandatoryFieldsMap[key] = mandatoryFields;
      }
    });

    return mandatoryFieldsMap;
  };

  const validateMandatoryFields = (rows, mandatoryFields) => {
    if (Array.isArray(changeFieldRows)) {
      const updatedMandatoryFields = templateName === TEMPLATE_KEYS?.LOGISTIC ? [...mandatoryFields, { jsonName: "AltUnit", fieldName: "Alternative Unit of Measure" }] : templateName === TEMPLATE_KEYS?.UPD_DESC ? [...mandatoryFields, { jsonName: "Langu", fieldName: "Language" }] : mandatoryFields;
      const validationErrors = {};

      rows?.forEach((row, index) => {
        const missingFields = updatedMandatoryFields?.filter((field) => !row[field?.jsonName] || row[field?.jsonName] === "")?.map((field) => field?.fieldName);
        if (missingFields?.length > 0) {
          validationErrors[index] = {
            id: row.id,
            slNo: row.slNo,
            missingFields,
          };
        }
      });

      return validationErrors;
    } else if (typeof changeFieldRows === "object") {
      let validationErrors = {};

      let index = 0; // Index for object keys
      Object.keys(rows).forEach((key) => {
        rows[key].forEach((row) => {
          const missingFields = mandatoryFields[key]?.filter((field) => !row[field.jsonName] || row[field.jsonName] === "").map((field) => field.fieldName);

          if (missingFields.length > 0) {
            validationErrors[index] = {
              id: row.id,
              slNo: row.slNo,
              type: row.type,
              missingFields,
            };
            index++; // Increment index for the next key
          }
        });
      });

      return validationErrors;
    }
  };

  const handleValidateChangeMultiTable = (button) => {
    const filteredRows = Object.fromEntries(
      Object.entries(changeFieldRows).map(([key, rows]) => [
        key,
        rows.filter((row) => selectedRows?.[key]?.includes(row.id)), // Case: changeFieldRows is an object, filter based on the same key in selectedRows
      ])
    );
    const mandatoryFields = constructMandatoryFields();
    const validationErrors = validateMandatoryFields(filteredRows, mandatoryFields);
    dispatch(setErrorData(validationErrors));
    if (Object.keys(validationErrors).length > 0) {
      const errorRows = Object.keys(validationErrors).map((key) => {
        return {
          "Table Name": validationErrors[key]?.type,
          "Sl. No": validationErrors[key]?.slNo,
          "Missing Fields": validationErrors[key].missingFields?.join(", "),
        };
      });
      setShowOkButton(true);
      setMessageDialogSeverity("danger");
      setDialogTitle("Please Fill All the Mandatory Fields : ");
      const columns = Object.keys(errorRows[0])?.map((key) => ({
        field: key,
        headerName: key?.charAt(0).toUpperCase() + key?.slice(1), // Capitalize header names
        flex: key === "Sl. No" ? 0.5 : key === "Missing Fields" ? 3 : 1.5,
        align: "center",
        headerAlign: "center",
      }));
      setTableColumns(columns);

      // Construct rows with a unique id
      const rows = errorRows?.map((row) => ({
        ...row,
        id: uuidv4(), // Add a unique identifier
      }));
      setTableRows(rows);
      setIsTable(true);
      handleMessageDialogClickOpen();
      dispatch(setIsSubmitDisabled(true));
    } else {
      // if (taskData?.ATTRIBUTE_2 || templateName === TEMPLATE_KEYS?.MRP) {
      if (requestType === REQUEST_TYPE?.CHANGE || requestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        if (!requestId || (requestId && requestorPayload && Object?.keys(requestorPayload)?.length)) {
          handleConfirmDialogOpen();
          return;
        }
        handleValidate(button);
        return;
      }
      setAlertType("success");
      setMessageDialogMessage("Data Validated Successfully");
      handleSnackBarOpen();
      dispatch(setIsSubmitDisabled(false));
      props?.setCompleted([true, true]);
    }
  };

  const handleValidateChange = (button) => {
    const filteredRows = changeFieldRows?.filter((row) => selectedRows?.includes(row.id));
    const validationErrors = validateMandatoryFields(filteredRows, changeDataDT?.["Mandatory Fields"]);
    dispatch(setErrorData(validationErrors));
    if (Object.keys(validationErrors).length > 0) {
      const errorRows = Object.keys(validationErrors).map((key) => {
        return {
          "Sl. No": validationErrors[key]?.slNo,
          "Missing Fields": validationErrors[key].missingFields?.join(", "),
        };
      });
      setShowOkButton(true);
      setMessageDialogSeverity("danger");
      setDialogTitle("Please Fill All the Mandatory Fields : ");
      const columns = Object.keys(errorRows[0])?.map((key) => ({
        field: key,
        headerName: key?.charAt(0).toUpperCase() + key?.slice(1), // Capitalize header names
        flex: key === "Sl. No" ? 0.5 : key === "Missing Fields" ? 3 : 1,
        align: "center",
        headerAlign: "center",
      }));
      setTableColumns(columns);

      // Construct rows with a unique id
      const rows = errorRows?.map((row) => ({
        ...row,
        id: uuidv4(), // Add a unique identifier
      }));
      setTableRows(rows);
      setIsTable(true);
      handleMessageDialogClickOpen();
      dispatch(setIsSubmitDisabled(true));
    } else {
      // if (taskData?.ATTRIBUTE_2 || templateName === TEMPLATE_KEYS?.ITEM_CAT || templateName === TEMPLATE_KEYS?.WARE_VIEW_2) {
      if (requestType === REQUEST_TYPE?.CHANGE || requestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        if (!requestId || (requestId && requestorPayload && Object?.keys(requestorPayload)?.length)) {
          handleConfirmDialogOpen();
          return;
        }
        handleValidate(button);
        return;
      }
      setAlertType("success");
      setMessageDialogMessage("Data Validated Successfully");
      handleSnackBarOpen();
      dispatch(setIsSubmitDisabled(false));
      props?.setCompleted([true, true]);
    }
  };

  const handleDialogConfirm = () => {
    if (isMandatory && !userInput) {
      setRemarksError(true);
      return;
    } else {
      // handleMessageDialogClose();
      if(syndicationType==="scheduleSyndication"){
        if (manageSchedulingRef?.current) {
          manageSchedulingRef?.current?.handlePriorityDialogClickOpen();
        }
      }else{
        // handleScheduledMessageDialogClose();
        executeAction(currentActionType, currentButtonState);
      }
    }
    handleMessageDialogClose();
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
    setUserInput("");
    setRemarksError(false);
    setIsMandatory(false);
  };

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  function openModalForSubmit(){
    handleOpenRemarkDialog();
    setButtoName("");
  }
  const handleSubmit = () => {
      openModalForSubmit()
  }

  return (
    <Stack>
      <Paper sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }} elevation={2}>
        <BottomNavigation
          className="container_BottomNav"
          showLabels
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            gap: 1,
            width: "100%",
          }}
        >
          {(!requestId || (isReqBench && rowData?.reqStatus === REQUEST_STATUS?.DRAFT)) && changePayloadData?.RequestType === REQUEST_TYPE.CHANGE && changePayloadData?.TemplateName === TEMPLATE_KEYS?.SET_DNU && (
            <Box sx={{ flex: 2, marginLeft: "90px" }}>
              <span>
                <strong>Note</strong>: All default values for <strong>Set To DNU</strong> template will be fetched after <strong>Validation</strong>.
              </span>
            </Box>
          )}
          <Box sx={{ display: "flex", gap: 1 }}>
            {requestType !== REQUEST_TYPE.EXTEND_WITH_UPLOAD && requestType !== REQUEST_TYPE.EXTEND && (
              <>
                {!requestId || (isReqBench && ENABLE_STATUSES?.includes(rowData?.reqStatus)) ? (
                  <>
                  
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => {
                        setButtoName(BUTTON_NAME.SAVE);
                        setConfirmationMessage(t(DIALOUGE_BOX_MESSAGES?.SAVE_AS_DRAFT_MSG))
                        if (changePayloadData?.RequestType === REQUEST_TYPE.CHANGE && (!requestId || (requestId && requestorPayload && Object?.keys(requestorPayload)?.length))) {
                          handleConfirmDialogOpen();
                          return;
                        }
                        handleOpenRemarkDialog();
                      }}
                    >
                      {t("Save As Draft")}
                    </Button>
                  
                    {payloadData?.payloadData?.RequestType === REQUEST_TYPE.CREATE && selectedTabValue === TABS_NAME_VALUE.REQUEST_DETAILS && (
                      <Tooltip title={LOADER_MESSAGES.VALIDATE_MANDATORY}>
                        <Button variant="contained" color="primary" onClick={props?.validateMaterials}>
                          {t("Validate")}
                        </Button>
                      </Tooltip>
                    )}
                    {(selectedTabValue === TABS_NAME_VALUE.REQUEST_DETAILS && (changePayloadData?.RequestType === REQUEST_TYPE.CHANGE || changePayloadData?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD || changePayloadData?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || changePayloadData?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD)) ? (
                      <>
                        <Button variant="contained" color="primary" onClick={handleValidateCombined}>
                          {t("Validate")}
                        </Button>
                      </>
                    ) : (
                      <>
                      {selectedTabValue === TABS_NAME_VALUE.PREVIEW && 
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={handleSubmit}
                          disabled={
                            (changePayloadData?.RequestType === REQUEST_TYPE?.CHANGE || changePayloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD || changePayloadData?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || changePayloadData?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) 
                            ? !(changePayloadData?.RequestStatus === REQUEST_STATUS?.VALIDATED_REQUESTOR)
                            : props?.submitForApprovalDisabled
                          }
                        >
                          {t("Submit")}
                        </Button>
                      }
                      </>
                    )}
                  </>
                ) : null}
              </>
            )}
            {((changePayloadData?.RequestType === REQUEST_TYPE.EXTEND || changePayloadData?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD && (
              (!requestId || (isReqBench && ENABLE_STATUSES?.includes(rowData?.reqStatus))) ||
              (!isReqBench && requestId)
            )) || (!isReqBench && requestId)) &&
              (
                props?.filteredButtons?.map((button, index) => {
                  const { MDG_MAT_DYN_BTN_BUTTON_NAME: name, MDG_MAT_DYN_BTN_BUTTON_STATUS: status } = button;
                  const isSyndicationButton = name === "SAP Syndication" || name === "Submit";
                  const isForwardOrSubmitBtn = name === "Forward" || name === "Submit";
                  const isSyndicationEnabled =
                    dynamicData?.requestHeaderData?.RequestStatus === "Validated-MDM" ||
                    changePayloadData?.RequestStatus === "Validated-MDM" ||
                    changePayloadData?.RequestStatus === "Validated-Requestor" || 
                    dynamicData?.childRequestHeaderData?.RequestStatus === "Validated-MDM" ||
                    dynamicData?.childRequestHeaderData?.RequestStatus === "Validated-Requestor" ||
                    props?.childRequestHeaderData?.RequestStatus === "Validated-MDM" ||
                    props?.childRequestHeaderData?.RequestStatus === "Validated-Requestor"

                  let isButtonDisabled = status === "DISABLED";
                  if (isSyndicationButton && isSyndicationEnabled) {
                    isButtonDisabled = false;
                  }

                if (isForwardOrSubmitBtn && (changePayloadData?.RequestType === REQUEST_TYPE?.CREATE || changePayloadData?.RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD) && !props?.submitForApprovalDisabled) {
                  isButtonDisabled = false;
                }
                else if((changePayloadData?.RequestType === REQUEST_TYPE?.CHANGE || changePayloadData?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD || changePayloadData?.RequestType === REQUEST_TYPE?.EXTEND || changePayloadData?.RequestType === REQUEST_TYPE?.EXTEND_WITH_UPLOAD) && isForwardOrSubmitBtn && isSyndicationEnabled) {
                  isButtonDisabled = false;
                }
                return (
                  <Button 
                    key={index} 
                    variant="contained" 
                    size="small" sx={{ ...button_Primary, mr: 1 }} 
                    disabled={isButtonDisabled || blurLoading} 
                    onClick={() =>handleButtonAction(button)  }
                  >
                    {button.MDG_MAT_DYN_BTN_BUTTON_NAME}
                  </Button>
                );
            }))}
          </Box>
        </BottomNavigation>
      </Paper>

      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={dialogTitle}
        dialogMessage={dialogMessage}
        handleDialogConfirm={handleDialogConfirm}
        dialogOkText="OK"
        dialogSeverity={messageDialogSeverity}
        showCancelButton={true}
        showInputText={inputText}
        inputText={userInput}
        blurLoading={blurLoading}
        setInputText={setUserInput}
        mandatoryTextInput={isMandatory}
        remarksError={remarksError}
        isTable={isTable}
        tableColumns={tableColumns}
        tableRows={tableRows}
        isShowWFLevel={props?.showWfLevels && isValidateCorrection}
        isSyndicationBtn={isSyndicationBtn}
        selectedLevel={selectedLevel}
        handleLevelChange={handleLevelChange}
        workFlowLevels={props.workFlowLevels}
        setSyndicationType={setSyndicationType}
        syndicationType={syndicationType}
        isMassSyndication={isMassSyndication}
      />
      <ManageScheduling ref={manageSchedulingRef} 
        dialogTitle={dialogTitle} 
        setDialogTitle={setDialogTitle}
        messageDialogMessage={messageDialogMessage}
        setMessageDialogMessage={setMessageDialogMessage}
        messageDialogSeverity={messageDialogSeverity}
        setMessageDialogSeverity={setMessageDialogSeverity}
        handleMessageDialogClickOpen={handleMessageDialogClickOpen}
        blurLoading={blurLoading}
        setBlurLoading={setBlurLoading}
        handleMessageDialogClose={handleMessageDialogClose}
        createPayloadFromReduxState={createPayloadFromReduxState}
        successMsg={successMsg}
        setSuccessMsg={setSuccessMsg}
        setTextInput={setTextInput}
        inputText={inputText}
        handleSnackBarOpen={handleSnackBarOpen}
        taskData={taskData}
        userData={userData}
        currentButtonState = {currentButtonState}
        requestType = {requestType}
        module={MODULE_MAP.MAT}
      />

      <MaterialConfirmDialog open={confirmationDialogOpen} onClose={handleConfirmDialogClose} handleOk={handleConfirmOk} message={confirmationMessage} />

      {messageDialogMessage && <ReusableSnackBar openSnackBar={openSnackbar} alertMsg={messageDialogMessage} alertType={alertType} handleSnackBarClose={handleSnackBarClose} />}
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />

      <Dialog
        hideBackdrop={false}
        elevation={2}
        PaperProps={{
          sx: { boxShadow: "none" },
        }}
        open={openRemarkDialog}
        onClose={handleRemarksDialogClose}
        maxWidth="xl"
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            display: "flex",
          }}
        >
          <Typography variant="h6">{buttonName === BUTTON_NAME.SAVE ? "Save As Draft" : "Remarks"}</Typography>
          <IconButton sx={{ width: "max-content" }} onClick={handleRemarksDialogClose} children={<CloseIcon />} />
        </DialogTitle>
        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          {buttonName !== BUTTON_NAME.SAVE ? (
            <Stack sx={{marginTop:"16px"}}>
              <Box sx={{ minWidth: 400 }}>
                <FormControl sx={{ height: "auto" }} fullWidth>
                  <TextField 
                    sx={{
                      backgroundColor: "#F5F5F5",
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          borderColor: isMaxLength ? colors?.error?.dark : 'rgba(0, 0, 0, 0.23)',
                        },
                        '&:hover fieldset': {
                          borderColor: isMaxLength ? colors?.error?.dark : 'rgba(0, 0, 0, 0.23)',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: isMaxLength ? colors?.error?.dark : colors?.primary?.dark,
                        },
                      },
                    }}
                    value={remarks} 
                    onChange={handleRemarks} 
                    inputProps={{ maxLength: maxLength }} 
                    multiline 
                    placeholder={"Enter Remarks"}
                  />
                  <FormHelperText 
                    sx={{ 
                      textAlign: 'right',
                      color: isMaxLength ? colors?.error?.dark : 'rgba(0, 0, 0, 0.6)',
                      marginTop: '4px'
                    }}
                  >
                    {`${remarks?.length || 0}/${maxLength}`}
                  </FormHelperText>
                </FormControl>
              </Box>
            </Stack>
          ) : (
            <Box sx={{ margin: "15px" }}>
              <Typography sx={{ fontWeight: "200" }}>{DIALOUGE_BOX_MESSAGES.DRAFT_MESSAGE}</Typography>
            </Box>
          )}
          
        </DialogContent>
        <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
          <Button sx={{ width: "max-content", textTransform: "capitalize" }} onClick={handleRemarksDialogClose}>
            {t("Cancel")}
          </Button>
          <Button className="button_primary--normal" type="save" disabled={blurLoading} onClick={handleBasicSubmit} variant="contained">
            {buttonName === BUTTON_NAME.SAVE ? "Yes" : "Submit"}
          </Button>
        </DialogActions>
      </Dialog>
    </Stack>
  );
};

export default BottomNav;
