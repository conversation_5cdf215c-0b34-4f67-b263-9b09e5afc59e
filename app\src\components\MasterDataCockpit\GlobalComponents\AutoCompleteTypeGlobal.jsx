import { setDependentDropdown } from "@app/dropDownDataSlice";
import { updateModuleFieldData } from "@app/profitCenterTabsSlice";
import { colors } from "@constant/colors";
import { CHANGE_LOG_STATUSES, DROP_DOWN_ITEM_SIZE, DROP_DOWN_SELECT_OR_MAP, LOCAL_STORAGE_KEYS, VISIBILITY_TYPE } from "@constant/enum";
import { Autocomplete, Grid, Stack, TextField, Typography, Tooltip } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";
import { FixedSizeList } from "react-window";
import { destination_CostCenter_Mass, destination_ProfitCenter_Mass, destination_GeneralLedger } from "../../../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import { updateModuleFieldDataCC } from "../../../app/costCenterTabsSlice";
import { updateModuleFieldDataGL } from "../../../app/generalLedgerTabSlice";
import { useLocation } from "react-router-dom";
import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";
import { setRequestHeaderPayloadDataPCG } from "@app/hierarchyDataSlice";
import { setDropDown } from "../../../app/dropDownDataSlice";
import { getLocalStorage } from "@helper/glhelper";
import { setBOMpayloadData } from "../../../modules/BillOfMaterial/bomSlice";
import { MODULE } from "../../../constant/enum";
import { updateModuleFieldDataIO } from "@InternalOrder/slice/internalOrderSlice";

const LISTBOX_PADDING = 2; // px

const OuterElementContext = React.createContext({});

const OuterElementType = React.forwardRef((props, ref) => {
  const outerProps = React.useContext(OuterElementContext);
  return <div ref={ref} {...props} {...outerProps} />;
});

const ListboxComponent = React.forwardRef(function ListboxComponent(props, ref) {
  const { children, ...other } = props;
  const itemData = [];
  children.forEach((item) => {
    itemData.push(item);
  });

  const itemCount = itemData.length;
  const itemSize = DROP_DOWN_ITEM_SIZE.HEIGHT;

  const getHeight = () => {
    if (itemCount > 8) {
      return 8 * itemSize;
    }
    return itemData.length * itemSize;
  };

  return (
    <div ref={ref}>
      <OuterElementContext.Provider value={other}>
        <FixedSizeList itemData={itemData} height={getHeight() + 2 * LISTBOX_PADDING} width="100%" outerElementType={OuterElementType} innerElementType="ul" itemSize={itemSize} overscanCount={5} itemCount={itemCount}>
          {({ data, index, style }) => {
            const dataSet = data[index];
            const inlineStyle = {
              ...style,
              top: style.top + LISTBOX_PADDING,
            };

            return <li style={{ ...inlineStyle, listStyle: "none" }}>{dataSet}</li>;
          }}
        </FixedSizeList>
      </OuterElementContext.Provider>
    </div>
  );
});

const AutocompleteTypeGlobal = (props) => {
  const { uniqueId, field, disabled, dropDownData, handleChange, module } = props;
  const [value, setValue] = useState(null);
  const dispatch = useDispatch();
  const selectedModule = getLocalStorage(LOCAL_STORAGE_KEYS.MODULE, true, {});

  const { updateChangeLogGl } = useChangeLogUpdateGl();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const rowsBodyData = useSelector((state) => state.generalLedger.payload?.rowsBodyData || {});
  let requestStatus = rowsBodyData?.[uniqueId]?.["Torequestheaderdata"]?.["RequestStatus"];
  const SAPview = location.pathname.includes("DisplayMaterialSAPView");
  let userRoles = useSelector((state) => state.userManagement.roles);
  const selectedModuleSelector = DROP_DOWN_SELECT_OR_MAP[selectedModule] || (() => ({}));
  const allDropDownData = useSelector(selectedModuleSelector);

  const valueFromPayloadPC = useSelector((state) => state.profitCenter.payload || {});
  const valueFromPayloadCC = useSelector((state) => state.costCenter.payload || {});
  const valueFromPayloadGL = useSelector((state) => state.generalLedger.payload || {});
  const valueFromPayloadHierarchy = useSelector((state) => state.hierarchyData || {});
  const templateFullData = useSelector((state) => state?.payload?.changeFieldSelectiondata || []);
  const BOMpayloadData = useSelector((state) => state.bom.BOMpayloadData || {});
  const IOpayloadData = useSelector((state) => state.internalOrder.IOpayloadData || {});

  const initialFieldValue =
    module === MODULE?.CC
      ? valueFromPayloadCC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ?? valueFromPayloadCC?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE?.GL
      ? valueFromPayloadGL?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ?? valueFromPayloadGL?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE?.PCG || module === MODULE?.CCG
      ? valueFromPayloadHierarchy?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE.BOM
      ? BOMpayloadData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE.IO
      ? IOpayloadData?.rowsBodyData?.[uniqueId]?.payload?.[field?.jsonName] ?? IOpayloadData?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : valueFromPayloadPC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ?? valueFromPayloadPC?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? null;

  // Debug logging for IO module
  if (module === MODULE.IO) {
    console.log("AutocompleteTypeGlobal Debug:", {
      uniqueId,
      fieldName: field?.jsonName,
      initialFieldValue,
      rowData: IOpayloadData?.rowsBodyData?.[uniqueId],
      payloadData: IOpayloadData?.rowsBodyData?.[uniqueId]?.payload,
      specificFieldValue: IOpayloadData?.rowsBodyData?.[uniqueId]?.payload?.[field?.jsonName]
    });
  }

  const dropDownDataForOptions = dropDownData?.[field?.jsonName] || allDropDownData?.[field?.jsonName] || [];

  useEffect(() => {
    if (initialFieldValue !== null && initialFieldValue !== undefined && initialFieldValue !== "") {
      if (initialFieldValue?.code) {
        setValue(initialFieldValue);
      } else {
        if (dropDownDataForOptions) {
          if (!Array.isArray(dropDownDataForOptions) && !Array.isArray(dropDownDataForOptions?.[uniqueId])) {
            setValue(null);
            return;
          }
          const matchedItem = dropDownDataForOptions?.length ? dropDownDataForOptions?.find((item) => item?.code?.trim() === initialFieldValue?.toString()?.trim()) : dropDownDataForOptions?.[uniqueId]?.find((item) => item?.code?.trim() === initialFieldValue?.toString()?.trim());
          if (matchedItem) {
            setValue({ code: matchedItem?.code, desc: matchedItem?.desc });
            if (module === MODULE?.CC) {
              dispatch(
                updateModuleFieldDataCC({
                  uniqueId: uniqueId || "",
                  keyName: field?.jsonName || "",
                  data: { code: matchedItem?.code, desc: matchedItem?.desc } ?? null,
                  viewID: field?.viewName,
                })
              );
            } else if (module === MODULE?.GL) {
              dispatch(
                updateModuleFieldDataGL({
                  uniqueId: uniqueId || "",
                  keyName: field?.jsonName || "",
                  data: { code: matchedItem?.code, desc: matchedItem?.desc } ?? null,
                  viewID: field?.viewName,
                })
              );
            } else if (module === MODULE?.PCG || module === MODULE?.CCG) {
              dispatch(
                setRequestHeaderPayloadDataPCG({
                  keyName: field?.jsonName || "",
                  data: { code: matchedItem?.code, desc: matchedItem?.desc } ?? null,
                })
              );
            } else if (module === MODULE.BOM) {
              dispatch(
                setBOMpayloadData({
                  keyName: field?.jsonName,
                  data: { code: matchedItem?.code, desc: matchedItem?.desc } ?? null,
                })
              );
            } else if (module === MODULE.IO) {
              dispatch(
                updateModuleFieldDataIO({
                  keyName: field?.jsonName,
                  data: { code: matchedItem?.code, desc: matchedItem?.desc } ?? null,
                })
              );
            } else {
              dispatch(
                updateModuleFieldData({
                  uniqueId: uniqueId || "",
                  keyName: field?.jsonName || "",
                  data: { code: matchedItem?.code, desc: matchedItem?.desc } ?? null,
                  viewID: field?.viewName,
                })
              );
            }
          } else {
            setValue(null);
            if (module === MODULE?.CC) {
              dispatch(
                updateModuleFieldDataCC({
                  uniqueId: uniqueId || "",
                  keyName: field?.jsonName || "",
                  data: null,
                  viewID: field?.viewName,
                })
              );
            } else if (module === MODULE?.GL) {
              dispatch(
                updateModuleFieldDataGL({
                  uniqueId: uniqueId || "",
                  keyName: field?.jsonName || "",
                  data: null,
                  viewID: field?.viewName,
                })
              );
            } else if (module === MODULE?.PCG || module === MODULE?.CCG) {
              dispatch(
                setRequestHeaderPayloadDataPCG({
                  keyName: field?.jsonName || "",
                  data: null,
                })
              );
            } else if (module === MODULE.BOM) {
              dispatch(
                setBOMpayloadData({
                  keyName: field?.jsonName,
                  data: null,
                })
              );
            } else if (module === MODULE.IO) {
              dispatch(
                updateModuleFieldDataIO({
                  uniqueId: uniqueId || "",
                  keyName: field?.jsonName,
                  data: null,
                  viewID: field?.viewName,
                })
              );
            } else {
              dispatch(
                updateModuleFieldData({
                  uniqueId: uniqueId || "",
                  keyName: field?.jsonName || "",
                  data: null,
                  viewID: field?.viewName,
                })
              );
            }
          }
        } else {
          setValue(null);
        }
      }
    } else {
      setValue(null);
    }
  }, [initialFieldValue]);
  const getChangeTemplateFields = (selectedTemplateName) => {
    const filteredAndSortedFields = templateFullData
      .filter((item) => item?.MDG_CHANGE_TEMPLATE_NAME === selectedTemplateName && item?.MDG_MAT_CHANGE_TYPE === "Item" && item?.MDG_MAT_FIELD_VISIBILITY !== "Hidden" && item?.MDG_MAT_FIELD_VISIBILITY !== "Display")
      .sort((a, b) => {
        // Convert to number for proper sorting, handle nulls/fallbacks
        const seqA = Number(a?.MDG_MAT_FIELD_SEQUENCE) || 0;
        const seqB = Number(b?.MDG_MAT_FIELD_SEQUENCE) || 0;
        return seqA - seqB;
      });

    const uniqueFieldNames = [...new Set(filteredAndSortedFields.map((item) => item?.MDG_MAT_FIELD_NAME).filter(Boolean))].map((field) => ({ code: field }));
    dispatch(
      setDropDown({
        keyName: "FieldName",
        data: uniqueFieldNames || [],
        keyName2: uniqueId,
      })
    );
  };

  const getChangeTemplateFieldsGL = (selectedTemplateName) => {
    const filteredAndSortedFields = templateFullData
      .filter((item) => item?.MDG_FIELD_SELECTION_LVL === selectedTemplateName)
      .sort((a, b) => {
        // Convert to number for proper sorting, handle nulls/fallbacks
        const seqA = Number(a?.MDG_SEQUENCE_NO) || 0;
        const seqB = Number(b?.MDG_SEQUENCE_NO) || 0;
        return seqA - seqB;
      });
    const uniqueFieldNames = [...new Set(filteredAndSortedFields.map((item) => item?.MDG_FIELD_NAME).filter(Boolean))].map((field) => ({ code: field }));
    dispatch(
      setDropDown({
        keyName: "FieldName",
        data: uniqueFieldNames || [],
        keyName2: uniqueId,
      })
    );
  };
  const fetchRegionBasedOnCountry = (countryCode) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "Regio",
          data: data?.body || [],
          keyName2: uniqueId,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(`/${destination_ProfitCenter_Mass}/data/getRegionBasedOnCountry?country=${countryCode}`, "get", hSuccess, hError);
  };

  const getAccountIdBasedOnHouseBank = (companyCode = "1010", housebank) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "AccountId",
          data: data?.body || [],
          keyName2: uniqueId,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(`/${destination_GeneralLedger}/data/getAccountId?companyCode=${companyCode}&houseBank=${housebank}`, "get", hSuccess, hError);
  };
  const fetchRegionBasedOnCountryCC = (countryCode) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "AddrRegion",
          data: data?.body || [],
          keyName2: uniqueId,
        })
      );
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(`/${destination_ProfitCenter_Mass}/data/getRegionBasedOnCountry?country=${countryCode}`, "get", hSuccess, hError);
  };

  const handleChangeValue = (newValue) => {
    setValue(newValue);
    if (field?.jsonName === "TemplateName") {
      getChangeTemplateFields(newValue?.code);
    }
    if (field?.jsonName === "Country") {
      fetchRegionBasedOnCountry(newValue?.code);
    }
    if (field?.jsonName === "HouseBank") {
      getAccountIdBasedOnHouseBank("1010", newValue?.code);
    }
    if (field?.jsonName === "AddrCountry") {
      fetchRegionBasedOnCountryCC(newValue?.code);
    }
    if (module === MODULE?.CC) {
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: newValue ?? null,
          viewID: field?.viewName,
        })
      );
    } else if (module === MODULE?.GL) {
      if (field?.jsonName === "TemplateName") {
        getChangeTemplateFieldsGL(newValue?.code);
      }
      dispatch(
        updateModuleFieldData({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: newValue ?? null,
          viewID: field?.viewName,
        })
      );
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: newValue ?? null,
          viewID: field?.viewName,
        })
      );
      {
        requestId &&
          !CHANGE_LOG_STATUSES.includes(requestStatus) &&
          updateChangeLogGl({
            uniqueId: uniqueId || "",
            viewName: props?.field?.viewName,
            plantData: "",
            fieldName: props?.field?.fieldName,
            jsonName: props?.field?.jsonName,
            currentValue: `${newValue?.code}-${newValue?.desc ?? ""}`,
            requestId: initialPayload?.RequestId,
            childRequestId: requestId,
          });
      }
    } else if (module === MODULE.BOM) {
      dispatch(
        setBOMpayloadData({
          keyName: field?.jsonName,
          data: newValue ?? null,
        })
      );
    } else if (module === MODULE.IO) {
      console.log("AutocompleteTypeGlobal - Dispatching IO data:", {
        uniqueId: uniqueId || "",
        keyName: field?.jsonName,
        data: newValue ?? null,
        viewID: field?.viewName,
      });
      dispatch(
        updateModuleFieldDataIO({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName,
          data: newValue ?? null,
          viewID: field?.viewName,
        })
      );
    } else {
      if (field?.jsonName === "Country") {
        fetchRegionBasedOnCountry(newValue?.code);
      }
      if (field?.jsonName === "AddrCountry") {
        fetchRegionBasedOnCountryCC(newValue?.code);
      }
      if (module === MODULE?.CC) {
        dispatch(
          updateModuleFieldData({
            uniqueId: uniqueId || "",
            keyName: field?.jsonName || "",
            data: newValue ?? null,
            viewID: field?.viewName,
          })
        );
      } else if (module === MODULE?.PCG || module === MODULE?.CCG) {
        dispatch(
          setRequestHeaderPayloadDataPCG({
            keyName: field?.jsonName || "",
            data: newValue ?? null,
          })
        );
      } else {
        dispatch(
          updateModuleFieldData({
            uniqueId: uniqueId || "",
            keyName: field?.jsonName || "",
            data: newValue ?? null,
            viewID: field?.viewName,
          })
        );
      }
    }
  };

  return (
    <>
      <Grid item md={2} sx={{ marginBottom: "12px !important" }}>
        <Stack>
          <>
            <Typography
              variant="body2"
              color={colors.secondary.grey}
              sx={{
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
                maxWidth: "100%",
              }}
              title={field?.fieldName}
            >
              {field?.fieldName || "Field Name"}
              {(field?.visibility === "Required" || field?.visibility === VISIBILITY_TYPE.MANDATORY) && <span style={{ color: colors.error.dark }}>*</span>}
            </Typography>
            <Autocomplete
              size="small"
              disabled={disabled || field.disabled}
              options={Array.isArray(dropDownDataForOptions) ? dropDownDataForOptions : Array.isArray(dropDownDataForOptions?.[uniqueId]) ? dropDownDataForOptions[uniqueId] : []}
              value={value}
              getOptionLabel={(option) => (option?.desc ? `${option?.code || ""} - ${option?.desc || ""}` : option?.code && !option?.desc ? `${option?.code || ""}` : `${option || ""}`)}
              ListboxComponent={ListboxComponent}
              onChange={(_, newValue) => {
                setValue(newValue);
                handleChangeValue(newValue ? newValue : null);
              }}
              sx={{
                height: "31px",
                "& .MuiAutocomplete-listbox": {
                  padding: 0,
                  "& .MuiAutocomplete-option": {
                    paddingLeft: "16px",
                    paddingTop: "4px",
                    paddingBottom: "4px",
                    justifyContent: "flex-start",
                  },
                },
                "& .MuiAutocomplete-option": {
                  display: "flex",
                  alignItems: "center",
                  minHeight: "36px",
                },
                "& .MuiInputBase-root.Mui-disabled": {
                  "& > input": {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                  backgroundColor: colors.hover.light,
                },
              }}
              renderOption={(props, option) => {
                const content = (
                  <Typography
                    component="li"
                    style={{
                      fontSize: 12,
                      width: "100%",
                      cursor: "pointer",
                      display: "flex",
                      alignItems: "start",
                    }}
                  >
                    <span
                      style={{
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        textAlign: "left",
                        width: "100%",
                      }}
                      title={`${option?.code}${option?.desc ? ` - ${option?.desc}` : ""}`}
                    >
                      <strong>{option?.code}</strong>
                      {option?.desc ? ` - ${option?.desc}` : ""}
                    </span>
                  </Typography>
                );

                if (field?.jsonName === "RequestType") {
                  return (
                    <li {...props}>
                      <Tooltip title={option?.tooltip || ""} placement="right-end" arrow>
                        <div style={{ width: "100%" }}>{content}</div>
                      </Tooltip>
                    </li>
                  );
                }

                return <li {...props}>{content}</li>;
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  required={field.required}
                  placeholder={disabled || props.field?.visibility === VISIBILITY_TYPE.DISPLAY ? "" : `SELECT ${props?.field?.fieldName?.toUpperCase() || ""}`}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      "& fieldset": {
                        borderColor: "#E0E0E0",
                      },
                      "&:hover fieldset": {
                        borderColor: "#BDBDBD",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#3026B9",
                      },
                    },
                  }}
                />
              )}
            />
          </>
        </Stack>
      </Grid>
    </>
  );
};

export default AutocompleteTypeGlobal;
