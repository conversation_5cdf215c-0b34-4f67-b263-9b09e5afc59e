import { useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { MODULE_MAP, REQUEST_TYPE } from "@constant/enum";
import useChangePayloadCreation from "@hooks/useChangePayloadCreation";
import usePayloadCreation from "@hooks/usePayloadCreation";
import { changePayloadForCC, changePayloadForPC, createPayloadForCC, createPayloadForPC, createPayloadForPCG, createPayloadForCCG } from "../../functions";
import { setFetchedCostCenterDataCc } from "@app/costCenterTabsSlice";


const usePreviewBifurcationPayload = ({
  module,
  requestId,
  requestType,
  templateName,
  payloadData,
}) => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const taskData = useSelector((state) => state.userManagement.taskData);
  const isReqBench = queryParams.get("reqBench");
  const initialReqScreen = !Boolean(taskData?.taskId) && !isReqBench;
  const initialPayload = useSelector((state) => state.request.requestHeader);
  const task = useSelector((state) => state?.userManagement.taskData);

  const reduxPayload = useSelector((state) => {
    switch (module) {
      case MODULE_MAP?.CC:
        return state.costCenter.payload;
      case MODULE_MAP?.PC:
        return state.profitCenter.payload;
      case MODULE_MAP?.GL:
        return state.anotherModuleData.someData;
      case MODULE_MAP?.CCG:
        return state.hierarchyData;
      case MODULE_MAP?.PCG:
        return state.hierarchyData;
      case MODULE_MAP?.CEG:
        return state.hierarchyData;
      default:
        return null; // or some default value
    }
  });

  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );

  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const { fetchedProfitCenterData, fetchReqBenchData } = useSelector(
    (state) => state.profitCenter
  );
  const { fetchedCostCenterData, fetchReqBenchDataCC } = useSelector(
    (state) => state.costCenter
  );

  const { createPayloadFromReduxState } = usePayloadCreation({
    initialReqScreen,
    isReqBench,
  });

  const { changePayloadForTemplate } = useChangePayloadCreation(templateName);

  const payloadMap = {
    [MODULE_MAP.MAT]: () => {
      if (
        requestType === REQUEST_TYPE.CHANGE ||
        requestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
      ) {
        return requestId
          ? changePayloadForTemplate(true)
          : changePayloadForTemplate(false);
      }
      return createPayloadFromReduxState(payloadData);
    },
    [MODULE_MAP.PC]: () => {
  if (
    requestType === REQUEST_TYPE?.CREATE ||
    requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
  ) {
    return createPayloadForPC(
      reduxPayload,
      requestHeaderSlice,
      requestId,
      taskData,
      dynamicData
    );
  }

  return changePayloadForPC(
    requestHeaderSlice,
    taskData,
    isReqBench,
    fetchReqBenchData,
    fetchedProfitCenterData
  );
},
    [MODULE_MAP.CC]: () => {
  if (
    requestType === REQUEST_TYPE?.CREATE ||
    requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
  ) {
    return createPayloadForCC(
      reduxPayload,
      requestHeaderSlice,
      // requestId,
      taskData,
      dynamicData
    );
  }

  return changePayloadForCC(
    requestHeaderSlice,
    initialPayload,
    task,
    isReqBench,
    fetchReqBenchDataCC,
    fetchedCostCenterData
  );
},
    [MODULE_MAP.GL]: () => null,
    [MODULE_MAP.PCG]: () => createPayloadForPCG(payloadData,requestHeaderSlice),
    [MODULE_MAP.CCG]: () => createPayloadForCCG(payloadData,requestHeaderSlice),
    [MODULE_MAP.CEG]: () => null,
  };

  const generatePayload = payloadMap[module] || (() => null);

  return generatePayload();
};

export default usePreviewBifurcationPayload;
