import { createSlice } from "@reduxjs/toolkit";
const savedDialogState = localStorage.getItem("isOpenDialog");
if (savedDialogState === null) {
  localStorage.setItem("isOpenDialog", "true");
}

const initialState = {
  validatedRowsStatus: {},
  validatedRows: {},
  payload: {
    requestHeaderData: {},
    // rowsHeaderData: [
    //   {
    //     id: "first_request_id_requestor",
    //     controllingArea: "",
    //     profitCenterNumber: "",
    //     longDescription: "",
    //     businessSegment: "",
    //     companyCode: "",
    //     included: true,
    //   },
    // ],
    rowsHeaderData: [],
    rowsBodyData: {},
  },
  hasDialogOpened: false,
  isOpenDialog: true,
  profitCenterData: {},
  profitCenterTabs: [],
  profitCenterConfig: {},

  profitCenterBasicData: {},
  profitCenterCompCodes: {},
  profitCenterIndicators: {},
  profitCenterAddress: {},
  profitCenterCommunication: {},
  profitCenterHistory: {},
  profitCenterViewData: [],
  singlePCPayload: {},
  singlePCPayloadGI: {},
  profitCenterNumber: "",
  // singleETPCPayloadGI:{},
  requiredFields: [],
  errorFields: [],
  EditMultipleCostCenter: {},
  EditMultipleProfitCenterData: [],
  MultipleProfitCenterData: [],
  MultipleProfitCenterRequestBench: [],
  requiredFieldsGI: [],
  buttonsIDM: [],
  dqsAuthToken: "",
  companyCode: [
    {
      id: 1,
      companyCodes: "",
      companyName: "",
      assigned: "",
    },
  ],
  handleMassMode: "",
  selectedHeader: [],
  selectedHeaderTab: "",
  rows: [
    {
      id: Date.now(),
      selectedCompanyCode: "",
      pcNumber: "",
    },
  ],
  // validatedRows: [],

  fetchedProfitCenterData: [],
  originalProfitCenterData: [],
  fetchReqBenchData: [],
  originalReqBenchData: [],
  changedFieldsMap: {},
  showGrid: false,
};

export const profitCenterTabSlice = createSlice({
  name: "profitCenter",
  initialState,
  reducers: {
    setPCPayload: (state, action) => {
      state.payload = action.payload;
    },
    setPCRows: (state, action) => {
      state.payload.rowsHeaderData = action.payload;
    },
    setRequestHeaderPayloadSingleField: (state, action) => {
      state.payload.requestHeaderData[action.payload.keyName] =
        action.payload.data;
      return state;
    },
    setRequestHeaderPayloadData: (state, action) => {
      state.payload.requestHeaderData = action.payload;
      return state;
    },
    updateModuleFieldData: (state, action) => {
      const { uniqueId, viewID, keyName, data } = action.payload;

      if (uniqueId) {
        if (!state.payload.rowsBodyData[uniqueId]) {
          state.payload.rowsBodyData[uniqueId] = {};
        }

        // if (!state.payload.rowsBodyData[uniqueId].payloadData[viewID]) {
        //   state.payload.rowsBodyData[uniqueId].payloadData[viewID] = {};
        // }

        if (keyName) {
          state.payload.rowsBodyData[uniqueId][keyName] = data?.code
            ? data.code
            : data
            ? data
            : "";
        }
      } else {
        state.payload.requestHeaderData[action.payload.keyName] = action
          ?.payload?.data?.code
          ? action?.payload?.data?.code
          : action?.payload?.data
          ? action?.payload?.data
          : "";
      }

      return state;
    },
    resetPayloadData: (state) => {
      (state.payload = {
        requestHeaderData: {},
        rowsHeaderData: [
          {
            id: "first_request_id_requestor",
            controllingArea: "",
            profitCenterNumber: "",
            longDescription: "",
            businessSegment: "",
            companyCode: "",
            included: true,
          },
        ],
        rowsBodyData: {},
      }),
        (state.profitCenterTabs = []);
    },
    setRows: (state, action) => {
      state.rows = action.payload;
    },
    setValidatedRows: (state, action) => {
      const { rowId } = action.payload;
      state.validatedRows = {
        ...state.validatedRows,
        [rowId]: true,
      };
    },
    setProfitCenterBasicDataTab: (state, action) => {
      state.profitCenterBasicData = action.payload;
    },
    setProfitCenterCompCodesTab: (state, action) => {
      state.profitCenterCompCodes = action.payload;
    },
    setProfitCenterIndicatorsTab: (state, action) => {
      state.profitCenterIndicators = action.payload;
    },
    setProfitCenterAddressTab: (state, action) => {
      state.profitCenterAddress = action.payload;
    },
    setProfitCenterCommunicationTab: (state, action) => {
      state.profitCenterCommunication = action.payload;
    },
    setProfitCenterHistoryTab: (state, action) => {
      state.profitCenterHistory = action.payload;
    },
    setProfitCenterViewData: (state, action) => {
      state.profitCenterViewData = action.payload;
    },
    setSingleProfitCenterPayload: (state, action) => {
      state.singlePCPayload[action.payload.keyName] = action.payload.data;
      return state;
    },
    setSingleProfitCenterPayloadGI: (state, action) => {
      state.singlePCPayloadGI[action.payload.keyName] = action.payload.data;
      return state;
    },
    setSelectedHeader(state, action) {
      state.selectedHeader = action.payload;
      return state;
    },
    setSelectedHeaderTab(state, action) {
      state.selectedHeaderTab = action.payload;
      return state;
    },
    // setSingleProfitCenterETPayloadGI: (state, action) => {
    //   state.singleETPCPayloadGI[action.payload.keyName] = action.payload.data;
    //   return state;
    // },
    clearProfitCenterPayload: (state) => {
      state.singlePCPayload = {};
    },
    clearProfitCenterSunocoPayloadGI: (state) => {
      state.singlePCPayloadGI = {};
    },
    clearSingleProfitCenter: (state, action) => {
      state.requiredFields = [];
      state.singlePCPayload = {};
      state.profitCenterViewData = [];
      state.requiredFieldsGI = [];
    },
    setPCRequiredFieldsGI: (state, action) => {
      if (
        state.requiredFieldsGI.findIndex((item) => item == action.payload) == -1
      ) {
        state.requiredFieldsGI.push(action.payload);
      }
      return state;
    },
    setPCRequiredFields: (state, action) => {
      if (
        state.requiredFields.findIndex((item) => item == action.payload) == -1
      ) {
        state.requiredFields.push(action.payload);
      }
      return state;
    },
    setPCErrorFields: (state, action) => {
      state.errorFields = action.payload;
      return state;
    },
    setCompanyCode: (state, action) => {
      state.companyCode = action.payload;
      return state;
    },
    setButtonsIDM: (state, action) => {
      state.buttonsIDM = action.payload;
      return state;
    },
    setDqsAuthToken: (state, action) => {
      state.dqsAuthToken = action.payload;
      return state;
    },
    setEditCostCenter(state, action) {
      state.EditMultipleCostCenter = {
        ...state.EditMultipleCostCenter,
        ...action.payload,
      };
    },
    setMultipleProfitCenterData(state, action) {
      // let data = { ...state.MultipleProfitCenterData, ...action.payload };
      // return {
      //   ...state,
      //   MultipleProfitCenterData: data,
      // };
      state.MultipleProfitCenterData = [
        ...state.MultipleProfitCenterData,
        ...action.payload,
      ];
    },
    setEditMultipleProfitCenterData(state, action) {
      state.editMultipleProfitCenterData = action.payload;
    },
    setMultipleProfitCenterRequestBench(state, action) {
      state.MultipleProfitCenterRequestBench = action.payload;
      return state;
    },
    setHandleMassMode(state, action) {
      state.handleMassMode = action.payload;
    },
    setProfitCenterNumber(state, action) {
      state.profitCenterNumber = action.payload;
      return state;
    },
    clearProfitCenter: (state) => {
      state.singlePCPayload = {};
      state.errorFields = [];
      state.requiredFields = [];
      state.requiredFieldsGI = [];
      state.EditMultipleCostCenter = {};
      state.companyCode = initialState.companyCode;
    },
    clearMultipleProfitCenterData: (state, action) => {
      state.MultipleProfitCenterData = [];
    },
    setProfitCenterData: (state, action) => {
      console.log(
        "Reducer: setProfitCenterConfig - New state:",
        action.payload
      );
      state.profitCenterData = action.payload;
    },
    setProfitCenterTabs: (state, action) => {
      console.log("setProfitCenterTabs", action.payload);

      // Ensure state.profitCenterTabs is an array
      if (!Array.isArray(state.profitCenterTabs)) {
        state.profitCenterTabs = [];
      }

      // Merge new tabs without replacing old ones
      action.payload.forEach(({ tab, data }) => {
        const existingTabIndex = state.profitCenterTabs.findIndex(
          (t) => t.tab === tab
        );

        if (existingTabIndex !== -1) {
          // Update existing tab data
          state.profitCenterTabs[existingTabIndex].data = data;
        } else {
          // Add new tab
          state.profitCenterTabs.push({ tab, data });
        }
      });
    },

    setProfitCenterConfig: (state, action) => {
      console.log("Setting Profit Center Config in Redux:", action.payload);
      state.profitCenterConfig = action.payload;
    },
    clearProfitCenterData: (state) => {
      state.profitCenterData = {};
      state.profitCenterTabs = [];
      state.profitCenterConfig = {};
    },

    // change reducers
    setShowGrid: (state, action) => {
      state.showGrid = action.payload;
    },
    setFetchedProfitCenterDataPc: (state, action) => {
      state.fetchedProfitCenterData = action.payload;
    },
    setOriginalProfitCenterDataPc: (state, action) => {
      state.originalProfitCenterData = action.payload;
    },
    setFetchReqBenchDataPc: (state, action) => {
      state.fetchReqBenchData = action.payload;
    },
    setOriginalReqBenchDataPc: (state, action) => {
      state.originalReqBenchData = action.payload;
    },
    updateProfitCenterRowPc: (state, action) => {
      const updatedRow = action.payload;
      state.fetchedProfitCenterData = state.fetchedProfitCenterData.map((row) =>
        row.id === updatedRow.id ? updatedRow : row
      );

      const originalRow = state.originalProfitCenterData.find(
        (row) => row.id === updatedRow.id
      );

      const changedFields = {};
      if (originalRow) {
        Object.keys(updatedRow).forEach((key) => {
          if (updatedRow[key] !== originalRow[key]) {
            changedFields[key] = true;
          }
        });
      }

      state.changedFieldsMap[updatedRow.id] = changedFields;
    },

    updateReqBenchRowPc: (state, action) => {
      const updatedRow = action.payload;

      state.fetchReqBenchData = state.fetchReqBenchData.map(
        (row) => (row.id === updatedRow.id ? { ...row, ...updatedRow } : row) // ✅ merge, don't replace
      );

      const originalRow = state.originalReqBenchData.find(
        (row) => row.id === updatedRow.id
      );

      const changedFields = {};
      if (originalRow) {
        Object.keys(updatedRow).forEach((key) => {
          if (updatedRow[key] !== originalRow[key]) {
            changedFields[key] = true;
          }
        });
      }

      state.changedFieldsMap[updatedRow.id] = changedFields;
    },

    setChangedFieldsMapPc: (state, action) => {
      state.changedFieldsMap = action.payload;
    },

    setValidatedStatus: (state, action) => {
      const { rowId, status } = action.payload;
      state.validatedRowsStatus[rowId] = status;
    },
    resetValidationStatus: (state) => {
      state.validatedRowsStatus = {};
    },

    setProfitCenterRows: (state, action) => {
      state.payload.rowsHeaderData = action.payload;
    },

    // setProfitCenterTab: (state, action) => {
    //   state.payload.rowsBodyData = action.payload;
    // },

    setProfitCenterTab: (state, action) => {
      state.payload.rowsBodyData = {
        ...state.payload.rowsBodyData,
        ...action.payload, // merge new tab data by rowId
      };
    },
    setDialogOpened: (state, action) => {
      state.hasDialogOpened = action.payload;
    },
    setOpenDialog: (state, action) => {
      state.isOpenDialog = action.payload;
      // localStorage.setItem("isOpenDialog", JSON.stringify(action.payload));
    },
    setValidatedRowsFromStorage: (state, action) => {
  state.validatedRows = action.payload;
  },
    resetValidatedRows: (state) => {
      state.validatedRows = {};
    },

    resetProfitCenterStatePc: () => initialState,
  },
});

// Action creators are generated for each case reducer function
export const {
  setDialogOpened,
  setValidatedRowsFromStorage,
  resetValidatedRows,
  setOpenDialog,
  setIsDialogOpen,
  setValidatedStatus,
  setProfitCenterRows,
  setProfitCenterTab,
  resetValidationStatus,
  setPCPayload,
  resetPayloadData,
  setPCRows,
  setRequestHeaderPayloadSingleField,
  setRequestHeaderPayloadData,
  updateModuleFieldData,
  setRows,
  setValidatedRows,
  setProfitCenterBasicDataTab,
  setProfitCenterCompCodesTab,
  setProfitCenterIndicatorsTab,
  setProfitCenterAddressTab,
  setProfitCenterCommunicationTab,
  setProfitCenterHistoryTab,
  setSingleProfitCenterPayload,
  clearProfitCenterPayload,
  setPCRequiredFields,
  setPCErrorFields,
  setCompanyCode,
  setMultipleProfitCenterData,
  setEditMultipleProfitCenterData,
  setMultipleProfitCenterRequestBench,
  setHandleMassMode,
  clearProfitCenter,
  setProfitCenterViewData,
  setPCRequiredFieldsGI,
  setSingleProfitCenterPayloadGI,
  setButtonsIDM,
  setDqsAuthToken,
  setSelectedHeader,
  clearMultipleProfitCenterData,
  setSelectedHeaderTab,
  clearProfitCenterSunocoPayloadGI,
  clearSingleProfitCenter,
  setProfitCenterNumber,
  setProfitCenterData,
  setProfitCenterTabs,
  setProfitCenterConfig,
  clearProfitCenterData,
  // setSingleProfitCenterETPayloadGI

  setFetchedProfitCenterDataPc,
  setOriginalProfitCenterDataPc,
  setFetchReqBenchDataPc,
  setOriginalReqBenchDataPc,
  updateProfitCenterRowPc,
  updateReqBenchRowPc,
  setChangedFieldsMapPc,
  resetProfitCenterStatePc,
  setShowGrid,
} = profitCenterTabSlice.actions;

export const profitCenterReducer = profitCenterTabSlice.reducer;
