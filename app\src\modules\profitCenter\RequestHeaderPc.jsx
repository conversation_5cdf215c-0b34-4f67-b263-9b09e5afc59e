import React, { useState, useEffect } from "react";
import {
  <PERSON>rid,
  <PERSON>Field,
  MenuItem,
  Button,
  Card,
  Typography,
  Stack,
  Box,
} from "@mui/material";
import { useSelector, useDispatch } from "react-redux";
import { doAjax } from "../../components/Common/fetchService";
import { container_Padding } from "../../components/Common/commonStyles";
import FilterFieldGlobal from "../../components/MasterDataCockpit/FilterFieldGlobal";
import { destination_ProfitCenter_Mass } from "../../destinationVariables";
import RequestDetailsChangePC from "./RequestDetailsChangePC";
import {
  ERROR_MESSAGES,
  REQUEST_TYPE,
  SUCCESS_MESSAGES,
  VISIBILITY_TYPE,
} from "@constant/enum";
import { setActiveStep } from "@app/redux/stepperSlice";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { setRequestHeaderData } from "@app/redux/requestHeaderSlice";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import useProfitCenterChangeFieldConfig from "@hooks/useProfitCenterChangeFieldConfig";

import DownloadDialog from "@components/Common/DownloadDialog";
import { END_POINTS } from "@constant/apiEndPoints";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { useLocation, useNavigate } from "react-router-dom";

import useProfitcenterRequestHeaderConfig from "@hooks/useProfitcenterRequestHeaderConfig";
import { updateModuleFieldData } from "@app/profitCenterTabsSlice";
import { setRequestHeader } from "@app/requestDataSlice";

import useLang from "@hooks/useLang";

const RequestHeaderPC = ({
  apiResponse,
  reqBench,
  downloadClicked,
  setDownloadClicked,
  setIsSecondTabEnabled,
  setIsAttachmentTabEnabled,
}) => {
  const currentHash = window.location.hash;
  const parts = currentHash.split("/");
  const activeLocation = parts[parts.length - 1];

  const { t } = useLang();
  const today = new Date();
  const formattedDate = today.toLocaleDateString("en-GB");
  const dispatch = useDispatch();
  const payloadFields = useSelector(
    (state) => state.profitCenter.payload.requestHeaderData
  );
  const requestHeaderData = useSelector((state) => state.request.requestHeader);
  const userData = useSelector((state) => state.userManagement.userData);
  const requestHeaderDetails = useSelector(
    (state) => state.tabsData.requestHeaderData
  );
  const fieldNames = useSelector(
    (state) => state.AllDropDown.dropDown.FieldName || []
  );
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isWorkspace = queryParams.get("RequestId");

  const currentDate = `/Date(${Date.now()})/`;

  const [requestType, setRequestType] = useState("");
  const [requestPriorityValue, setRequestPriorityValue] = useState("");
  const [requestDesc, setRequestDesc] = useState("");
  const [changeCategory, setChangeCategory] = useState("");
  const [changeReason, setChangeReason] = useState([]);
  const navigate = useNavigate();
  const [isButtonEnabled, setIsButtonEnabled] = useState(false);
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [isLoading, setIsLoading] = useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [successMsg, setSuccessMsg] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState("");
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);

  const { getChangeTemplate } =
    useProfitCenterChangeFieldConfig("Profit Center");
  const { getRequestHeaderTemplatePc } = useProfitcenterRequestHeaderConfig();

  const requestTypeData = [
    {
      code: "Create",
      tooltip: "Create New ProfitCenter Directly in Application",
    },
    {
      code: "Change",
      tooltip: "Modify Existing ProfitCenter Directly in Application",
    },
    {
      code: "Create with Upload",
      tooltip: "Create New ProfitCenter with Excel Upload",
    },
    {
      code: "Change with Upload",
      tooltip: "Modify Existing ProfitCenter with Excel Upload",
    },
  ];

  // const requestTypeData =[
  //       { code: "Create",desc: ""},
  //       {
  //         code: "Change",desc: ""

  //       },
  //       {
  //         code: "Create with Upload",desc: ""

  //       },
  //       {
  //         code: "Change with Upload",desc: ""

  //       },
  //     ]

  const templateNames = [
    { code: "All Other Fields", desc: "" },
    { code: "Address Change", desc: "" },
    { code: "Block", desc: "" },
    { code: "Temporary Block/Unblock", desc: "" },
  ];

  const requestPriority = [
    { code: "High", desc: "" },
    { code: "Medium", desc: "" },
    { code: "Low", desc: "" },
  ];

  const templatePC = useSelector(
    (state) => state.AllDropDown?.dropDown?.TemplateName
  );

  dispatch(updateModuleFieldData({ keyName: "RequestStatus", data: "DRAFT" }));
  dispatch(
    updateModuleFieldData({ keyName: "ReqCreatedBy", data: userData?.user_id })
  );

  // dispatch(setDropDown({ keyName: "RequestPriority", data: requestPriority }))
  // dispatch(setDropDown({ keyName: "RequestType", data: requestTypeData }));
  // dispatch(setDropDown({ keyName: "TemplateName", data: templateNames }))

  useEffect(() => {
    if (downloadClicked) {
      if (payloadFields?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
        setOpenDownloadDialog(true);
        return;
      }
      if (payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        setDialogOpen(true);
        return;
      }
    }
  }, [downloadClicked]);

  const checkAllFieldsFilled = () => {
    let allFilled = true;
    if (
      payloadFields &&
      requestHeaderDetails[Object.keys(requestHeaderDetails)]?.length
    ) {
      requestHeaderDetails[Object.keys(requestHeaderDetails)[0]]?.forEach(
        (reqst) => {
          if (
            !payloadFields[reqst.jsonName] &&
            reqst.visibility === VISIBILITY_TYPE?.MANDATORY
          ) {
            allFilled = false;
          }
        }
      );
    } else {
      allFilled = false;
    }
    return allFilled;
  };

  useEffect(() => {
    if (
      payloadFields?.RequestType === "Change" ||
      payloadFields?.RequestType === "Change with Upload"
    ) {
      getChangeTemplate(payloadFields?.TemplateName);
    }
  }, [payloadFields?.RequestType]);

  useEffect(() => {
    getRequestHeaderTemplatePc();
  }, [payloadFields?.RequestType]);

  useEffect(() => {
    const baseValid =
      requestType && requestPriorityValue && requestDesc.trim() !== "";
    const changeFieldsValid =
      requestType !== "Change" ||
      requestType === "Change with Upload" ||
      (changeCategory && changeReason.length > 0);
    setIsButtonEnabled(baseValid && changeFieldsValid);
  }, [
    requestType,
    requestPriorityValue,
    requestDesc,
    changeCategory,
    changeReason,
  ]);

  const handleButtonClick = () => {
    const epochTime = new Date(payloadFields?.ReqCreatedOn).getTime();
    const fieldNameValue =
      fieldNames.map((item) => item.code || "").join(", ") || "";
    setDialogOpen(false);

    const payload = {
      RequestId: "",
      ReqCreatedBy: "<EMAIL>",
      ReqCreatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      ReqUpdatedOn: epochTime ? `/Date(${epochTime})/` : currentDate,
      RequestType: payloadFields?.RequestType || "",
      RequestDesc: payloadFields?.RequestDesc || "",
      RequestStatus: "DRAFT",
      RequestPriority: payloadFields?.RequestPriority || "",
      FieldName: fieldNameValue,
      TemplateName: payloadFields?.TemplateName || "",
      ChangeCategory: "",
      IsHierarchyGroup: false,
    };

    const hSuccess = (data) => {
      setSuccessMsg(true);
      setMessageDialogMessage(
        `Request Header Created Successfully! Request ID: ${data?.body?.requestPrefix}${data?.body?.requestId}`
      );
      setIsLoading(false);
      setAlertType("success");
      handleSnackBarOpen();
      // setIsAttachmentTabEnabled(true);
      dispatch(setRequestHeaderData(data?.body));
      dispatch(setRequestHeader(data.body));
      //setDisableProceed(false);
      // dispatch(updateAllTabsData({}))
      // dispatch(setTaskData({}))
      if (
        payloadFields?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
        payloadFields?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
      ) {
        setOpenDownloadDialog(true);
        return;
      }
      if (payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        setDialogOpen(true);
        return;
      }

      setTimeout(() => {
        dispatch(setActiveStep(1));
        setIsSecondTabEnabled(true);
      }, 2500);
    };

    const hError = (error) => {
      console.error("APIError", error);
      setSuccessMsg(true);
      setAlertType("error");
      setMessageDialogMessage("Error occured while saving Request Header");
      handleSnackBarOpen();
    };

    const destination = destination_ProfitCenter_Mass;


    doAjax(
      `/${destination}/massAction/createRequestHeader`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const handleDownloadDialogClose = () => {
    setDownloadClicked(false);
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
    if (!isWorkspace) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  const handleDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    let payload = {
      scenario:
        payloadFields?.RequestType || requestHeaderData?.requestType || "",
      rolePrefix: "ETP",
      dtName: "MDG_PC_FIELD_CONFIG",
      version: "v2",
      requestId: payloadFields?.RequestId || requestHeaderData?.requestId || "",
    };
    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        setSuccessMsg(true);
        setMessageDialogMessage("No data found for the selected criteria.");
        setAlertType("danger");
        handleSnackBarOpen();
        return;
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", "Mass_Create.xlsx");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setMessageDialogMessage(
        `${
          payloadFields?.TemplateName
            ? `${payloadFields?.TemplateName}_Mass Change`
            : "Mass_Create"
        }.xlsx has been downloaded successfully.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(`/requestBench`);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
    };

    const downloadUrl = `/${destination_ProfitCenter_Mass}${
      payloadFields?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND
        : END_POINTS.EXCEL.DOWNLOAD_EXCEL
    }`;
    doAjax(downloadUrl, "postandgetblob", hSuccess, hError, payload);
  };

  const handleEmailDownload = () => {
    setBlurLoading(true);
    let payload = {
      region: payloadFields?.Region,
      scenario: payloadFields?.RequestType,
      matlType: "ALL",
      dtName: "MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",
      version: "v1",
      rolePrefix: "",
      requestId: payloadFields?.RequestId ? payloadFields?.RequestId : "",
    };
    const hSuccess = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(SUCCESS_MESSAGES?.DOWNLOAD_MAIL_INITIATED);
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setSuccessMsg(true);
      setMessageDialogMessage(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL);
      setAlertType("danger");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };

    const downloadUrl = `/${destination_MaterialMgmt}${
      payloadFields?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? END_POINTS.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL
        : END_POINTS.EXCEL.DOWNLOAD_EXCEL_MAIL
    }`;

    doAjax(downloadUrl, "post", hSuccess, hError, payload);
  };

  return (
    <div>
      <Stack spacing={2}>
        {Object.entries(requestHeaderDetails).map(([key, fields]) => {
          return (
            <Grid
              item
              md={12}
              key={key}
              sx={{
                backgroundColor: "white",
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: "1px solid #E0E0E0",
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
              }}
            >
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  paddingBottom: "10px",
                }}
              >
                {key}
              </Typography>

              <Box>
                <Grid container spacing={1}>
                  {fields
                    .filter((field) => field.visibility !== "Hidden")
                    .sort((a, b) => a.sequenceNo - b.sequenceNo)
                    .map((innerItem) => (
                      <FilterFieldGlobal
                        isHeader={true}
                        key={innerItem.id}
                        field={innerItem}
                        dropDownData={{
                          RequestType: requestTypeData,
                          RequestPriority: requestPriority,
                          TemplateName: templatePC,
                        }}
                        disabled={isWorkspace || !!requestHeaderData?.requestId}
                        requestHeader={true}
                      />
                    ))}
                </Grid>
              </Box>

              {!isWorkspace && !requestHeaderData?.requestId && (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    marginTop: "20px",
                  }}
                >
                  <Button
                    variant="contained"
                    color="primary"
                    disabled={!checkAllFieldsFilled()}
                    onClick={handleButtonClick}
                  >
                    {t("Save Request Header")}
                  </Button>
                </Box>
              )}
            </Grid>
          );
        })}

        <ReusableBackDrop
          blurLoading={blurLoading}
          loaderMessage={loaderMessage}
        />
        {successMsg && (
          <ReusableSnackBar
            openSnackBar={openSnackbar}
            alertMsg={messageDialogMessage}
            alertType={alertType}
            handleSnackBarClose={handleSnackBarClose}
          />
        )}

        {dialogOpen && (
          <RequestDetailsChangePC
            downloadClicked={downloadClicked}
            setDownloadClicked={setDownloadClicked}
          />
        )}
        <DownloadDialog
          onDownloadTypeChange={onDownloadTypeChange}
          open={openDownloadDialog}
          downloadType={downloadType}
          handleDownloadTypeChange={handleDownloadTypeChange}
          onClose={handleDownloadDialogClose}
        />
      </Stack>
    </div>
  );
};

export default RequestHeaderPC;
