
import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  requestHeaderDTIO: {},
  tabValue:0,
  dropDownDataIO: {},
  IOpayloadData: {
    requestHeaderData: {},
    rowsHeaderData: [],
    rowsBodyData: {},
  },
  requestHeaderID: "",
  savedReqData: {},
  fieldConfigByOrderType: {}, // Store field config by order type
};

const internalOrderSlice = createSlice({
  name: "internalOrder",
  initialState,
  reducers: {
    setRequestHeaderDTIO: (state, action) => {
      state.requestHeaderDTIO = action.payload;
    },
    clearRequestHeaderIO: (state) => {
      state.requestHeaderDTIO = {};
    },
    setDropDownDataIO: (state, action) => {
      const { keyName, data } = action.payload;
      state.dropDownDataIO[keyName] = data;
    },
    setIOpayloadData: (state, action) => {
      const { keyName, data } = action.payload;
      state.IOpayloadData[keyName] = data;
    },
    updateModuleFieldDataIO: (state, action) => {
      const { uniqueId, viewID, keyName, data } = action.payload;
      const value = data?.code ?? data ?? "";
      if (uniqueId) {
        if (!state.IOpayloadData.rowsBodyData[uniqueId]) {
          state.IOpayloadData.rowsBodyData[uniqueId] = {};
        }
        if (!state.IOpayloadData.rowsBodyData[uniqueId].payload) {
          state.IOpayloadData.rowsBodyData[uniqueId].payload = {};
        }
        state.IOpayloadData.rowsBodyData[uniqueId].payload[keyName] = value;
      } else {
        state.IOpayloadData.requestHeaderData[keyName] = value;
      }
    },
    updateTableColumnDataIO: (state, action) => {
      const { uniqueId, keyName, data } = action.payload;
      const value = data?.code ?? data ?? "";
      if (uniqueId) {
        if (!state.IOpayloadData.rowsBodyData[uniqueId]) {
          state.IOpayloadData.rowsBodyData[uniqueId] = {};
        }
        // Store table column data directly under uniqueId (not in payload)
        state.IOpayloadData.rowsBodyData[uniqueId][keyName] = value;
      }
    },
    removeRowDataIO: (state, action) => {
      const { uniqueId } = action.payload;
      if (uniqueId && state.IOpayloadData.rowsBodyData[uniqueId]) {
        delete state.IOpayloadData.rowsBodyData[uniqueId];
      }
    },
    setInternalOrderFieldConfig: (state, action) => {
      const { orderType, fieldData } = action.payload;
      if (!state.fieldConfigByOrderType) {
        state.fieldConfigByOrderType = {};
      }
      state.fieldConfigByOrderType[orderType] = fieldData;
    },
    setRequestHeaderIDIO: (state, action) => {
      state.requestHeaderID = action.payload;
    },
    setSavedReqData: (state, action) => {
      state.savedReqData = action.payload;
    },
    setTabValue: (state, action) => {
      state.tabValue = action.payload;
    },
    resetInternalOrderState: (state) => {
      // Reset to initial state for new requests
      state.tabValue = 0;
      state.dropDownDataIO = {};
      state.IOpayloadData = {
        requestHeaderData: {},
        rowsHeaderData: [],
        rowsBodyData: {},
      };
      state.requestHeaderID = "";
      state.savedReqData = {};
      state.fieldConfigByOrderType = {};
    },
  },
});

export const {
  setRequestHeaderDTIO,
  clearRequestHeaderIO,
  setDropDownDataIO,
  setIOpayloadData,
  setRequestHeaderIDIO,
  updateModuleFieldDataIO,
  updateTableColumnDataIO,
  removeRowDataIO,
  setInternalOrderFieldConfig,
  setSavedReqData,
  setTabValue,
  resetInternalOrderState
} = internalOrderSlice.actions;

export default internalOrderSlice.reducer;
