import { container_Padding } from '@components/Common/commonStyles';
import { Box, Grid, Stack, Typography, Tooltip } from '@mui/material';
import { useSelector } from 'react-redux';
import { API_CODE, REQUEST_STATUS, REQUEST_TYPE, BUTTON_NAME, ENABLE_STATUSES, INITIAL_PAYLOAD_MAP, REQUEST_HEADER_MAP, MODULE_MAP } from '@constant/enum';
import { useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { doAjax } from "../../components/Common/fetchService";
import { destination_IDM } from "../../../src/destinationVariables";
import useLogger from '@hooks/useLogger';
import WorkflowDashboard from "@components/RequestHistory/workFlow/WorkflowDashboard";
import PreviewDownloadCard from './RequestPages/PreviewDownloadCard';
import WorkflowSkeletonLoader from '@components/common/ui/skelton/WorkflowSkeletonLoader';
import usePreviewBifurcationPayload from "@modules/modulesHooks/usePreviewBifurcationPayload";
import BottomNav from "@material/BottomNav";
import { checkIncludedAndValidated, filterButtonsBasedOnTab, filterNavigation } from "@helper/helper";
import {useCreateDynamicButtons} from "@hooks/useCreateDynamicButtons";
import { BUTTONS_ACTION_TYPE, TABS_NAME_VALUE } from '@constant/buttonPriority';
import {useExtendDynamicButton} from '@hooks/useExtendDynamicButton';
import { useSnackbar } from "@hooks/useSnackbar";
import useLang from '@hooks/useLang';

const PreviewPage = (props) => {
  const payloadData  = props?.payloadData
  const { customError } = useLogger();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const storedRows = useSelector((state) => state.request.materialRows);
  const taskData = useSelector((state) => state.userManagement.taskData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const isreqBench = queryParams.get("reqBench");
  const RequestId = queryParams.get("RequestId");
   const requestDetails = useSelector((state) => {
    switch (props.module) {
      case MODULE_MAP?.MAT : 
       return state.request.requestHeader;
      case MODULE_MAP?.CC:
        return state.costCenter.payload.requestHeaderData;
      case MODULE_MAP?.PC:
        return state.profitCenter.payload.requestHeaderData;
      case MODULE_MAP?.GL:
        return state.anotherModuleData.someData;
      case MODULE_MAP?.CCG:
        return state.hierarchyData.requestHeaderData;
      case MODULE_MAP?.PCG:
        return state.hierarchyData.requestHeaderData;
      case MODULE_MAP?.CEG:
        return state.hierarchyData.requestHeaderData;
      default:
        return null; // or some default value
    }
  });
  
  const { showSnackbar } = useSnackbar();
  const [workFlowDetails, setWorkFlowDetails] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const selectedModuleRequestHeader = REQUEST_HEADER_MAP[props?.module] || (() => ({}));
  const requestHeaderDetails = useSelector(selectedModuleRequestHeader);

  
  const { t } = useLang();
  const selectedModuleInitialPayload = INITIAL_PAYLOAD_MAP[props?.module] || (() => ({}));
  const initialPayload = useSelector(selectedModuleInitialPayload)
  const filteredButtonsForChange = useSelector((state) => state.payload.filteredButtons)
  const {filteredButtons} = useCreateDynamicButtons(taskData,applicationConfig,destination_IDM,BUTTON_NAME);
  const {extendFilteredButtons} = useExtendDynamicButton(taskData,applicationConfig,destination_IDM,BUTTON_NAME)
  const requestStatus = initialPayload?.RequestStatus;
  const requestStatusCheck = (requestStatus === REQUEST_STATUS.DRAFT || requestStatus === REQUEST_STATUS.DRAFT_IN_CAPS || requestStatus === REQUEST_STATUS.VALIDATED_REQUESTOR || requestStatus === REQUEST_STATUS.VALIDATION_FAILED_REQUESTOR || requestStatus === REQUEST_STATUS.UPLOAD_SUCCESSFUL)
  const {destination,bifurcationEndPoint} = filterNavigation(props?.module)

  const  payloadForBifurcation  = usePreviewBifurcationPayload({module:props?.module,requestId:RequestId,requestType:initialPayload?.RequestType,templateName:initialPayload?.TemplateName,payloadData});
  let requestDetailsButton;
  const actionTypesToFilter = [
    BUTTONS_ACTION_TYPE.HANDLE_SEND_BACK,
    BUTTONS_ACTION_TYPE.HANDLE_VALIDATE,
    BUTTONS_ACTION_TYPE.HANDLE_CORRECTION,
  ];

  if (
    initialPayload?.RequestType === REQUEST_TYPE.CREATE ||
    initialPayload?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD
  ) {
    requestDetailsButton = filterButtonsBasedOnTab(
      filteredButtons,
      [...actionTypesToFilter, BUTTONS_ACTION_TYPE.HANDLE_VALIDATE1]
    );
  } else if (
    initialPayload?.RequestType === REQUEST_TYPE.EXTEND ||
    initialPayload?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
  ) {
    requestDetailsButton = filterButtonsBasedOnTab(
      extendFilteredButtons,
      actionTypesToFilter
    );
  } else if (
    initialPayload?.RequestType === REQUEST_TYPE.CHANGE ||
    initialPayload?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
  ) {
    requestDetailsButton = filterButtonsBasedOnTab(
      filteredButtonsForChange,
      actionTypesToFilter
    );
  } else {
    requestDetailsButton = [];
  }
  const disableCheck = !ENABLE_STATUSES.includes(props?.requestStatus) || (RequestId && !isreqBench);
  useEffect(() => {
    if(requestStatusCheck){
      setIsLoading(true)
      const hSuccess = (data) => {
        if (data.statusCode === API_CODE.STATUS_200) {
          setWorkFlowDetails(data?.body)
          setIsLoading(false)
        }else{
          showSnackbar(data?.message,'error')
          setIsLoading(false)
        }
      }
      const hError = (error) => {
        customError(error)
        setIsLoading(false)
      }

      doAjax(`/${destination}${bifurcationEndPoint}`, "post", hSuccess, hError, payloadForBifurcation);
    }
  }, []);

  return (
    <>
    <Stack spacing={2}>
      {Object.entries(requestHeaderDetails).map(([key, fields]) => (
        <Grid
          item
          md={12}
          key={key}
          sx={{
            backgroundColor: "white",
            borderRadius: "8px",
            border: "1px solid #E0E0E0",
            boxShadow: "0px 1px 4px rgba(0, 0, 0, 0.1)",
            ...container_Padding,
            pt: '10px'
          }}
        >
          {/* Section Title */}
          <Typography
            sx={{
              fontWeight: "bold",
              mb: "6px",
            }}
          >
            {t("Request Details")}
          </Typography>

          {/* Fields Container */}
          <Box
            sx={{
              backgroundColor: "#FAFAFA",
              padding: "10px",
              pl: '0px',
              pr: '0px',
              borderRadius: "8px",
              boxShadow: "none",
            }}
          >
            <Grid container spacing={2}>
              {fields
                .filter((field) => field.visibility !== "Hidden")
                .sort((a, b) => a.sequenceNo - b.sequenceNo)
                .map((innerItem) => {
                  let value =
                    requestDetails?.[innerItem?.jsonName] ||
                    initialPayload?.[innerItem?.jsonName] ||
                    "";
                  let formattedValue = "";

                  if (Array.isArray(value)) {
                    formattedValue = value.join(", ");
                  } else if (value instanceof Date || (typeof value === "object" && value instanceof Object && value.toString().includes("GMT"))) {
                    formattedValue = new Date(value).toLocaleString(); // Converts Date to readable format
                  } else {
                    formattedValue = value;
                  }
                  value = formattedValue;
                  return (
                    value && value !== null &&
                    value !== "" && (
                      <Grid item md={3} key={innerItem?.id}>
                        <div
                          style={{
                            padding: "12px",
                            backgroundColor: "#ffffff",
                            borderRadius: "8px",
                            boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                            // margin: "16px 0",
                            transition: "all 0.3s ease",
                          }}
                        >
                          {/* Field Name with Tooltip */}
                          <Tooltip title={t(innerItem?.fieldName) || "Field Name"}>
                            <Typography
                              variant="body1"
                              sx={{
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                maxWidth: "100%",
                                fontWeight: 600,
                                fontSize: "12px",
                                marginBottom: "4px",
                                display: "flex",
                                alignItems: "center",
                              }}
                            >
                              {t(innerItem?.fieldName) || "Field Name"}
                              {(innerItem?.visibility === "Required" ||
                                innerItem?.visibility === "MANDATORY") && (
                                  <span
                                    style={{ color: "#d32f2f", marginLeft: "2px" }}
                                  >
                                    *
                                  </span>
                                )}
                            </Typography>
                          </Tooltip>

                          {/* Value with Tooltip */}
                          <Tooltip title={value || "--"}>
                            <div
                              style={{
                                fontSize: "0.8rem",
                                color: "#333333",
                                marginTop: "4px",
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                maxWidth: "100%",
                              }}
                            >
                              <span
                                style={{
                                  fontWeight: 500,
                                  color: "grey",
                                  letterSpacing: "0.5px",
                                  wordSpacing: "1px",
                                }}
                              >
                                {value || "--"}
                              </span>
                            </div>
                          </Tooltip>
                        </div>
                      </Grid>
                    )
                  );
                })}
            </Grid>
          </Box>
        </Grid>
      ))}
     <PreviewDownloadCard payloadForDownloadExcel={props?.payloadForDownloadExcel}/>
     {requestStatusCheck && (
      <>
      {workFlowDetails && !isLoading ? (
        <WorkflowDashboard data={workFlowDetails} module={props.module} />
          ) : (
            <WorkflowSkeletonLoader />
        )}
      </>
     )}
    </Stack>
    {(!disableCheck || (RequestId && !isreqBench) || (isreqBench && ENABLE_STATUSES.includes(props?.requestStatus))) && 
      <Box sx={{ 
        borderTop: '1px solid #e0e0e0',
        padding: '16px'
      }}>
        <BottomNav
          activeTab={TABS_NAME_VALUE.PREVIEW} 
          submitForApprovalDisabled={!checkIncludedAndValidated(storedRows)} 
          filteredButtons={requestDetailsButton}
          childRequestHeaderData={payloadData?.[initialPayload?.selectedMaterialID]?.Tochildrequestheaderdata} 
        />
      </Box>
    }
    </>
  )
}

export default PreviewPage;
