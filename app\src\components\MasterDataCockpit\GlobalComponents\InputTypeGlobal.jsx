import { <PERSON><PERSON>, <PERSON>ack, TextField, Typography } from "@mui/material";
import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateModuleFieldData } from "../../../app/profitCenterTabsSlice";
import { updateModuleFieldDataCC } from "../../../app/costCenterTabsSlice";
import { colors } from "@constant/colors";
import { updateModuleFieldDataGL } from "@app/generalLedgerTabSlice";
import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";
import { CHANGE_LOG_STATUSES } from "@constant/enum";

import { setRequestHeaderPayloadDataPCG } from "@app/hierarchyDataSlice";
import { setBOMpayloadData } from "@BillOfMaterial/bomSlice";
import { MODULE } from "../../../constant/enum";
import { updateModuleFieldDataIO } from "@InternalOrder/slice/internalOrderSlice";

const InputTypeGlobal = (props) => {
  const { uniqueId, field, disabled, handleChange, module } = props;
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const rowsBodyData = useSelector((state) => state.generalLedger.payload?.rowsBodyData || {});
  let requestStatus = rowsBodyData?.[uniqueId]?.["Torequestheaderdata"]?.["RequestStatus"];
  const valueFromPayloadPC = useSelector((state) => state.profitCenter.payload || {});
  const valueFromPayloadCC = useSelector((state) => state.costCenter.payload || {});
  const valueFromPayloadGL = useSelector((state) => state.generalLedger.payload || {});
  const BOMpayloadData = useSelector((state) => state.bom.BOMpayloadData || {});
  const IOpayloadData = useSelector((state) => state.internalOrder.IOpayloadData || {});

  const { updateChangeLogGl } = useChangeLogUpdateGl();

  const valueFromPayloadHierarchy = useSelector((state) => state.hierarchyData || {});
  const initialFieldValue =
    module === MODULE?.CC
      ? valueFromPayloadCC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ?? valueFromPayloadCC?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE?.GL
      ? valueFromPayloadGL?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ?? valueFromPayloadGL?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE?.PCG || module === MODULE?.CCG
      ? valueFromPayloadHierarchy?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE.BOM
      ? BOMpayloadData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE.IO
      ? IOpayloadData?.rowsBodyData?.[uniqueId]?.payload?.[field?.jsonName] ?? IOpayloadData?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : valueFromPayloadPC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ?? valueFromPayloadPC?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? "";

  const [localValue, setLocalValue] = useState(initialFieldValue);
  const [isFocused, setIsFocused] = useState(false);
  const [charCount, setCharCount] = useState({});
  const [isClicked, setIsClicked] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    setLocalValue(initialFieldValue);
    setCharCount((prevCount) => ({
      ...prevCount,
      [field?.jsonName]: initialFieldValue?.length || 0,
    }));
  }, [initialFieldValue]);

  const handleInputChange = (e) => {
    const updatedValue = e.target.value
      .replace(/[^a-zA-Z0-9\-&()#,. ]/g, "")
      .replace(/\s{2,}/g, " ")
      .replace(/\s*([-&()#,.])\s*/g, "$1")
      .trimStart();

    setCharCount((prevCount) => ({
      ...prevCount,
      [field?.jsonName]: updatedValue.length,
    }));

    setLocalValue(updatedValue);

    if (module === MODULE?.CC) {
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
    } else if (module === MODULE?.GL) {
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
      {
        requestId &&
          !CHANGE_LOG_STATUSES.includes(requestStatus) &&
          updateChangeLogGl({
            uniqueId: uniqueId || "",
            viewName: props?.field?.viewName,
            plantData: "",
            fieldName: props?.field?.fieldName,
            jsonName: props?.field?.jsonName,
            currentValue: updatedValue,
            requestId: initialPayload?.RequestId,
            childRequestId: requestId,
          });
      }
    } else if (module === MODULE?.BOM) {
      dispatch(
        setBOMpayloadData({
          keyName: field?.jsonName,
          data: updatedValue,
        })
      );
    } else if (module === MODULE?.IO) {
      dispatch(
        updateModuleFieldDataIO({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName,
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
    } else if (module === MODULE?.PCG || module === MODULE?.CCG) {
      dispatch(
        setRequestHeaderPayloadDataPCG({
          keyName: field?.jsonName || "",
          data: updatedValue,
        })
      );
    } else {
      dispatch(
        updateModuleFieldData({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
    }
  };
  return (
    <>
      <Grid item md={2}>
        <Stack>
          <>
            <Typography variant="body2" color="#777" sx={{ whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis", maxWidth: "100%" }} title={field?.fieldName}>
              {field.fieldName}
              {(field.visibility === "Mandatory" || field.visibility === "0") && <span style={{ color: "red" }}>*</span>}
            </Typography>
            <TextField
              size="small"
              type={field.dataType === "QUAN" ? "number" : "text"}
              placeholder={disabled ? "" : `Enter ${field.fieldName}`}
              // error={errorFields?.includes(field?.jsonName)}
              value={localValue}
              title={localValue}
              onBlur={(e) => {
                setIsFocused(false);
              }}
              inputProps={{
                style: { textTransform: "uppercase", fontSize: "12px" },
                maxLength: field.maxLength, // This handles the transformation directly
              }}
              onFocus={() => {
                setIsFocused(true);
              }}
              onClick={() => {
                setIsClicked(true);
              }}
              helperText={isFocused && (charCount[field?.jsonName] === field.maxLength ? `Max Length Reached` : `${charCount[field?.jsonName]}/${field.maxLength}`)}
              FormHelperTextProps={{
                sx: {
                  color: isFocused && charCount[field?.jsonName] === field.maxLength ? "red" : "blue",
                  position: "absolute",
                  bottom: "-20px",
                },
              }}
              sx={{
                "& .MuiInputBase-root.Mui-disabled": {
                  "& > input": {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                  backgroundColor: colors.hover.light,
                },
                "& .MuiInputBase-root": {
                  height: "34px",
                },
                "& .MuiOutlinedInput-root": {
                  "&.Mui-focused fieldset": {
                    borderColor: isFocused && charCount[field?.jsonName] >= field.maxLength ? "red" : "",
                  },
                  "& fieldset": {
                    borderColor: isFocused && charCount[field?.jsonName] >= field.maxLength ? "red" : "",
                  },
                },
              }}
              onChange={handleInputChange}
              disabled={disabled}
              required={field.visibility === "Mandatory" || field.visibility === "0"}
            />
          </>
        </Stack>
      </Grid>
    </>
  );
};

export default InputTypeGlobal;
