import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  validatedRowsStatus: {},
  changeFieldSelectiondata: {},
  payload: {
    requestHeaderData: {},
    rowsHeaderData: [],
    rowsBodyData: {},
  },
  selectedRowId: null,
  costCenterData: {},
  costCenterTabs: [],
  costCenterConfig: {},
  isOpenDialog: true,
  costCenterBasicData: {},
  costCenterAdditionalData: {},
  costCenterCompCodes: {},
  costCenterIndicators: {},
  costCenterAddress: {},
  costCenterCommunication: {},
  costCenterHistory: {},
  costCenterViewData: [],
  singleCCPayload: {},
  singleCCPayloadGI: {},
  costCenterNumber: "",
  // singleETPCPayloadGI:{},
  requiredFields: [],
  errorFields: [],
  EditMultipleCostCenter: {},
  EditMultipleCostCenterData: [],
  MultipleCostCenterData: [],
  MultipleCostCenterRequestBench: [],
  requiredFieldsGI: [],
  buttonsIDM: [],
  dqsAuthToken: "",
  companyCode: [
    {
      id: 1,
      companyCodes: "",
      companyName: "",
      assigned: "",
    },
  ],
  handleMassMode: "",
  selectedHeader: [],
  selectedHeaderTab: "",
  rows: [
    {
      id: Date.now(),
      selectedCompanyCode: "",
      ccNumber: "",
    },
  ],
  validatedRows: [],

  fetchedCostCenterData: [],
  originalCostCenterData: [],
  fetchReqBenchDataCC: [],
  originalReqBenchData: [],
  changedFieldsMap: {},
};

export const costCenterTabSlice = createSlice({
  name: "costCenter",
  initialState,
  reducers: {
    setCCPayload: (state, action) => {
      state.payload = action.payload;
    },
    setCCRows: (state, action) => {
      state.payload.rowsHeaderData = action.payload;
    },
    setRequestHeaderPayloadSingleField: (state, action) => {
      state.payload.requestHeaderData[action.payload.keyName] =
        action.payload.data;
      return state;
    },
    setRequestHeaderPayloadData: (state, action) => {
      state.payload.requestHeaderData = action.payload;
      return state;
    },
    updateModuleFieldDataCC: (state, action) => {
      const { uniqueId, viewID, keyName, data } = action.payload;

      if (uniqueId) {
        if (!state.payload.rowsBodyData[uniqueId]) {
          state.payload.rowsBodyData[uniqueId] = {};
        }
        if (keyName) {
          state.payload.rowsBodyData[uniqueId][keyName] = data?.code
            ? data.code
            : data
            ? data
            : "";
        }
      } else {
        state.payload.requestHeaderData[action.payload.keyName] = action
          ?.payload?.data?.code
          ? action?.payload?.data?.code
          : action?.payload?.data
          ? action?.payload?.data
          : "";
      }

      return state;
    },
    resetPayloadData: (state) => {
      (state.payload = {
        requestHeaderData: {},
        rowsHeaderData: [
          {
            id: "first_request_id_requestor",
            controllingArea: "",
            costCenterNumber: "",
            longDescription: "",
            businessSegment: "",
            companyCode: "",
            included: true,
          },
        ],
        rowsBodyData: {},
      }),
        (state.costCenterTabs = []);
    },
    setRows: (state, action) => {
      state.rows = action.payload;
    },
    setValidatedRows: (state, action) => {
      state.validatedRows = action.payload; // Save validated row IDs
    },
    setCostCenterBasicDataTab: (state, action) => {
      state.costCenterBasicData = action.payload;
    },
    setAdditionalDataTab: (state, action) => {
      state.costCenterAdditionalData = action.payload;
    },
    setCostCenterCompCodesTab: (state, action) => {
      state.costCenterCompCodes = action.payload;
    },
    setCostCenterIndicatorsTab: (state, action) => {
      state.costCenterIndicators = action.payload;
    },
    setCostCenterAddressTab: (state, action) => {
      state.costCenterAddress = action.payload;
    },
    setCostCenterCommunicationTab: (state, action) => {
      state.costCenterCommunication = action.payload;
    },
    setCostCenterHistoryTab: (state, action) => {
      state.costCenterHistory = action.payload;
    },
    setCostCenterViewData: (state, action) => {
      state.costCenterViewData = action.payload;
    },
    setSingleCostCenterPayload: (state, action) => {
      state.singleCCPayload[action.payload.keyName] = action.payload.data;
      return state;
    },
    setSingleCostCenterPayloadGI: (state, action) => {
      state.singleCCPayloadGI[action.payload.keyName] = action.payload.data;
      return state;
    },
    setSelectedHeader(state, action) {
      state.selectedHeader = action.payload;
      return state;
    },
    setSelectedHeaderTab(state, action) {
      state.selectedHeaderTab = action.payload;
      return state;
    },
    clearCostCenterPayload: (state) => {
      state.singleCCPayload = {};
    },
    clearCostCenterSunocoPayloadGI: (state) => {
      state.singleCCPayloadGI = {};
    },
    clearSingleCostCenter: (state, action) => {
      state.requiredFields = [];
      state.singleCCPayload = {};
      state.costCenterViewData = [];
      state.requiredFieldsGI = [];
    },
    setCCRequiredFieldsGI: (state, action) => {
      if (
        state.requiredFieldsGI.findIndex((item) => item == action.payload) == -1
      ) {
        state.requiredFieldsGI.push(action.payload);
      }
      return state;
    },
    setCCRequiredFields: (state, action) => {
      if (
        state.requiredFields.findIndex((item) => item == action.payload) == -1
      ) {
        state.requiredFields.push(action.payload);
      }
      return state;
    },
    setCCErrorFields: (state, action) => {
      state.errorFields = action.payload;
      return state;
    },
    setCompanyCode: (state, action) => {
      state.companyCode = action.payload;
      return state;
    },
    setButtonsIDM: (state, action) => {
      state.buttonsIDM = action.payload;
      return state;
    },
    setDqsAuthToken: (state, action) => {
      state.dqsAuthToken = action.payload;
      return state;
    },
    setEditCostCenter(state, action) {
      state.EditMultipleCostCenter = {
        ...state.EditMultipleCostCenter,
        ...action.payload,
      };
    },
    setMultipleCostCenterData(state, action) {
      state.MultipleCostCenterData = [
        ...state.MultipleCostCenterData,
        ...action.payload,
      ];
    },
    setMultipleCostCenterRequestBench(state, action) {
      state.MultipleCostCenterRequestBench = action.payload;
      return state;
    },
    setHandleMassMode(state, action) {
      state.handleMassMode = action.payload;
    },
    setCostCenterNumber(state, action) {
      state.costCenterNumber = action.payload;
      return state;
    },
    clearCostCenter: (state) => {
      state.singleCCPayload = {};
      state.errorFields = [];
      state.requiredFields = [];
      state.requiredFieldsGI = [];
      state.EditMultipleCostCenter = {};
      state.companyCode = initialState.companyCode;
    },
    clearMultipleCostCenterData: (state, action) => {
      state.MultipleCostCenterData = [];
    },
    setCostCenterData: (state, action) => {
      state.costCenterData = action.payload;
    },
    setCostCenterTabs: (state, action) => {
      // Ensure state.costCenterTabs is an array
      if (!Array.isArray(state.costCenterTabs)) {
        state.costCenterTabs = [];
      }

      // Merge new tabs without replacing old ones
      action.payload.forEach(({ tab, data }) => {
        const existingTabIndex = state.costCenterTabs.findIndex(
          (t) => t.tab === tab
        );

        if (existingTabIndex !== -1) {
          // Update existing tab data
          state.costCenterTabs[existingTabIndex].data = data;
        } else {
          // Add new tab
          state.costCenterTabs.push({ tab, data });
        }
      });
    },

    setCostCenterConfig: (state, action) => {
      state.costCenterConfig = action.payload;
    },
    clearCostCenterData: (state) => {
      state.costCenterData = {};
      state.costCenterTabs = [];
      state.costCenterConfig = {};
    },

    // change reducers
    setFetchedCostCenterDataCc: (state, action) => {
      state.fetchedCostCenterData = action.payload;
    },
    setOriginalCostCenterDataCc: (state, action) => {
      state.originalCostCenterData = action.payload;
    },
    setFetchReqBenchDataCc: (state, action) => {
      state.fetchReqBenchDataCC = action.payload;
    },
    setOriginalReqBenchDataCc: (state, action) => {
      state.originalReqBenchData = action.payload;
    },
    updateCostCenterRowCc: (state, action) => {
      const updatedRow = action.payload;
      state.fetchedCostCenterData = state.fetchedCostCenterData.map((row) =>
        row.id === updatedRow.id ? updatedRow : row
      );

      const originalRow = state.originalCostCenterData.find(
        (row) => row.id === updatedRow.id
      );

      const changedFields = {};
      if (originalRow) {
        Object.keys(updatedRow).forEach((key) => {
          if (updatedRow[key] !== originalRow[key]) {
            changedFields[key] = true;
          }
        });
      }

      state.changedFieldsMap[updatedRow.id] = changedFields;
    },
    updateReqBenchRowCc: (state, action) => {
      const updatedRow = action.payload;
      state.fetchReqBenchDataCC = state.fetchReqBenchDataCC.map((row) =>
        row.id === updatedRow.id ? updatedRow : row
      );

      const originalRow = state.originalReqBenchData.find(
        (row) => row.id === updatedRow.id
      );

      const changedFields = {};
      if (originalRow) {
        Object.keys(updatedRow).forEach((key) => {
          if (updatedRow[key] !== originalRow[key]) {
            changedFields[key] = true;
          }
        });
      }

      state.changedFieldsMap[updatedRow.id] = changedFields;
    },
    setChangedFieldsMapCc: (state, action) => {
      state.changedFieldsMap = action.payload;
    },
    setSelectedRowIdCC: (state, action) => {
      state.selectedRowId = action.payload;
    },
    resetCostCenterStateCc: () => initialState,
    setValidatedStatus: (state, action) => {
      const { rowId, status } = action.payload;
      state.validatedRowsStatus[rowId] = status;
    },
    resetValidationStatus: (state) => {
      state.validatedRowsStatus = {};
    },
    setChangeFieldSelectionData: (state, action) => {
      state.changeFieldSelectiondata = action.payload;
    },
    setOpenDialog: (state, action) => {
      state.isOpenDialog = action.payload;
    },
    setCostCenterRows: (state, action) => {
      state.payload.rowsHeaderData = action.payload;
    },
    setCostCenterTab: (state, action) => {
      state.payload.rowsBodyData = {
        ...state.payload.rowsBodyData,
        ...action.payload, // merge new tab data by rowId
      };
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setCCPayload,
  resetPayloadData,
  setCCRows,
  setRequestHeaderPayloadSingleField,
  setRequestHeaderPayloadData,
  updateModuleFieldDataCC,
  setRows,
  setValidatedRows,
  setCostCenterBasicDataTab,
  setAdditionalDataTab,
  setCostCenterCompCodesTab,
  setCostCenterIndicatorsTab,
  setCostCenterAddressTab,
  setCostCenterCommunicationTab,
  setCostCenterHistoryTab,
  setSingleCostCenterPayload,
  clearCostCenterPayload,
  setCCRequiredFields,
  setCCErrorFields,
  setCompanyCode,
  setMultipleCostCenterData,
  setMultipleCostCenterRequestBench,
  setHandleMassMode,
  clearCostCenter,
  setCostCenterViewData,
  setCCRequiredFieldsGI,
  setSingleCostCenterPayloadGI,
  setButtonsIDM,
  setDqsAuthToken,
  setSelectedHeader,
  clearMultipleCostCenterData,
  setSelectedHeaderTab,
  clearCostCenterSunocoPayloadGI,
  clearSingleCostCenter,
  setCostCenterNumber,
  setCostCenterData,
  setCostCenterTabs,
  setCostCenterConfig,
  clearCostCenterData,

  setFetchedCostCenterDataCc,
  setOriginalCostCenterDataCc,
  setFetchReqBenchDataCc,
  setOriginalReqBenchDataCc,
  updateCostCenterRowCc,
  updateReqBenchRowCc,
  setChangedFieldsMapCc,
  resetCostCenterStateCc,

  setCostCenterControlTab,
  setCostCenterTemplatesTab,
  clearRequiredFields,
  setSyndicationType,
  setWholeData,
  updateCCPCMassChangeStore,
  setControllingArea,
  setSelectedRowIdCC,

  setValidatedStatus,
  resetValidationStatus,
  setChangeFieldSelectionData,
  setOpenDialog,
  setCostCenterRows,
  setCostCenterTab,
} = costCenterTabSlice.actions;

export const costCenterReducer = costCenterTabSlice.reducer;
