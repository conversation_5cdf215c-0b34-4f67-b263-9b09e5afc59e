import { useCallback } from "react";
import { useSelector } from "react-redux";
import { useSnackbar } from "@hooks/useSnackbar";
import { SUCCESS_MESSAGES, ERROR_MESSAGES } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import { destination_BOM } from "../../../destinationVariables";
import { createBOMPayload } from "../../../functions";
import { doAjax } from "@components/Common/fetchService";

const BOM_ACTION_MAPPING = {
  SAVE_AS_DRAFT: {
    endpoint: `/${destination_BOM}${END_POINTS.MASS_ACTION.CREATE_BOM_SAVE_AS_DRAFT}`,
    successMessage: SUCCESS_MESSAGES.BOM_SAVED_AS_DRAFT,
    errorMessage: ERROR_MESSAGES.BOM_SAVE_AS_DRAFT_ERROR,
  },
  SUBMIT_FOR_REVIEW: {
    endpoint: `/${destination_BOM}${<PERSON>ND_POINTS.MASS_ACTION.CREATE_BOM_SUBMIT_FOR_REVIEW}`,
    successMessage: SUCCESS_MESSAGES.BOM_SUBMITTED_FOR_REVIEW,
    errorMessage: ERROR_MESSAGES.BOM_SUBMIT_FOR_REVIEW_ERROR,
  },
  APPROVE: {
    endpoint: `/${destination_BOM}${END_POINTS.MASS_ACTION.CREATE_BOM_SUBMIT_FOR_APPROVE}`,
    successMessage: SUCCESS_MESSAGES.BOM_SUBMITTED_FOR_APPROVE,
    errorMessage: ERROR_MESSAGES.BOM_SUBMIT_FOR_APPROVE_ERROR,
  },
  VALIDATE: {
    endpoint: `/${destination_BOM}${END_POINTS.MASS_ACTION.VALIDATE_BOM}`,
    successMessage: SUCCESS_MESSAGES.BOM_VALIDATED,
    errorMessage: ERROR_MESSAGES.BOM_VALIDATE_ERROR,
  },
};

export default function useBomBottomButtons() {
  const bomRows = useSelector((state) => state.bom.bomRows);
  const tabFieldValues = useSelector((state) => state.bom.tabFieldValues);
  const payloadFields = useSelector((state) => state.bom.BOMpayloadData);
  const createdRequestId = useSelector((state) => state.bom.requestHeaderID);
  const taskData = useSelector((state) => state.userManagement.taskData);
  const { showSnackbar } = useSnackbar();

  const handleBottomButton = useCallback(
    (type) => {
      const payload = createBOMPayload(
        bomRows,
        tabFieldValues,
        payloadFields,
        createdRequestId,
        taskData
      );
      const actionConfig = BOM_ACTION_MAPPING[type];
      if (!actionConfig) {
        showSnackbar("Invalid action type", "error");
        return;
      }
      doAjax(
        actionConfig.endpoint,
        "post",
        (data) => {
          showSnackbar(actionConfig.successMessage, "success");
        },
        (err) => {
          showSnackbar(actionConfig.errorMessage, "error");
        },
        payload
      );
    },
    [bomRows, tabFieldValues, payloadFields, createdRequestId, taskData, showSnackbar]
  );

  return { handleBottomButton };
} 