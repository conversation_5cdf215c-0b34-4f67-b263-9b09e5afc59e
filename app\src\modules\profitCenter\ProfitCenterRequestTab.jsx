import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import RequestHeaderPC from "./RequestHeaderPC";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import ArrowCircleLeftOutlined from "@mui/icons-material/ArrowCircleLeftOutlined";
import RequestDetailsPC from "./RequestDetailsPC";
import { setActiveStep } from "@app/redux/stepperSlice";
import RequestDetailsChangePC from "./RequestDetailsChangePC";
import { setRequestHeader } from "@app/requestDataSlice";
import {
  DESTINATION_FIN,
  DIALOUGE_BOX_MESSAGES,
  REQUEST_STATUS,
  REQUEST_TYPE,
} from "@constant/enum";
import {
  button_Outlined,
  button_Primary,
} from "../../components/Common/commonStyles.jsx";
import {
  destination_IDM,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import {
  transformApiResponseToReduxPayloadPc,
  fetchRegionBasedOnCountry,
  getProfitCenterGrp,
} from "../../functions";

import {
  Step,
  StepButton,
  Stepper,
  IconButton,
  DialogContent,
  DialogActions,
  Button,
} from "@mui/material";
import { Box, Grid, Typography } from "@mui/material";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import { APP_END_POINTS } from "@constant/appEndPoints";
import {
  resetPayloadData,
  setPCPayload,
  setRequestHeaderPayloadData,
  resetValidationStatus,
  resetProfitCenterStatePc,
} from "@app/profitCenterTabsSlice";
import AttachmentsCommentsTab from "../../components/RequestBench/RequestPages/AttachmentsCommentsTab";
import { setOdataApiCall } from "@profitCenter/slice/profitCenterDropdownSlice";
import { idGenerator } from "../../functions";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { WarningOutlined } from "@mui/icons-material";
import { colors } from "@constant/colors";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import PreviewPage from "../../components/RequestBench/PreviewPage.jsx";
import { LOCAL_STORAGE_KEYS, MODULE_MAP } from "../../constant/enum";
import useLang from "@hooks/useLang";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import useDropdownFMDData from "../modulesHooks/useDropdownFMDData.js";
import { setLocalStorage,clearLocalStorageItem } from "@helper/helper.js";
import { setDropDown } from "@profitCenter/slice/profitCenterDropdownSlice";
import useProfitCenterChangeFieldConfig from "@hooks/useProfitCenterChangeFieldConfig";
import { END_POINTS } from "@constant/apiEndPoints";
import ExcelOperations from "@components/Common/ExcelOperationsCard";
import ChangeLogGL from "@components/Changelog/ChangeLogGL";
import { setCreatePayloadCopyForChangeLog } from "@app/changeLogReducer";

const steps = [
  "Request Header",
  "Profit Center List",
  "Attachments & Comments",
  "Preview",
];

const ProfitCenterRequestTab = () => {
  const { t } = useLang();
  const tabValue = useSelector((state) => state.CommonStepper.activeStep);
  const { getChangeTemplate } = useProfitCenterChangeFieldConfig(MODULE_MAP.PC);

  const requestHeaderData = useSelector(
    (state) => state.profitCenter.payload.requestHeaderData
  );
  const requestIdHeader = useSelector(
    (state) => state.request.requestHeader?.requestId
  );
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );

  const reduxPayload = useSelector((state) => state.profitCenter.payload);
  const templateName = reduxPayload?.requestHeaderData?.TemplateName;

  const dispatch = useDispatch();
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [addHardCodeData, setAddHardCodeData] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const [apiResponses, setApiResponses] = useState([]);
  const [completed, setCompleted] = useState([false, false, false]);
  const navigate = useNavigate();
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [isDialogVisible, setisDialogVisible] = useState(false);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [pcNumber, setPcNumber] = useState("");
  const [isChangeLogopen, setisChangeLogopen] = useState(false);

  const onlyDigits = (val) => String(val || "").replace(/\D/g, "");
  const isProfitCenterApiCalled = useSelector(
    (state) => state.profitCenterDropdownData?.isOdataApiCalled
  );
  const { fetchAllDropdownFMD } = useDropdownFMDData(
    destination_ProfitCenter_Mass,
    setDropDown
  );
  const handleTabChange = (index) => {
    dispatch(setActiveStep(index));
  };
  const templateFullData = useSelector(
    (state) => state?.payload?.changeFieldSelectiondata || []
  );
  const location = useLocation();
  const module = location?.state?.moduleName;

  const rowData = location.state;

  const queryParams = new URLSearchParams(location.search);
  const reqBench = queryParams.get("reqBench");
  const requestId = queryParams.get("RequestId");
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");
  const handleDownload = () => {
    setDownloadClicked(true);
  };

  const openChangeLog = () => {
    setisChangeLogopen(true);
  };

  const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };

  const handleUploadPC = (file) => {
    let url = "";
    url = "getAllProfitCenterFromExcel";
    setLoaderMessage("Initiating Excel Upload");
    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append(
      "dtName",
      RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
        RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? "MDG_PC_FIELD_CONFIG"
        : "MDG_CHANGE_TEMPLATE_DT"
    );
    formData.append(
      "version",
      RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
        RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD
        ? "v2"
        : "v6"
    );
    formData.append(
      "requestId",
      onlyDigits(requestId) ? onlyDigits(requestId) : ""
    );
    formData.append("IsSunoco", "false");
    formData.append("screenName", RequestType ? RequestType : "");

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      } else {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/${url}`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };

  useEffect(() => {
    if (!isProfitCenterApiCalled) {
      fetchAllDropdownFMD("profitCenter");
      setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.PC);
      dispatch(setOdataApiCall(true));
    }
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE,MODULE_MAP.PC)
    getAttachmentsIDM();
    setPcNumber(idGenerator("PC"));
    return () => {
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.MODULE)
    }
  }, []);

  const getAttachmentsIDM = () => {
    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": MODULE_MAP.PC,
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": REQUEST_TYPE.CREATE,
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1, // ensure backend expects number not string
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data?.statusCode === 200) {
        const attachmentList =
          data?.data?.result?.[0]?.MDG_ATTACHMENTS_ACTION_TYPE ?? [];

        // Optional: If you need to transform for display
        const templateData = attachmentList.map((element, index) => ({
          id: index,
          attachmentName: element?.MDG_ATTACHMENTS_NAME,
          changeEntryFields: element?.MDG_ATTACH_CHNG_ENT_FIELDS,
        }));
        setAttachmentsData(attachmentList);
      } else {
        console.warn("Unexpected statusCode:", data?.statusCode);
      }
    };

    const hError = (error) => {
      console.error("Attachment fetch error:", error);
    };

    const endpoint =
      applicationConfig.environment === "localhost"
        ? END_POINTS.INVOKE_RULES.LOCAL
        : END_POINTS.INVOKE_RULES.PROD;

    doAjax(`/${destination_IDM}${endpoint}`, "post", hSuccess, hError, payload);
  };

  const getDisplayDataPC = (requestId) => {
    const isChildPresent = rowData?.childRequestIds !== "Not Available";
    if (reqBench === "true") {
      const payload = {
        sort: "id,asc",
        parentId: !isChildPresent ? onlyDigits(requestId) : "",
        massCreationId:
          isChildPresent &&
          (RequestType === REQUEST_TYPE.CREATE ||
            RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD)
            ? onlyDigits(requestId)
            : "",
        massChangeId:
          isChildPresent &&
          (RequestType === REQUEST_TYPE.CHANGE ||
            RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD)
            ? onlyDigits(requestId)
            : "",
        page: 0,
        size: 10,
      };

      const hSuccess = (response) => {
        const apiResponse = response?.body || [];

        //new added For header Data Constant
        let requestHeaderData = response?.body[0]?.Torequestheaderdata;
        let TotalIntermediateTasks = response?.body[0]?.TotalIntermediateTasks;

        dispatch(
          setRequestHeaderPayloadData({
            RequestId: requestHeaderData.RequestId,
            RequestPrefix: requestHeaderData.RequestPrefix,
            ReqCreatedBy: requestHeaderData.ReqCreatedBy,
            ReqCreatedOn: requestHeaderData.ReqCreatedOn,
            ReqUpdatedOn: requestHeaderData.ReqUpdatedOn,
            RequestType: requestHeaderData.RequestType,
            RequestDesc: requestHeaderData.RequestDesc,
            RequestStatus: requestHeaderData.RequestStatus,
            RequestPriority: requestHeaderData.RequestPriority,
            FieldName: requestHeaderData.FieldName,
            TemplateName: requestHeaderData.TemplateName,
            Division: requestHeaderData.Division,
            region: requestHeaderData.region,
            leadingCat: requestHeaderData.leadingCat,
            firstProd: requestHeaderData.firstProd,
            launchDate: requestHeaderData.launchDate,
            isBifurcated: requestHeaderData.isBifurcated,
            screenName: requestHeaderData.screenName,
            TotalIntermediateTasks: TotalIntermediateTasks,
          })
        );
        setApiResponses(apiResponse);
        const reqType = requestHeaderData?.RequestType;
        if (
          reqType === REQUEST_TYPE.CHANGE ||
          reqType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
        ) {
          getChangeTemplate();
        }
        const transformedPayload =
          transformApiResponseToReduxPayloadPc(apiResponse);

        apiResponse.forEach((item) => {
          if (item.ProfitCenterID) {
            fetchRegionBasedOnCountry(
              item.Country,
              dispatch,
              item.ProfitCenterID
            );
            getProfitCenterGrp(item.COArea, dispatch, item.ProfitCenterID);
          }

          if (item?.Torequestheaderdata?.TemplateName) {
            let selectedTemplate = item?.Torequestheaderdata?.TemplateName;
            const filteredAndSortedFields = templateFullData
              .filter(
                (item) =>
                  item?.MDG_CHANGE_TEMPLATE_NAME === selectedTemplate &&
                  item?.MDG_MAT_CHANGE_TYPE === "Item" &&
                  item?.MDG_MAT_FIELD_VISIBILITY !== "Hidden" &&
                  item?.MDG_MAT_FIELD_VISIBILITY !== "Display"
              )
              .sort((a, b) => {
                // Convert to number for proper sorting, handle nulls/fallbacks
                const seqA = Number(a?.MDG_MAT_FIELD_SEQUENCE) || 0;
                const seqB = Number(b?.MDG_MAT_FIELD_SEQUENCE) || 0;
                return seqA - seqB;
              });
            const uniqueFieldNames = [
              ...new Set(
                filteredAndSortedFields
                  .map((item) => item?.MDG_MAT_FIELD_NAME)
                  .filter(Boolean)
              ),
            ].map((field) => ({ code: field }));
            // return uniqueFieldNames;
            //   const changeFieldNames = uniqueFieldNames.map((item) => ({
            //   code: item?.fieldName,
            //   desc: "",
            // }));
            dispatch(
              setDropDown({
                keyName: "FieldName",
                data: uniqueFieldNames || [],
                // keyName2: uniqueId,
              })
            );
          }
        });
        dispatch(setPCPayload(transformedPayload?.payload));
        dispatch(setCreatePayloadCopyForChangeLog(transformedPayload?.payload));
        // getCompanyCode(response?.body[0]?.ControllingArea, "ETP")
      };

      const hError = (error) => {
        console.error("Error fetching PC Create data:", error);
      };

      doAjax(
        `/${destination_ProfitCenter_Mass}/data/displayMassProfitCenterDto`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted((prev) => {
        const updated = [...prev];
        updated[0] = true; // ✅ mark step 1 as completed
        return updated;
      });
    }
  }, [isSecondTabEnabled]);

  useEffect(() => {
    if (isAttachmentTabEnabled) {
      setCompleted((prev) => {
        const updated = [...prev];
        updated[1] = true; // ✅ mark step 2 as completed
        return updated;
      });
    }
  }, [isAttachmentTabEnabled]);

  useEffect(() => {
    const loadData = async () => {
      if (RequestId) {
        await getDisplayDataPC(RequestId);
        if (
          ((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD &&
            !rowData?.length) ||
            RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
            RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) &&
          (rowData?.reqStatus === REQUEST_STATUS.DRAFT ||
            rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)
        ) {
          dispatch(setActiveStep(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(false);
        } else {
          dispatch(setActiveStep(1));
          setIsSecondTabEnabled(true);
          setIsAttachmentTabEnabled(true);
        }

        setAddHardCodeData(true);
      } else {
        dispatch(setActiveStep(0));
      }
    };

    loadData();
    return () => {
      dispatch(resetPayloadData());
      dispatch(setRequestHeader({}));
      dispatch(resetValidationStatus());
      dispatch(resetProfitCenterStatePc());
    };
  }, [requestId, dispatch]);

  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    } else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    } else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.MASTER_DATA_PC);
    }
  };

  const handleCancel = () => {
    setisDialogVisible(false);
  };

  return (
    <div>
      <Box sx={{ padding: 2 }}>
        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {requestId || requestIdHeader ? (
            <Box>
              <Typography
                variant="h6"
                sx={{
                  mb: 1,
                  textAlign: "left",
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                }}
              >
                <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
                {t("Request Header ID:")}{" "}
                <span>
                  {requestIdHeader
                    ? requestHeaderSlice?.requestPrefix +
                      "" +
                      requestHeaderSlice?.requestId
                    : `${requestId}`}
                </span>
              </Typography>

              {templateName && (
                <Typography
                  variant="h6"
                  sx={{
                    mb: 1,
                    textAlign: "left",
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <FeedOutlinedIcon sx={{ fontSize: "1.5rem" }} />
                  Template Name: <span>{templateName}</span>
                </Typography>
              )}
            </Box>
          ) : (
            <div style={{ flex: 1 }} />
          )}

          {isChangeLogopen && (
            <ChangeLogGL
              module={MODULE_MAP.PC}
              open={true}
              closeModal={handleClosemodalData}
              requestId={requestHeaderData?.RequestId || requestId}
              requestType={"create"}
            />
          )}
          {tabValue === 1 && (
            <Box
              sx={{ display: "flex", justifyContent: "flex-end", gap: "1rem" }}
            >
              <Button
                variant="outlined"
                size="small"
                title="Download Error Report"
                disabled={!RequestId}
                onClick={() => {
                  navigate(
                    `/requestBench/errorHistory?RequestId=${
                      RequestId ? RequestId : ""
                    }`,
                    { state: { display: true } }
                  );
                }}
                color="primary"
              >
                <SummarizeOutlinedIcon sx={{ padding: "2px" }} />
              </Button>

              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                onClick={openChangeLog}
                title="Change Log"
              >
                <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
              </Button>

              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                title="Export Excel"
              >
                <FileUploadOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
            </Box>
          )}
        </Grid>
        <IconButton
          onClick={() => {
            if (reqBench === "true") {
              navigate(APP_END_POINTS?.REQUEST_BENCH);
              return;
            }
            setisDialogVisible(true);
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{ left: "-10px" }}
          title="Back"
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>

        <Stepper
          nonLinear
          activeStep={tabValue}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "-35px 14% 10px",
            marginTop: "-35px",
          }}
        >
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton
                color="error"
                disabled={
                  (index === 1 && !isSecondTabEnabled) ||
                  (index === 2 && !isAttachmentTabEnabled)
                  // (index === 3 && !isAttachmentTabEnabled)
                }
                onClick={() => handleTabChange(index)}
                sx={{ fontSize: "50px", fontWeight: "bold" }}
              >
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>
                  {label}
                </span>
              </StepButton>
            </Step>
          ))}
        </Stepper>

        {tabValue === 0 && (
          <>
            <RequestHeaderPC
              apiResponse={apiResponses}
              reqBench={reqBench}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
              setIsSecondTabEnabled={setIsSecondTabEnabled}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
            />
            {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD ||
              RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ||
              RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) &&
              ((rowData?.reqStatus == REQUEST_STATUS.DRAFT &&
                !rowData?.material?.length) ||
                rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
                <ExcelOperations
                  handleDownload={handleDownload}
                  setEnableDocumentUpload={setEnableDocumentUpload}
                  enableDocumentUpload={enableDocumentUpload}
                  handleUploadMaterial={handleUploadPC}
                />
              )}
          </>
        )}
        {tabValue === 1 &&
          requestHeaderData.RequestType &&
          (requestHeaderData.RequestType === "Change" ||
          requestHeaderData.RequestType === "Change with Upload" ? (
            <RequestDetailsChangePC
              reqBench={reqBench}
              requestId={requestId}
              apiResponses={apiResponses}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
              setCompleted={setCompleted}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
              template={templateName}
            />
          ) : (
            <RequestDetailsPC
              reqBench={reqBench}
              apiResponses={apiResponses}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} // ✅ pass setter
              setCompleted={setCompleted}
            />
          ))}
        {tabValue === 2 && (
          <AttachmentsCommentsTab
            requestStatus={
              rowData?.reqStatus
                ? rowData?.reqStatus
                : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME
            }
            attachmentsData={attachmentsData}
            requestIdHeader={
              requestIdHeader
                ? requestHeaderSlice?.requestPrefix +
                  "" +
                  requestHeaderSlice?.requestId
                : requestId
            }
            pcNumber={pcNumber}
          />
        )}
        {tabValue === 3 && (
          <Box
            sx={{
              width: "100%",
              overflow: "auto",
            }}
          >
            <PreviewPage module={MODULE_MAP?.PC} />
          </Box>
        )}
      </Box>
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
      {isDialogVisible && (
        <CustomDialog
          isOpen={isDialogVisible}
          titleIcon={
            <WarningOutlined
              size="small"
              sx={{ color: colors?.secondary?.amber, fontSize: "20px" }}
            />
          }
          Title={"Warning"}
          handleClose={handleCancel}
        >
          <DialogContent sx={{ mt: 2 }}>
            {DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE}
          </DialogContent>
          <DialogActions>
            <Button
              variant="outlined"
              size="small"
              sx={{ ...button_Outlined }}
              onClick={handleCancel}
            >
              {t("No")}
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              onClick={handleYes}
            >
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </div>
  );
};

export default ProfitCenterRequestTab;
